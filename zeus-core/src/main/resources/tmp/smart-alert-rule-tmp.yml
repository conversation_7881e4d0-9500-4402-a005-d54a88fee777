# This config is for $rule
name: ruleName
project: vip-xuanwu
refer_duration: 32d
keep_firing_for: 5m
time_offset: 10m
time_shift: 2m
interval: 1m
baseline_judge_wnd: 10

#hubble_group: boss
#hubble_users:
#  - ''

notice_configs:
  - name: 'feishu'
    users:
      - ''

tsdb_configs:
  - name: vip-eagle
    url: http://mimir-api.qiyi.domain/prometheus
    headers:
      X-Scope-OrgID: vip-eagle
    type: prometheus

metric_configs:
  - name: name1
    db_source: vip-eagle
    expr: 'expr'
    metric_type: request_count
