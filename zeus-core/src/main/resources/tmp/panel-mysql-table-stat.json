{"id": 1, "title": "$title", "type": "stat", "datasource": {"type": "mysql", "uid": "$datasourceUid"}, "targets": [{"dataset": "$databaseName", "datasource": {"type": "mysql", "uid": "$datasourceUid"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "$rawSql", "refId": "A", "sql": {"columns": [{"type": "function", "parameters": []}], "groupBy": [{"type": "groupBy", "property": {"type": "string"}}], "limit": 50}}], "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "green"}, {"value": 80, "color": "red"}]}, "color": {"mode": "thresholds"}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "options": {"reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "orientation": "auto", "textMode": "auto", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "pluginVersion": "10.1.0-pre"}