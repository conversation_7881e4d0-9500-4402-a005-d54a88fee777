{"id": 1, "title": "", "type": "table", "datasource": {"type": "mysql", "uid": "datasourceUid"}, "targets": [{"dataset": "databaseName", "datasource": {"type": "mysql", "uid": "datasourceUid"}, "editorMode": "code", "format": "table", "hide": false, "rawQuery": true, "rawSql": "sql", "refId": "A", "sql": {"columns": [{"parameters": [], "type": "function"}], "groupBy": [{"property": {"type": "string"}, "type": "groupBy"}], "limit": 50}}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.1.0-pre"}