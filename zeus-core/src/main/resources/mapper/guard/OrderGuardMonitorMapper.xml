<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.OrderGuardMonitorMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="department_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="datasource_type" jdbcType="VARCHAR" property="datasourceType"/>
        <result column="datasource_id" jdbcType="INTEGER" property="datasourceId"/>
        <result column="query_sql" jdbcType="VARCHAR" property="querySql"/>
        <result column="detail_sql" jdbcType="VARCHAR" property="detailSql"/>
        <result column="create_opr" jdbcType="VARCHAR" property="createOpr"/>
        <result column="update_opr" jdbcType="VARCHAR" property="updateOpr"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, category, department_name, datasource_type, datasource_id, query_sql, detail_sql, create_opr,
        update_opr, status, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from order_guard_monitor where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO">
        insert into order_guard_monitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="departmentName != null">
                department_name,
            </if>
            <if test="datasourceType != null">
                datasource_type,
            </if>
            <if test="datasourceId != null">
                datasource_id,
            </if>
            <if test="querySql != null">
                query_sql,
            </if>
            <if test="detailSql != null">
                detail_sql,
            </if>
            <if test="createOpr != null">
                create_opr,
            </if>
            <if test="updateOpr != null">
                update_opr,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="departmentName != null">
                #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="datasourceType != null">
                #{datasourceType,jdbcType=VARCHAR},
            </if>
            <if test="datasourceId != null">
                #{datasourceId,jdbcType=INTEGER},
            </if>
            <if test="querySql != null">
                #{querySql,jdbcType=VARCHAR},
            </if>
            <if test="detailSql != null">
                #{detailSql,jdbcType=VARCHAR},
            </if>
            <if test="createOpr != null">
                #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO">
        update order_guard_monitor
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="departmentName != null">
                department_name = #{departmentName,jdbcType=VARCHAR},
            </if>
            <if test="datasourceType != null">
                datasource_type = #{datasourceType,jdbcType=VARCHAR},
            </if>
            <if test="datasourceId != null">
                datasource_id = #{datasourceId,jdbcType=INTEGER},
            </if>
            <if test="querySql != null">
                query_sql = #{querySql,jdbcType=VARCHAR},
            </if>
            <if test="detailSql != null">
                detail_sql = #{detailSql,jdbcType=VARCHAR},
            </if>
            <if test="createOpr != null">
                create_opr = #{createOpr,jdbcType=VARCHAR},
            </if>
            <if test="updateOpr != null">
                update_opr = #{updateOpr,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from order_guard_monitor
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from order_guard_monitor
        where status = 1
    </select>

</mapper>