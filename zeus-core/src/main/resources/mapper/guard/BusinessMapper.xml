<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.BusinessMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.BusinessPO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="code" property="code" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="department" property="department" jdbcType="VARCHAR"/>
        <result column="department_emails" property="departmentEmails" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, code, name, description, department, department_emails, 
        create_user, update_user, status, create_time, update_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM business
        WHERE id = #{id}
    </select>

    <!-- 根据业务CODE查询 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM business
        WHERE code = #{code}
    </select>

    <!-- 根据业务名称查询 -->
    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM business
        WHERE name = #{name}
    </select>

    <!-- 查询所有有效的业务信息 -->
    <select id="selectAllValid" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM business
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询业务信息 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM business
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR code LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计业务信息总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM business
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR code LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>

    <!-- 插入业务信息 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.BusinessPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO business (
            code, name, description, department, department_emails,
            create_user, update_user, status, create_time, update_time
        ) VALUES (
            #{code}, #{name}, #{description}, #{department}, #{departmentEmails},
            #{createUser}, #{updateUser}, #{status}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新业务信息 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.BusinessPO">
        UPDATE business
        <set>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="department != null">department = #{department},</if>
            <if test="departmentEmails != null">department_emails = #{departmentEmails},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除业务信息（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE business
        SET status = 0, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量插入业务信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO business (
            code, name, description, department, department_emails,
            create_user, update_user, status, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.code}, #{item.name}, #{item.description}, #{item.department}, #{item.departmentEmails},
             #{item.createUser}, #{item.updateUser}, #{item.status}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

</mapper>
