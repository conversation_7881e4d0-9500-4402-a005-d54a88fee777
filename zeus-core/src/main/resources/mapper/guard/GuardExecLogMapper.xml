<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.GuardExecLogMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.GuardExecLogPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="day" property="day" jdbcType="DATE"/>
        <result column="guard_item_id" property="guardItemId" jdbcType="INTEGER"/>
        <result column="exec_result_id" property="execResultId" jdbcType="BIGINT"/>
        <result column="exec_time" property="execTime" jdbcType="TIMESTAMP"/>
        <result column="check_sql" property="checkSql" jdbcType="LONGVARCHAR"/>
        <result column="detail_sql" property="detailSql" jdbcType="LONGVARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="error_message" property="errorMessage" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, day, guard_item_id, exec_result_id, exec_time, check_sql, detail_sql, status, error_message, create_time
    </sql>

    <!-- 根据ID查询执行记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE id = #{id}
    </select>

    <!-- 根据稽核项ID和日期查询执行记录 -->
    <select id="selectByGuardItemIdAndDay" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE guard_item_id = #{guardItemId} AND day = #{day}
        ORDER BY exec_time DESC
    </select>

    <!-- 根据稽核项ID查询执行记录列表 -->
    <select id="selectByGuardItemId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE guard_item_id = #{guardItemId}
        ORDER BY day DESC, exec_time DESC
    </select>

    <!-- 根据日期查询执行记录列表 -->
    <select id="selectByDay" parameterType="java.time.LocalDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE day = #{day}
        ORDER BY exec_time DESC
    </select>

    <!-- 根据执行结果ID查询执行记录 -->
    <select id="selectByExecResultId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE exec_result_id = #{execResultId}
        ORDER BY exec_time DESC
    </select>

    <!-- 根据状态查询执行记录列表 -->
    <select id="selectByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE status = #{status}
        ORDER BY exec_time DESC
    </select>

    <!-- 分页查询执行记录 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE 1=1
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
        <if test="day != null">
            AND day = #{day}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY exec_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计执行记录总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM guard_exec_log
        WHERE 1=1
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
        <if test="day != null">
            AND day = #{day}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 插入执行记录 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardExecLogPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO guard_exec_log (
            day, guard_item_id, exec_result_id, exec_time, check_sql, detail_sql, status, error_message, create_time
        ) VALUES (
            #{day}, #{guardItemId}, #{execResultId}, #{execTime}, #{checkSql}, #{detailSql}, #{status}, #{errorMessage}, #{createTime}
        )
    </insert>

    <!-- 批量插入执行记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO guard_exec_log (
            day, guard_item_id, exec_result_id, exec_time, check_sql, detail_sql, status, error_message, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.day}, #{item.guardItemId}, #{item.execResultId}, #{item.execTime}, #{item.checkSql}, #{item.detailSql}, #{item.status}, #{item.errorMessage}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 更新执行记录 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardExecLogPO">
        UPDATE guard_exec_log
        <set>
            <if test="day != null">day = #{day},</if>
            <if test="guardItemId != null">guard_item_id = #{guardItemId},</if>
            <if test="execResultId != null">exec_result_id = #{execResultId},</if>
            <if test="execTime != null">exec_time = #{execTime},</if>
            <if test="checkSql != null">check_sql = #{checkSql},</if>
            <if test="detailSql != null">detail_sql = #{detailSql},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除执行记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM guard_exec_log WHERE id = #{id}
    </delete>

    <!-- 根据日期删除执行记录 -->
    <delete id="deleteByDay" parameterType="java.time.LocalDate">
        DELETE FROM guard_exec_log WHERE day = #{day}
    </delete>

    <!-- 根据稽核项ID删除执行记录 -->
    <delete id="deleteByGuardItemId" parameterType="java.lang.Integer">
        DELETE FROM guard_exec_log WHERE guard_item_id = #{guardItemId}
    </delete>

    <!-- 查询最新的执行记录 -->
    <select id="selectLatestByGuardItemId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE guard_item_id = #{guardItemId}
        ORDER BY exec_time DESC
        LIMIT 1
    </select>

    <!-- 统计稽核项执行次数 -->
    <select id="countByGuardItemId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM guard_exec_log
        WHERE guard_item_id = #{guardItemId}
    </select>

    <!-- 统计失败执行次数 -->
    <select id="countFailedByGuardItemId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM guard_exec_log
        WHERE guard_item_id = #{guardItemId} AND status = 0
    </select>

    <!-- 查询执行失败的记录 -->
    <select id="selectFailedRecords" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_log
        WHERE status = 0
        ORDER BY exec_time DESC
        LIMIT #{limit}
    </select>

</mapper>
