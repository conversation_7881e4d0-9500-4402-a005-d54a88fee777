<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.GuardBusinessMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.GuardBusinessPO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="guard_item_id" property="guardItemId" jdbcType="INTEGER"/>
        <result column="business_id" property="businessId" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, guard_item_id, business_id, status, create_time, update_time
    </sql>

    <!-- 根据ID查询关联关系 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_business
        WHERE id = #{id}
    </select>

    <!-- 根据稽核项ID查询关联的业务列表 -->
    <select id="selectByGuardItemId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_business
        WHERE guard_item_id = #{guardItemId} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据业务ID查询关联的稽核项列表 -->
    <select id="selectByBusinessId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_business
        WHERE business_id = #{businessId} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据稽核项ID和业务ID查询关联关系 -->
    <select id="selectByGuardItemIdAndBusinessId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_business
        WHERE guard_item_id = #{guardItemId} AND business_id = #{businessId}
    </select>

    <!-- 插入关联关系 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardBusinessPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO guard_business (
            guard_item_id, business_id, status, create_time, update_time
        ) VALUES (
            #{guardItemId}, #{businessId}, #{status}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO guard_business (
            guard_item_id, business_id, status, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.guardItemId}, #{item.businessId}, #{item.status}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 更新关联关系 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardBusinessPO">
        UPDATE guard_business
        <set>
            <if test="guardItemId != null">guard_item_id = #{guardItemId},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除关联关系（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE guard_business
        SET status = 0, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据稽核项ID删除所有关联关系 -->
    <update id="deleteByGuardItemId" parameterType="java.lang.Integer">
        UPDATE guard_business
        SET status = 0, update_time = NOW()
        WHERE guard_item_id = #{guardItemId}
    </update>

    <!-- 根据业务ID删除所有关联关系 -->
    <update id="deleteByBusinessId" parameterType="java.lang.Integer">
        UPDATE guard_business
        SET status = 0, update_time = NOW()
        WHERE business_id = #{businessId}
    </update>

    <!-- 根据稽核项ID和业务ID删除关联关系 -->
    <update id="deleteByGuardItemIdAndBusinessId">
        UPDATE guard_business
        SET status = 0, update_time = NOW()
        WHERE guard_item_id = #{guardItemId} AND business_id = #{businessId}
    </update>

</mapper>
