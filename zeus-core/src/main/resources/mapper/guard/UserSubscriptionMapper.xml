<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.UserSubscriptionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.UserSubscriptionPO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="oa_name" property="oaName" jdbcType="VARCHAR"/>
        <result column="guard_item_id" property="guardItemId" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, oa_name, guard_item_id, status, create_time, update_time
    </sql>

    <!-- 根据ID查询订阅记录 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_subscription
        WHERE id = #{id}
    </select>

    <!-- 根据用户和稽核项ID查询订阅记录 -->
    <select id="selectByOaNameAndGuardId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_subscription
        WHERE oa_name = #{oaName} AND guard_item_id = #{guardItemId}
    </select>

    <!-- 根据用户查询订阅的稽核项列表 -->
    <select id="selectByOaName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_subscription
        WHERE oa_name = #{oaName} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据稽核项ID查询订阅用户列表 -->
    <select id="selectByGuardItemId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_subscription
        WHERE guard_item_id = #{guardItemId} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询订阅记录 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_subscription
        WHERE 1=1
        <if test="oaName != null and oaName != ''">
            AND oa_name = #{oaName}
        </if>
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计订阅记录总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM user_subscription
        WHERE 1=1
        <if test="oaName != null and oaName != ''">
            AND oa_name = #{oaName}
        </if>
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
    </select>

    <!-- 插入订阅记录 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.UserSubscriptionPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_subscription (
            oa_name, guard_item_id, status, create_time, update_time
        ) VALUES (
            #{oaName}, #{guardItemId}, #{status}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入订阅记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_subscription (
            oa_name, guard_item_id, status, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.oaName}, #{item.guardItemId}, #{item.status}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 更新订阅记录 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.UserSubscriptionPO">
        UPDATE user_subscription
        <set>
            <if test="oaName != null">oa_name = #{oaName},</if>
            <if test="guardItemId != null">guard_item_id = #{guardItemId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除订阅记录（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE user_subscription
        SET status = 0, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据用户和稽核项ID删除订阅记录 -->
    <update id="deleteByOaNameAndGuardId">
        UPDATE user_subscription
        SET status = 0, update_time = NOW()
        WHERE oa_name = #{oaName} AND guard_item_id = #{guardItemId}
    </update>

    <!-- 根据稽核项ID删除所有订阅记录 -->
    <update id="deleteByGuardItemId" parameterType="java.lang.Integer">
        UPDATE user_subscription
        SET status = 0, update_time = NOW()
        WHERE guard_item_id = #{guardItemId}
    </update>

    <!-- 检查用户是否已订阅稽核项 -->
    <select id="existsByOaNameAndGuardId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM user_subscription
        WHERE oa_name = #{oaName} AND guard_item_id = #{guardItemId} AND status = 1
    </select>

</mapper>
