<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.GuardDatasourceMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.GuardDatasourcePO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="conn_url" property="connUrl" jdbcType="VARCHAR"/>
        <result column="conn_config" property="connConfig" jdbcType="LONGVARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, type, conn_url, conn_config, description, create_user, update_user, status, create_time, update_time
    </sql>

    <!-- 插入数据源 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardDatasourcePO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO guard_datasource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="type != null">type,</if>
            <if test="connUrl != null">conn_url,</if>
            <if test="connConfig != null">conn_config,</if>
            <if test="description != null">description,</if>
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="type != null">#{type},</if>
            <if test="connUrl != null">#{connUrl},</if>
            <if test="connConfig != null">#{connConfig},</if>
            <if test="description != null">#{description},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!-- 更新数据源 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardDatasourcePO">
        UPDATE guard_datasource
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="type != null">type = #{type},</if>
            <if test="connUrl != null">conn_url = #{connUrl},</if>
            <if test="connConfig != null">conn_config = #{connConfig},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除数据源 -->
    <update id="deleteById" parameterType="java.lang.Integer">
        delete from guard_datasource WHERE id = #{id}
    </update>

    <update id="updateStatus" parameterType="java.lang.Integer">
        UPDATE guard_datasource 
        set status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询数据源 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_datasource
        WHERE id = #{id}
    </select>

    <!-- 根据名称查询数据源 -->
    <select id="getByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_datasource
        WHERE name = #{name}
    </select>

    <!-- 搜索数据源 -->
    <select id="search" parameterType="com.iqiyi.vip.zeus.core.req.guard.GuardDatasourceSearchParam" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_datasource
        WHERE 1=1
        <if test="id != null"> AND id = #{id}</if>
        <if test="partName != null and partName != ''"> AND name like CONCAT('%', #{partName}, '%')</if>
        <if test="type != null and type != ''"> AND type = #{type}</if>
        <if test="partConnUrl != null and partConnUrl != ''"> AND conn_url like CONCAT('%', #{partConnUrl}, '%')</if>
        <if test="status != null"> AND status = #{status}</if>
        <if test="operateUser != null and operateUser != ''">
            AND (create_user = #{operateUser} or update_user = #{operateUser})
        </if>
        ORDER BY update_time DESC
    </select>

</mapper>
