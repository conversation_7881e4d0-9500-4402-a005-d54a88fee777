<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.GuardExecResultMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.GuardExecResultPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="day" property="day" jdbcType="DATE"/>
        <result column="guard_item_id" property="guardItemId" jdbcType="INTEGER"/>
        <result column="first_value" property="firstValue" jdbcType="DECIMAL"/>
        <result column="second_value" property="secondValue" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, day, guard_item_id, first_value, second_value, create_time
    </sql>

    <!-- 根据ID查询执行结果 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_result
        WHERE id = #{id}
    </select>

    <!-- 根据日期和稽核项ID查询执行结果 -->
    <select id="selectByDayAndGuardId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_result
        WHERE day = #{day} AND guard_item_id = #{guardItemId}
    </select>

    <!-- 根据稽核项ID查询执行结果列表 -->
    <select id="selectByGuardItemId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_result
        WHERE guard_item_id = #{guardItemId}
        ORDER BY day DESC
    </select>

    <!-- 根据日期范围查询执行结果列表 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_result
        WHERE 1=1
        <if test="startDate != null">
            AND day >= #{startDate}
        </if>
        <if test="endDate != null">
            AND day &lt;= #{endDate}
        </if>
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
        ORDER BY day DESC, guard_item_id ASC
    </select>

    <!-- 分页查询执行结果 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_result
        WHERE 1=1
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
        <if test="startDate != null">
            AND day >= #{startDate}
        </if>
        <if test="endDate != null">
            AND day &lt;= #{endDate}
        </if>
        ORDER BY day DESC, guard_item_id ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计执行结果总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM guard_exec_result
        WHERE 1=1
        <if test="guardItemId != null">
            AND guard_item_id = #{guardItemId}
        </if>
        <if test="startDate != null">
            AND day >= #{startDate}
        </if>
        <if test="endDate != null">
            AND day &lt;= #{endDate}
        </if>
    </select>

    <!-- 插入执行结果 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardExecResultPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO guard_exec_result (
            day, guard_item_id, first_value, second_value, create_time
        ) VALUES (
            #{day}, #{guardItemId}, #{firstValue}, #{secondValue}, #{createTime}
        )
    </insert>

    <!-- 批量插入执行结果 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO guard_exec_result (
            day, guard_item_id, first_value, second_value, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.day}, #{item.guardItemId}, #{item.firstValue}, #{item.secondValue}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 更新执行结果 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardExecResultPO">
        UPDATE guard_exec_result
        <set>
            <if test="day != null">day = #{day},</if>
            <if test="guardItemId != null">guard_item_id = #{guardItemId},</if>
            <if test="firstValue != null">first_value = #{firstValue},</if>
            <if test="secondValue != null">second_value = #{secondValue},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据日期和稽核项ID更新执行结果 -->
    <update id="updateByDayAndGuardItemId" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardExecResultPO">
        UPDATE guard_exec_result
        <set>
            <if test="firstValue != null">first_value = #{firstValue},</if>
            <if test="secondValue != null">second_value = #{secondValue},</if>
        </set>
        WHERE day = #{day} AND guard_item_id = #{guardItemId}
    </update>

    <!-- 根据ID删除执行结果 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM guard_exec_result WHERE id = #{id}
    </delete>

    <!-- 根据日期删除执行结果 -->
    <delete id="deleteByDay" parameterType="java.time.LocalDate">
        DELETE FROM guard_exec_result WHERE day = #{day}
    </delete>

    <!-- 根据稽核项ID删除执行结果 -->
    <delete id="deleteByGuardItemId" parameterType="java.lang.Integer">
        DELETE FROM guard_exec_result WHERE guard_item_id = #{guardItemId}
    </delete>

    <!-- 查询最新的执行结果 -->
    <select id="selectLatestByGuardItemId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_exec_result
        WHERE guard_item_id = #{guardItemId}
        ORDER BY day DESC
        LIMIT 1
    </select>

    <!-- 统计稽核项执行次数 -->
    <select id="countByGuardItemId" parameterType="java.lang.Integer" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM guard_exec_result
        WHERE guard_item_id = #{guardItemId}
    </select>

</mapper>
