<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.guard.GuardItemMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.guard.GuardItemPO">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="datasource_type" property="datasourceType" jdbcType="VARCHAR"/>
        <result column="datasource_id" property="datasourceId" jdbcType="INTEGER"/>
        <result column="check_sql" property="checkSql" jdbcType="LONGVARCHAR"/>
        <result column="detail_sql" property="detailSql" jdbcType="LONGVARCHAR"/>
        <result column="dimension" property="dimension" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="scope" property="scope" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, datasource_type, datasource_id, check_sql, detail_sql, dimension, 
        description, create_user, update_user, scope, status, create_time, update_time
    </sql>

    <!-- 根据ID查询稽核项 -->
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE id = #{id}
    </select>

    <!-- 根据名称查询稽核项 -->
    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE name = #{name}
    </select>

    <!-- 根据数据源ID查询稽核项列表 -->
    <select id="selectByDatasourceId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE datasource_id = #{datasourceId} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据数据源ID查询稽核项数量 -->
    <select id="countByDatasourceId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM guard_item WHERE datasource_id = #{datasourceId} AND status = 1
    </select>

    <!-- 根据维度查询稽核项列表 -->
    <select id="selectByDimension" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE dimension = #{dimension} AND status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 查询所有有效的稽核项 -->
    <select id="selectAllValid" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询稽核项 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE 1=1
        <if test="datasourceId != null">
            AND datasource_id = #{datasourceId}
        </if>
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计稽核项总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM guard_item
        WHERE 1=1
        <if test="datasourceId != null">
            AND datasource_id = #{datasourceId}
        </if>
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
    </select>

    <!-- 插入稽核项 -->
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardItemPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO guard_item (
            name, datasource_type, datasource_id, check_sql, detail_sql, dimension,
            description, create_user, update_user, scope, status, create_time, update_time
        ) VALUES (
            #{name}, #{datasourceType}, #{datasourceId}, #{checkSql}, #{detailSql}, #{dimension},
            #{description}, #{createUser}, #{updateUser}, #{scope}, #{status}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新稽核项 -->
    <update id="updateById" parameterType="com.iqiyi.vip.zeus.core.po.guard.GuardItemPO">
        UPDATE guard_item
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="datasourceType != null">datasource_type = #{datasourceType},</if>
            <if test="datasourceId != null">datasource_id = #{datasourceId},</if>
            <if test="checkSql != null">check_sql = #{checkSql},</if>
            <if test="detailSql != null">detail_sql = #{detailSql},</if>
            <if test="dimension != null">dimension = #{dimension},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除稽核项（逻辑删除） -->
    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE guard_item
        SET status = 0, update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据业务ID查询稽核项列表 -->
    <select id="selectByBusinessId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT gi.<include refid="Base_Column_List"/>
        FROM guard_item gi
        INNER JOIN guard_business gb ON gi.id = gb.guard_item_id
        WHERE gb.business_id = #{businessId} AND gi.status = 1 AND gb.status = 1
        ORDER BY gi.create_time DESC
    </select>

    <!-- 根据用户订阅查询稽核项列表 -->
    <select id="selectByUserSubscription" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT gi.<include refid="Base_Column_List"/>
        FROM guard_item gi
        INNER JOIN user_subscription us ON gi.id = us.guard_item_id
        WHERE us.oa_name = #{oaName} AND gi.status = 1 AND us.status = 1
        ORDER BY gi.create_time DESC
    </select>

    <!-- 查询需要执行的稽核项列表 -->
    <select id="selectForExecution" parameterType="java.time.LocalDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM guard_item
        WHERE status = 1
        ORDER BY create_time ASC
    </select>

</mapper>
