<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.DatasourceSchemaMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.DatasourceSchemaPO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="datasource_id" jdbcType="INTEGER" property="datasourceId"/>
        <result column="table_team_code" jdbcType="VARCHAR" property="tableTeamCode"/>
        <result column="table" jdbcType="VARCHAR" property="table"/>
        <result column="column" jdbcType="VARCHAR" property="column"/>
        <result column="column_type" jdbcType="VARCHAR" property="columnType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <resultMap id="TableSchemaResultMap" type="com.iqiyi.vip.zeus.core.model.DatasourceTableSchema">
        <id column="table" jdbcType="VARCHAR" property="table"/>
        <collection property="columnList" ofType="com.iqiyi.vip.zeus.core.model.ColumnInfo">
            <id column="column" jdbcType="VARCHAR" property="columnName"/>
            <result column="column_type" jdbcType="VARCHAR" property="columnType"/>
        </collection>
    </resultMap>
    <resultMap id="ColumnResultMap" type="com.iqiyi.vip.zeus.core.model.ColumnInfo">
        <id column="column" jdbcType="VARCHAR" property="columnName"/>
        <result column="column_type" jdbcType="VARCHAR" property="columnType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, datasource_id, table_team_code, `table`, `column`, `column_type`, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from datasource_schema where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByDatasourceId" parameterType="java.lang.Integer">
        delete from datasource_schema where datasource_id = #{datasourceId,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.DatasourceSchemaPO" keyProperty="id" useGeneratedKeys="true">
        insert into datasource_schema
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="datasourceId != null">
                datasource_id,
            </if>
            <if test="tableTeamCode != null">
                table_team_code,
            </if>
            <if test="table != null">
                table,
            </if>
            <if test="column != null">
                column,
            </if>
            <if test="columnType != null">
                column_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="datasourceId != null">
                #{datasourceId,jdbcType=INTEGER},
            </if>
            <if test="tableTeamCode != null">
                #{tableTeamCode,jdbcType=VARCHAR},
            </if>
            <if test="table != null">
                #{table,jdbcType=VARCHAR},
            </if>
            <if test="column != null">
                #{column,jdbcType=VARCHAR},
            </if>
            <if test="columnType != null">
                #{columnType,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        insert into datasource_schema (`datasource_id`, `table_team_code`, `table`, `column`, `column_type`) values
        <foreach collection="list" item="item" separator=",">
            ( #{item.datasourceId,jdbcType=INTEGER},
            #{item.tableTeamCode,jdbcType=VARCHAR},
            #{item.table,jdbcType=VARCHAR},
            #{item.column,jdbcType=VARCHAR},
            #{item.columnType,jdbcType=VARCHAR} )
        </foreach>
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from datasource_schema where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByTableAndColumn" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from datasource_schema
        where datasource_id = #{datasourceId,jdbcType=INTEGER}
        and `table` = #{table,jdbcType=VARCHAR}
        and `column` = #{column,jdbcType=VARCHAR}
        limit 1
    </select>

    <select id="selectByDatasourceId" parameterType="map" resultMap="TableSchemaResultMap">
        select `table`, `column`, `column_type`
        from datasource_schema
        where datasource_id = #{datasourceId,jdbcType=INTEGER}
          and table_team_code = #{teamCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByDatasourceIdAndTable" parameterType="map" resultMap="ColumnResultMap">
        select `column`, `column_type`
        from datasource_schema
        where datasource_id = #{datasourceId,jdbcType=INTEGER}
          and `table` = #{table,jdbcType=VARCHAR}
          and table_team_code = #{teamCode,jdbcType=VARCHAR}
        order by `column`
    </select>

</mapper>