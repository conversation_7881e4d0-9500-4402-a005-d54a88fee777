<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.MetricTemplateMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.MetricTemplatePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="datasource_type" jdbcType="VARCHAR" property="datasourceType"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="metric_type" jdbcType="VARCHAR" property="metricType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, datasource_type, type, metric_type, content, description, create_user, update_user,
        status, create_time, update_time
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from metric_template where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.MetricTemplatePO" keyProperty="id" useGeneratedKeys="true">
        insert into metric_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="datasourceType != null">
                datasource_type,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="metricType != null">
                metric_type,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="datasourceType != null">
                #{datasourceType,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="metricType != null">
                #{metricType,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.zeus.core.po.MetricTemplatePO">
        update metric_template
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="datasourceType != null">
                datasource_type = #{datasourceType,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="metricType != null">
                metric_type = #{metricType,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from metric_template
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from metric_template
        where name = #{name,jdbcType=VARCHAR};
    </select>

    <select id="selectAll" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from metric_template where status = 1 order by id desc
    </select>

</mapper>