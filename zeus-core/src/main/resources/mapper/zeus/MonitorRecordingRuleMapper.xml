<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.MonitorRecordingRuleMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.zeus.MonitorRecordingRulePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="monitor_id" jdbcType="INTEGER" property="monitorId"/>
        <result column="query_id" jdbcType="INTEGER" property="queryId"/>
        <result column="record_name" jdbcType="VARCHAR" property="recordName"/>
    </resultMap>
    <resultMap id="QueryRecordMappingResultMap" type="com.iqiyi.vip.zeus.core.model.QueryRecordMapping">
        <result column="query_id" jdbcType="INTEGER" property="queryId"/>
        <result column="record_name" jdbcType="VARCHAR" property="recordName"/>
    </resultMap>
    <resultMap id="RecordMonitorMappingResultMap" type="com.iqiyi.vip.zeus.core.model.RecordMonitorMapping">
        <id column="record_name" jdbcType="VARCHAR" property="recordName"/>
        <collection property="monitorQueryMappings" ofType="com.iqiyi.vip.zeus.core.model.MonitorQueryMapping" resultMap="MonitorQueryMappingResult"/>
    </resultMap>

    <resultMap id="MonitorQueryMappingResult" type="com.iqiyi.vip.zeus.core.model.MonitorQueryMapping">
        <id property="monitorId" column="monitor_id"/>
        <collection property="queryIds" ofType="Integer" resultMap="QueryIdResultMap"/>
    </resultMap>

    <resultMap id="QueryIdResultMap" type="java.lang.Integer">
        <id column="query_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, monitor_id, query_id, record_name
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from monitor_recording_rule where id = #{id,jdbcType=INTEGER}
    </delete>

    <delete id="deleteByMonitorIdAndQueryIds" parameterType="map">
        delete from monitor_recording_rule
        where monitor_id = #{monitorId,jdbcType=INTEGER}
        <if test="queryIds != null and queryIds.size() > 0">
            and query_id in
            <foreach collection="queryIds" item="queryId" open="(" separator="," close=")">
                #{queryId}
            </foreach>
        </if>
    </delete>

    <delete id="batchDelete" parameterType="list">
        delete from monitor_recording_rule where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchInsert" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        insert into monitor_recording_rule (monitor_id, query_id, record_name) values
        <foreach collection="list" item="item" separator=",">
            (#{item.monitorId,jdbcType=INTEGER}, #{item.queryId,jdbcType=INTEGER}, #{item.recordName,jdbcType=VARCHAR})
        </foreach>
        on duplicate key update record_name = values(record_name)
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from monitor_recording_rule
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByMonitorId" parameterType="java.lang.Integer" resultMap="QueryRecordMappingResultMap">
        select <include refid="Base_Column_List"/>
        from monitor_recording_rule
        where monitor_id = #{monitorId,jdbcType=INTEGER}
    </select>

    <select id="selectByRecordName" parameterType="java.lang.String" resultMap="MonitorQueryMappingResult">
        select <include refid="Base_Column_List"/>
        from monitor_recording_rule
        where record_name = #{recordName,jdbcType=VARCHAR}
    </select>

    <select id="batchSelectByRecordNames" parameterType="list" resultMap="RecordMonitorMappingResultMap">
        select monitor_id, query_id, record_name
        from monitor_recording_rule
        where record_name in
        <foreach collection="list" item="recordName" open="(" separator="," close=")">
            #{recordName,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>