<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.DatasourceMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.zeus.DatasourcePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="team_code" jdbcType="VARCHAR" property="teamCode"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="sensitive_extra_data" jdbcType="VARCHAR" property="sensitiveExtraData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, type, team_code, url, create_user, update_user, status,
        extra_data, sensitive_extra_data, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from datasource where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.zeus.DatasourcePO" keyProperty="id" useGeneratedKeys="true">
        insert into datasource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="teamCode != null">
                team_code,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="extraData != null">
                extra_data,
            </if>
            <if test="sensitiveExtraData != null">
                sensitive_extra_data,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                #{extraData,jdbcType=VARCHAR},
            </if>
            <if test="sensitiveExtraData != null">
                #{sensitiveExtraData,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="com.iqiyi.vip.zeus.core.po.zeus.DatasourcePO">
        update datasource
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                team_code = #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                extra_data = #{extraData,jdbcType=VARCHAR},
            </if>
            <if test="sensitiveExtraData != null">
                sensitive_extra_data = #{sensitiveExtraData,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from datasource where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="batchSelectByPrimaryKey" parameterType="list" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from datasource
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from datasource where status = 1 and name = #{name,jdbcType=VARCHAR}
    </select>

    <select id="search" parameterType="map" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from datasource
        where status = 1
        <if test="teamCode != null">
            and (team_code = #{teamCode,jdbcType=VARCHAR} or team_code = '573')
        </if>
        <if test="type != null">
            and type = #{type,jdbcType=VARCHAR}
        </if>
        <if test="name != null">
            and name like CONCAT('%',#{name},'%')
        </if>
        order by id desc
    </select>

    <select id="listDatasourceByType" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from datasource
        where status = 1 and type = #{type,jdbcType=VARCHAR}
        order by id desc
    </select>

</mapper>