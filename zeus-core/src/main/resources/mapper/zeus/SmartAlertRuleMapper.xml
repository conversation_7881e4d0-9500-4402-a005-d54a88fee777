<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.SmartAlertRuleMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.SmartAlertRulePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="monitor_id" jdbcType="INTEGER" property="monitorId"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="duration" jdbcType="INTEGER" property="duration"/>
        <result column="check_frequency" jdbcType="INTEGER" property="checkFrequency"/>
        <result column="receivers" jdbcType="VARCHAR" property="receivers"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, monitor_id, rule_name, duration, check_frequency, receivers, status, extra_data,
        create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from smart_alert_rule where id = #{id,jdbcType=INTEGER}
    </delete>

    <delete id="deleteByMonitorId" parameterType="java.lang.Integer">
        delete from smart_alert_rule where monitor_id = #{monitorId,jdbcType=INTEGER}
    </delete>

    <update id="resetStatus" parameterType="map">
        update smart_alert_rule set status = #{status} where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="resetStatusByMonitorId" parameterType="map">
        update smart_alert_rule set status = #{status} where monitor_id = #{monitorId,jdbcType=INTEGER}
    </update>

    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.SmartAlertRulePO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into smart_alert_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="monitorId != null">
                monitor_id,
            </if>
            <if test="ruleName != null">
                rule_name,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="checkFrequency != null">
                check_frequency,
            </if>
            <if test="receivers != null">
                receivers,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="extraData != null">
                extra_data,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="monitorId != null">
                #{monitorId,jdbcType=INTEGER},
            </if>
            <if test="ruleName != null">
                #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=INTEGER},
            </if>
            <if test="checkFrequency != null">
                #{checkFrequency,jdbcType=INTEGER},
            </if>
            <if test="receivers != null">
                #{receivers,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                #{extraData,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="com.iqiyi.vip.zeus.core.po.SmartAlertRulePO">
        update smart_alert_rule
        <set>
            <if test="monitorId != null">
                monitor_id = #{monitorId,jdbcType=INTEGER},
            </if>
            <if test="ruleName != null">
                rule_name = #{ruleName,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                duration = #{duration,jdbcType=INTEGER},
            </if>
            <if test="checkFrequency != null">
                check_frequency = #{checkFrequency,jdbcType=INTEGER},
            </if>
            <if test="receivers != null">
                receivers = #{receivers,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                extra_data = #{extraData,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from smart_alert_rule
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByMonitorId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from smart_alert_rule
        where monitor_id = #{monitorId,jdbcType=INTEGER} and status = 1
    </select>

    <select id="selectByRuleName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from smart_alert_rule
        where rule_name = #{ruleName,jdbcType=VARCHAR} and status = 1
    </select>

</mapper>