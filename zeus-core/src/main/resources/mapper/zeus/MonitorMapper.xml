<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.MonitorMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.zeus.MonitorPO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="category" jdbcType="TINYINT" property="category"/>
        <result column="datasource_id" jdbcType="INTEGER" property="datasourceId"/>
        <result column="query" jdbcType="VARCHAR" property="query"/>
        <result column="team_code" jdbcType="VARCHAR" property="teamCode"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="dashboard_uid" jdbcType="VARCHAR" property="dashboardUid"/>
        <result column="panel_id" jdbcType="INTEGER" property="panelId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, name, category, datasource_id, query, team_code, create_user, update_user,
        dashboard_uid, panel_id, status, extra_data, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from monitor where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.zeus.MonitorPO" keyProperty="id" useGeneratedKeys="true">
        insert into monitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="datasourceId != null">
                datasource_id,
            </if>
            <if test="query != null">
                query,
            </if>
            <if test="teamCode != null">
                team_code,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="dashboardUid != null">
                dashboard_uid,
            </if>
            <if test="panelId != null">
                panel_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="extraData != null">
                extra_data,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=TINYINT},
            </if>
            <if test="datasourceId != null">
                #{datasourceId,jdbcType=INTEGER},
            </if>
            <if test="query != null">
                #{query,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="dashboardUid != null">
                #{dashboardUid,jdbcType=VARCHAR},
            </if>
            <if test="panelId != null">
                #{panelId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                #{extraData,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="com.iqiyi.vip.zeus.core.po.zeus.MonitorPO">
        update monitor
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=TINYINT},
            </if>
            <if test="datasourceId != null">
                datasource_id = #{datasourceId,jdbcType=INTEGER},
            </if>
            <if test="query != null">
                query = #{query,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                team_code = #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="dashboardUid != null">
                dashboard_uid = #{dashboardUid,jdbcType=VARCHAR},
            </if>
            <if test="panelId != null">
                panel_id = #{panelId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                extra_data = #{extraData,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from monitor where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getCountByDatasource" resultType="int" parameterType="java.lang.Integer">
        select count(1) from monitor where datasource_id = #{datasourceId,jdbcType=INTEGER}
    </select>

    <select id="search" parameterType="com.iqiyi.vip.zeus.core.req.MonitorSearchParam" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from monitor
        where status = 1
        <if test="teamCode != null">
            and team_code = #{teamCode,jdbcType=VARCHAR}
        </if>
        <if test="category != null">
            and category = #{category,jdbcType=TINYINT}
        </if>
        <if test="name != null">
            and name like CONCAT('%',#{name},'%')
        </if>
        <if test="dashboardUid != null">
            and dashboard_uid = #{dashboardUid,jdbcType=VARCHAR}
        </if>
        order by id desc
    </select>

</mapper>