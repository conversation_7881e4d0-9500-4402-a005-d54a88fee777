<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.zeus.AlertRuleMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.AlertRulePO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="monitor_id" jdbcType="INTEGER" property="monitorId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="team_code" jdbcType="VARCHAR" property="teamCode"/>
        <result column="duration" jdbcType="INTEGER" property="duration"/>
        <result column="check_frequency" jdbcType="INTEGER" property="checkFrequency"/>
        <result column="conditions" jdbcType="VARCHAR" property="conditions"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="receivers" jdbcType="VARCHAR" property="receivers"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="eagle_uid" jdbcType="VARCHAR" property="eagleUid"/>
        <result column="smart_alert_rule" jdbcType="VARCHAR" property="smartAlertRule"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, monitor_id, name, team_code, duration, check_frequency, conditions, level,
        receivers, create_user, update_user, eagle_uid, smart_alert_rule, status, extra_data, create_time, update_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from alert_rule where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.iqiyi.vip.zeus.core.po.AlertRulePO" keyProperty="id" useGeneratedKeys="true">
        insert into alert_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="monitorId != null">
                monitor_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="teamCode != null">
                team_code,
            </if>
            <if test="duration != null">
                duration,
            </if>
            <if test="checkFrequency != null">
                check_frequency,
            </if>
            <if test="conditions != null">
                conditions,
            </if>
            <if test="level != null">
                level,
            </if>
            <if test="receivers != null">
                receivers,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="eagleUid != null">
                eagle_uid,
            </if>
            <if test="smartAlertRule != null">
                smart_alert_rule,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="extraData != null">
                extra_data,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="monitorId != null">
                #{monitorId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=INTEGER},
            </if>
            <if test="checkFrequency != null">
                #{checkFrequency,jdbcType=INTEGER},
            </if>
            <if test="conditions != null">
                #{conditions,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="receivers != null">
                #{receivers,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="eagleUid != null">
                #{eagleUid,jdbcType=VARCHAR},
            </if>
            <if test="smartAlertRule != null">
                #{smartAlertRule,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                #{extraData,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="update" parameterType="com.iqiyi.vip.zeus.core.po.AlertRulePO">
        update alert_rule
        <set>
            <if test="monitorId != null">
                monitor_id = #{monitorId,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="teamCode != null">
                team_code = #{teamCode,jdbcType=VARCHAR},
            </if>
            <if test="duration != null">
                duration = #{duration,jdbcType=INTEGER},
            </if>
            <if test="checkFrequency != null">
                check_frequency = #{checkFrequency,jdbcType=INTEGER},
            </if>
            <if test="conditions != null">
                conditions = #{conditions,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=VARCHAR},
            </if>
            <if test="receivers != null">
                receivers = #{receivers,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="updateUser != null">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="eagleUid != null">
                eagle_uid = #{eagleUid,jdbcType=VARCHAR},
            </if>
            <if test="smartAlertRule != null">
                smart_alert_rule = #{smartAlertRule,jdbcType=VARCHAR},
            </if>
            <if test="smartAlertRule == null">
                smart_alert_rule = null,
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="extraData != null">
                extra_data = #{extraData,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateStatus">
        update alert_rule set status = #{status,jdbcType=TINYINT} where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from alert_rule where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByMonitor" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from alert_rule where monitor_id = #{monitorId,jdbcType=INTEGER}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from alert_rule where status = 1 and name = #{name,jdbcType=VARCHAR}
    </select>

    <select id="search" parameterType="com.iqiyi.vip.zeus.core.req.AlertRuleSearchParam" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from alert_rule
        where status = 1
        <if test="teamCode != null">
            and team_code = #{teamCode,jdbcType=VARCHAR}
        </if>
        <if test="name != null">
            and name like CONCAT('%',#{name},'%')
        </if>
    </select>

</mapper>