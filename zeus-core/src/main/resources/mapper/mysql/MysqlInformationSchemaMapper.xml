<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.core.mapper.mysql.MysqlInformationSchemaMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.core.po.DatasourceSchemaPO">
        <result column="TABLE_NAME" jdbcType="VARCHAR" property="table"/>
        <result column="COLUMN_NAME" jdbcType="VARCHAR" property="column"/>
        <result column="DATA_TYPE" jdbcType="VARCHAR" property="columnType"/>
    </resultMap>

    <select id="selectByDatabaseName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select TABLE_NAME, COLUMN_NAME, DATA_TYPE
        from COLUMNS
        where TABLE_SCHEMA = #{databaseName,jdbcType=VARCHAR}
    </select>

</mapper>