<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="DB2Tables" targetRuntime="MyBatis3">
        <!--去除注释  -->
        <commentGenerator>
            <property name="suppressAllComments" value="true" />
        </commentGenerator>

        <!--数据库连接 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="********************************************************************************************"
                        userId="vip_test" password="rg_z_6UF)w=Y">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false" />
        </javaTypeResolver>

        <!-- 生成model模型 -->
        <javaModelGenerator targetPackage="com.iqiyi.vip.zeus.core.po" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!--对应的xml mapper文件  -->
        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.iqiyi.vip.zeus.core.mapper.zeus" targetProject="src/main/java">
            <property name="enableSubPackages" value="true" />
        </javaClientGenerator>

<!--        <table tableName="datasource" domainObjectName="DatasourcePO" enableCountByExample="false"-->
<!--            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="monitor" domainObjectName="MonitorPO" enableCountByExample="false"-->
<!--            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="alert_rule" domainObjectName="AlertRulePO" enableCountByExample="false"-->
<!--            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
<!--        <table tableName="datasource_schema" domainObjectName="DatasourceSchema" enableCountByExample="false"-->
<!--            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>-->
        <table tableName="order_guard_monitor" domainObjectName="OrderGuardMonitor" enableCountByExample="false"
            enableDeleteByExample="false" enableSelectByExample="false" enableUpdateByExample="false"/>
    </context>
</generatorConfiguration>
