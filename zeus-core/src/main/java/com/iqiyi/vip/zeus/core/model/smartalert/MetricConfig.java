package com.iqiyi.vip.zeus.core.model.smartalert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2024/11/21 14:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MetricConfig {

    /**
     * 指标名称
     */
    private String name;
    /**
     * 数据源名称
     */
    private String db_source;
    /**
     * 对应数据源的查询表达式
     */
    private String expr;
    /**
     * 指标类型
     */
    private String metric_type;

    public MetricConfig copy() {
        return MetricConfig.builder()
            .name(name)
            .db_source(db_source)
            .expr(expr)
            .metric_type(metric_type)
            .build();
    }

}
