package com.iqiyi.vip.zeus.core.mapper.pilot;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.iqiyi.bigdata.pilot.rpc.client.TPilotStatement;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.po.PilotQueryResult;

/**
 * <AUTHOR>
 * @date 2022/3/23 上午 10:26
 */
@Component
@Slf4j
@Data
public class PilotComponent {

    @ConfigJsonValue("${pilot.hive.conn.conf:{\"hiveconf.mapreduce.job.queuename\": \"root.vip.order_analysis\",\"sparkconf.spark.sql.broadcastTimeout\": \"1000\",\"sparkconf.spark.driver.memory\": \"8g\",\"useSpark3\": \"true\",\"sparkconf.spark.sql.adaptive.coalescePartitions.enabled\": \"false\"}}")
    private Map<String, Object> pilotHiveConnConf;

    @Value("${pilot.hive.conn.url:**************************************************************************************************************************************;}")
    private String pilotHiveConnUrl;

    @ConfigJsonValue("${pilot.clickhouse.conn.conf:{\"clickhouseconf.token\": \"SMPpgMoeldH0drKhaNK7gif4qeSmBXyr\",\"clickhouseconf.database\": \"vip_order_analysis\",\"clickhouseconf.cluster\": \"vip_order_analysis\",\"clickhouseconf.project\": \"clickhouse\",\"clickhouseconf.env\": \"online\"}}")
    private Map<String, Object> pilotClickHouseConnConf;

    @Value("${pilot.clickhouse.conn.url:**************************************************************************;}")
    private String pilotClickHouseConnUrl;

    public List<LinkedHashMap<String, Object>> queryDetail(DataSourceType dataSourceType, String querySql) {
        try (Connection connection = createConnection(dataSourceType)) {
            return executeQuery(connection, querySql);
        } catch (SQLException sqlException) {
            log.error("query hive sql exception, querySql:{}", querySql, sqlException);
            return Collections.emptyList();
        }
    }

    private Connection createConnection(DataSourceType dataSourceType) throws SQLException {
        Map<String, Object> connConf = null;
        String connUrl = null;
        if (dataSourceType == DataSourceType.Hive) {
            connConf = pilotHiveConnConf;
            connUrl = pilotHiveConnUrl;
        }
        if (dataSourceType == DataSourceType.ClickHouse) {
            connConf = pilotClickHouseConnConf;
            connUrl = pilotClickHouseConnUrl;
        }
        Properties properties = new Properties();
        properties.putAll(connConf);
        log.info("use {} conn config from cloudConfig:{}", dataSourceType.getValue(), connConf);
        return DriverManager.getConnection(connUrl, properties);
    }

    private List<LinkedHashMap<String, Object>> executeQuery(Connection conn, String querySql) {
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        try (TPilotStatement statement = (TPilotStatement) conn.createStatement()) {
            statement.setFetchDuration(500);
            try (ResultSet resultSet = statement.executeQuery(querySql)) {
                if (resultSet == null) {
                    return dataList;
                }
                ResultSetMetaData rsMetaData = resultSet.getMetaData();
                int columnCount = rsMetaData != null ? rsMetaData.getColumnCount() : 0;
                if (columnCount == 0) {
                    return dataList;
                }
                List<String> columnNames = new ArrayList<>(columnCount);
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(rsMetaData.getColumnName(i));
                }
                while (resultSet.next()) {
                    LinkedHashMap<String, Object> rowDataMap = new LinkedHashMap<>();
                    for (String columnName : columnNames) {
                        rowDataMap.put(columnName, resultSet.getObject(columnName));
                    }
                    dataList.add(rowDataMap);
                }
            }
            log.info("executeQuery result size:{}, querySql:{}", dataList.size(), querySql);
        } catch (Exception e) {
            log.error("executeQuery fail，querySql:{}, error:", querySql, e);
        }
        return dataList;
    }

    public PilotQueryResult queryNew(DataSourceType dataSourceType, String querySql) {
        try (Connection connection = createConnection(dataSourceType)) {
            return executeQueryNew(connection, querySql);
        } catch (SQLException sqlException) {
            log.error("{} query sql exception, querySql:{}", dataSourceType.getValue(), querySql, sqlException);
            return null;
        }
    }

    private PilotQueryResult executeQueryNew(Connection conn, String querySql) {
        List<String> columnNames = new ArrayList<>();
        List<List<Object>> rowDataList = new ArrayList<>();
        try (TPilotStatement statement = (TPilotStatement) conn.createStatement()) {
            statement.setFetchDuration(500);
            try (ResultSet resultSet = statement.executeQuery(querySql)) {
                if (resultSet == null) {
                    return null;
                }
                ResultSetMetaData rsMetaData = resultSet.getMetaData();
                int columnCount = rsMetaData != null ? rsMetaData.getColumnCount() : 0;
                if (columnCount == 0) {
                    return null;
                }
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(rsMetaData.getColumnName(i));
                }
                while (resultSet.next()) {
                    List<Object> oneRowData = new ArrayList<>();
                    for (String columnName : columnNames) {
                        oneRowData.add(resultSet.getObject(columnName));
                    }
                    rowDataList.add(oneRowData);
                }
            }
            log.info("executeQueryNew result size:{}, querySql:{}", rowDataList.size(), querySql);
        } catch (Exception e) {
            log.error("executeQueryNew fail，querySql:{}, error:", querySql, e);
        }
        return new PilotQueryResult(columnNames, rowDataList);
    }

}
