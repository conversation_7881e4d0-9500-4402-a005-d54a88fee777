package com.iqiyi.vip.zeus.core.mapper.zeus;

import org.apache.ibatis.annotations.Param;

import com.iqiyi.vip.zeus.core.po.SmartAlertRulePO;

public interface SmartAlertRuleMapper {

    int deleteByPrimaryKey(Integer id);

    int deleteByMonitorId(Integer monitorId);

    int resetStatus(@Param("id") Integer id, @Param("status") Integer status);

    int resetStatusByMonitorId(@Param("monitorId") Integer monitorId, @Param("status") Integer status);

    int insert(SmartAlertRulePO record);

    int update(SmartAlertRulePO record);

    SmartAlertRulePO selectByPrimaryKey(Integer id);

    SmartAlertRulePO selectByMonitorId(Integer monitorId);

    SmartAlertRulePO selectByRuleName(String ruleName);
}