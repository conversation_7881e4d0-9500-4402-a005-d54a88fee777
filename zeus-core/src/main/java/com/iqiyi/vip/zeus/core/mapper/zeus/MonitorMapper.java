package com.iqiyi.vip.zeus.core.mapper.zeus;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.MonitorPO;
import com.iqiyi.vip.zeus.core.req.MonitorSearchParam;

public interface MonitorMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(MonitorPO record);

    int update(MonitorPO record);

    MonitorPO selectByPrimaryKey(Integer id);

    int getCountByDatasource(Integer datasourceId);

    List<MonitorPO> search(MonitorSearchParam searchParam);
}