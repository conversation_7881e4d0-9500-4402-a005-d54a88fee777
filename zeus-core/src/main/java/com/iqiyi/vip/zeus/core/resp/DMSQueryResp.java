package com.iqiyi.vip.zeus.core.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DMSQueryResp {

    /**
     * 查询结果
     */
    private List<LinkedHashMap<String, Object>> data;
    /**
     * 列信息
     */
    private List<LinkedHashMap<String, Object>> columns;

}
