package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.GuardDatasourcePO;
import com.iqiyi.vip.zeus.core.req.guard.GuardDatasourceSearchParam;

/**
 * 数据源Mapper接口
 */
public interface GuardDatasourceMapper {

    /**
     * 插入数据源
     */
    int insert(GuardDatasourcePO datasource);
    
    /**
     * 更新数据源
     */
    int updateById(GuardDatasourcePO datasource);
    
    /**
     * 根据ID删除数据源（逻辑删除）
     */
    int deleteById(@Param("id") Integer id);
    
    /**
     * 根据ID更新数据源状态
     */
    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);
    
    /**
     * 根据ID查询数据源
     */
    GuardDatasourcePO selectById(@Param("id") Integer id);
    
    /**
     * 根据数据源名称精确匹配数据源
     * @param name
     */
    GuardDatasourcePO getByName(@Param("name") String name);

    /**
     * 搜索数据源
     */
    List<GuardDatasourcePO> search(GuardDatasourceSearchParam searchParam);
    
}