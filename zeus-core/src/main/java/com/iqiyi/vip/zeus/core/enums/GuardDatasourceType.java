package com.iqiyi.vip.zeus.core.enums;

import java.util.HashMap;

/**
 * 业务稽核数据源类型
 *
 * @author: guojing
 * @date: 2025/9/10 18:30
 */
public enum GuardDatasourceType {

    StarRocks("StarRocks", GuardDataQueryType.Pilot, "****************************************************************************************************************;", "{\"hadoopUser\":\"hadoop-user-name\",\"cluster\":\"your-cluster\",\"token\":\"your-token\"}"),
    ClickHouse("ClickHouse", GuardDataQueryType.Pilot, "*********************************************************************;", "{\"clickhouseconf.token\": \"your-token\",\"clickhouseconf.database\": \"database-name\",\"clickhouseconf.cluster\": \"your-cluster-name\",\"clickhouseconf.project\": \"clickhouse\",\"clickhouseconf.env\": \"online\"}"),
    Hive("Hive", GuardDataQueryType.Pilot, "********************************************************************************************************;", "{\"hiveconf.mapreduce.job.queuename\": \"your-job-queue\",\"sparkconf.spark.sql.broadcastTimeout\": \"1000\",\"sparkconf.spark.driver.memory\": \"8g\",\"useSpark3\": \"true\",\"sparkconf.spark.sql.adaptive.coalescePartitions.enabled\": \"false\"}"),
    MySQL("MySQL", GuardDataQueryType.DMS_API, "mysql.domain:3306 注意: 使用统计域名，避免查询影响业务", "{\"cluster\": \"数据库的集群名称\",\"schema\": \"数据库名称\",\"isPublic\": \"是否公有云，1代表公有云，0代表私有云，不填默认为0\"}"),
    TiDB("TiDB", GuardDataQueryType.DMS_API, "tidb.domain:4000 注意: 使用统计域名，避免查询影响业务", "{\"cluster\": \"数据库的集群名称\",\"schema\": \"数据库名称\",\"isPublic\": \"是否公有云，1代表公有云，0代表私有云，不填默认为0\"}"),
    Prometheus("Prometheus", GuardDataQueryType.Prometheus_API, "http://mimir-ui.qiyi.domain/vip-eagle", ""),
    ;

    private String value;
    private GuardDataQueryType queryType;
    private String connUrlDemo;
    private String connConfigDemo;
    
    GuardDatasourceType(String value, GuardDataQueryType queryType, String connUrlDemo, String connConfigDemo) {
        this.value = value;
        this.queryType = queryType;
        this.connUrlDemo = connUrlDemo;
        this.connConfigDemo = connConfigDemo;
    }

    public String getValue() {
        return value;
    }

    public GuardDataQueryType getQueryType() {
        return queryType;
    }

    public String getConnUrlDemo() {
        return connUrlDemo;
    }

    public String getConnConfigDemo() {
        return connConfigDemo;
    }

    private static final HashMap<String, GuardDatasourceType> map = new HashMap<>();

    static {
        for (GuardDatasourceType enumType : GuardDatasourceType.values()) {
            map.put(enumType.getValue(), enumType);
        }
    }

    public static GuardDatasourceType parseValue(String value) {
        return map.getOrDefault(value, null);
    }

}
