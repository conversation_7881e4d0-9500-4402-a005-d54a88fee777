package com.iqiyi.vip.zeus.core.mapper.zeus;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.AlertRulePO;
import com.iqiyi.vip.zeus.core.req.AlertRuleSearchParam;

public interface AlertRuleMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AlertRulePO record);

    int update(AlertRulePO record);

    int updateStatus(Integer id, Integer status);

    AlertRulePO selectByPrimaryKey(Integer id);

    AlertRulePO selectByMonitor(Integer monitorId);

    AlertRulePO selectByName(Integer name);

    List<AlertRulePO> search(AlertRuleSearchParam searchParam);
}