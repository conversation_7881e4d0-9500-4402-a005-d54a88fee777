package com.iqiyi.vip.zeus.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.eagleclient.DevOpsClient;
import com.iqiyi.vip.zeus.eagleclient.EagleCenterClient;
import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.PrometheusQueryClient;
import com.iqiyi.vip.zeus.eagleclient.SmartAlertClient;
import com.iqiyi.vip.zeus.eagleclient.api.DevOpsApi;
import com.iqiyi.vip.zeus.eagleclient.api.EagleAlertRuleApi;
import com.iqiyi.vip.zeus.eagleclient.api.EagleDashboardApi;
import com.iqiyi.vip.zeus.eagleclient.api.EagleDatasourceApi;
import com.iqiyi.vip.zeus.eagleclient.api.EagleFolderApi;
import com.iqiyi.vip.zeus.eagleclient.api.EagleNotificationApi;
import com.iqiyi.vip.zeus.eagleclient.api.PrometheusRecordingRuleApi;
import com.iqiyi.vip.zeus.eagleclient.api.PrometheusSchemaApi;
import com.iqiyi.vip.zeus.eagleclient.api.SmartAlertApi;

/**
 * @author: guojing
 * @date: 2023/11/23 16:31
 */
@Slf4j
@Configuration
public class InvokeClientConfig {

    @Value("${eagle.center.domain:http://eagle-center.online.qiyi.qae}")
    private String eagleCenterDomain;
    @Value("${eagle.server.domain:http://eagle.online.qiyi.qae}")
    private String eagleServerDomain;
    @Value("${devops.server.domain:http://devops.vip.online.qiyi.qae}")
    private String devOpsServerDomain;
    @Value("${smart.alert.server.domain:http://normshield.qiyi.domain}")
    private String smartAlertServerDomain;

    @Value("${eagle.server.token:glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693}")
    private String eagleServerToken;
    @Value("${devops.server.token:3daf9775-2667-4733-a411-4fae622e5c0d}")
    private String devOpsServerToken;

    @Resource
    private RestTemplate eagleRestTemplate;
    @Resource
    private RestTemplate devOpsRestTemplate;
    @Resource
    private RestTemplate eagleCenterRestTemplate;
    @Resource
    private RestTemplate smartAlertRestTemplate;
    @Resource
    private RestTemplate prometheusQueryRestTemplate;

    private EagleClient eagleClient() {
        return new EagleClient(eagleRestTemplate, eagleServerDomain, eagleServerToken);
    }

    private DevOpsClient devOpsClient() {
        return new DevOpsClient(devOpsRestTemplate, devOpsServerDomain, devOpsServerToken);
    }

    private EagleCenterClient eagleCenterClient() {
        return new EagleCenterClient(eagleCenterRestTemplate, eagleCenterDomain);
    }

    private SmartAlertClient smartAlertClient() {
        return new SmartAlertClient(smartAlertRestTemplate, smartAlertServerDomain);
    }

    @Bean
    public PrometheusQueryClient prometheusQueryClient() {
        return new PrometheusQueryClient(prometheusQueryRestTemplate);
    }

    @Bean
    public EagleFolderApi eagleFolderApi() {
        return new EagleFolderApi(eagleClient());
    }

    @Bean
    public EagleDatasourceApi eagleDatasourceApi() {
        return new EagleDatasourceApi(eagleClient());
    }

    @Bean
    public EagleDashboardApi eagleDashboardApi() {
        return new EagleDashboardApi(eagleClient());
    }

    @Bean
    public EagleAlertRuleApi eagleAlertRuleApi() {
        return new EagleAlertRuleApi(eagleClient());
    }

    @Bean
    public EagleNotificationApi eagleNotificationApi() {
        return new EagleNotificationApi(eagleClient());
    }

    @Bean
    public DevOpsApi devOpsApi() {
        return new DevOpsApi(devOpsClient());
    }

    @Bean
    public PrometheusSchemaApi prometheusSchemaApi() {
        return new PrometheusSchemaApi(eagleClient());
    }

    @Bean
    public PrometheusRecordingRuleApi prometheusRecordingRuleApi() {
        return new PrometheusRecordingRuleApi(eagleCenterClient());
    }

    @Bean
    public SmartAlertApi smartAlertApi() {
        return new SmartAlertApi(smartAlertClient());
    }

//    @Bean
//    public PrometheusQueryApi prometheusQueryApi() {
//        return new PrometheusQueryApi(prometheusQueryClient());
//    }

}
