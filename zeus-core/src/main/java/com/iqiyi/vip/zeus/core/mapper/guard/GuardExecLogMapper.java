package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.GuardExecLogPO;

/**
 * 稽核项执行记录Mapper接口
 */
public interface GuardExecLogMapper {
    
    /**
     * 根据ID查询执行记录
     */
    GuardExecLogPO selectById(@Param("id") Long id);
    
    /**
     * 根据稽核项ID和日期查询执行记录
     */
    List<GuardExecLogPO> selectByGuardItemIdAndDay(@Param("guardItemId") Integer guardItemId, 
                                                 @Param("day") LocalDate day);
    
    /**
     * 根据稽核项ID查询执行记录列表
     */
    List<GuardExecLogPO> selectByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据执行日期查询执行记录列表
     */
    List<GuardExecLogPO> selectByDay(@Param("day") LocalDate day);
    
    /**
     * 根据执行结果ID查询执行记录
     */
    List<GuardExecLogPO> selectByExecResultId(@Param("execResultId") Long execResultId);
    
    /**
     * 根据执行状态查询执行记录列表
     */
    List<GuardExecLogPO> selectByStatus(@Param("status") Integer status);
    
    /**
     * 分页查询执行记录
     */
    List<GuardExecLogPO> selectByPage(@Param("offset") Integer offset, 
                                    @Param("limit") Integer limit,
                                    @Param("guardItemId") Integer guardItemId,
                                    @Param("startDate") LocalDate startDate,
                                    @Param("endDate") LocalDate endDate,
                                    @Param("status") Integer status);
    
    /**
     * 统计执行记录总数
     */
    Long countByCondition(@Param("guardItemId") Integer guardItemId,
                         @Param("startDate") LocalDate startDate,
                         @Param("endDate") LocalDate endDate,
                         @Param("status") Integer status);
    
    /**
     * 插入执行记录
     */
    int insert(GuardExecLogPO execLog);
    
    /**
     * 批量插入执行记录
     */
    int batchInsert(@Param("list") List<GuardExecLogPO> execLogList);
    
    /**
     * 更新执行记录
     */
    int updateById(GuardExecLogPO execLog);
    
    /**
     * 根据ID删除执行记录
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据稽核项ID删除执行记录
     */
    int deleteByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 查询稽核项最新的执行记录
     */
    GuardExecLogPO selectLatestByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 统计稽核项执行次数
     */
    Long countByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 统计稽核项失败次数
     */
    Long countFailedByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 查询失败的执行记录
     */
    List<GuardExecLogPO> selectFailedRecords(@Param("limit") Integer limit);
}