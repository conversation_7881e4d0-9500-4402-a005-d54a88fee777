package com.iqiyi.vip.zeus.core.utils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class BizDateUtil {

    public static final String PATTERN_DATE = "yyyy-MM-dd";

    public static final String COMMON_PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";

    public static final String yyyyMMdd_PATTERN = "yyyyMMdd";

    public static final String yyyyMMddHHmm_PATTERN = "yyyyMMddHHmm";

    private static final SimpleDateFormat COMMON_PATTERN_FORMAT = new SimpleDateFormat(COMMON_PATTERN_DATETIME);


    /**
     * 取得当前日期
     */
    public static Date getCurrentDate() {
        return new Date();
    }

    public static Timestamp getCurrentTimestamp() {
        return Timestamp.valueOf(LocalDateTime.now());
    }

    public static String getNowDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat(COMMON_PATTERN_DATETIME);
        return sdf.format(new Date());
    }


    /**
     * 把日期格式化为yyyy-MM-dd格式的字符串
     */
    public static String date2String(Date date) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(PATTERN_DATE);
            return dateFormat.format(date);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 字符传转换为日期
     * @param str
     * @return
     */
    public static Date string2Date(String str){
        DateFormat sdf = new SimpleDateFormat(PATTERN_DATE);
        try{
            return sdf.parse(str);
        }catch(Exception e){
            return null;
        }
    }

    /**
     * 把日期格式化为yyyy-MM-dd HH:mm:ss格式的字符串
     * @param date
     */
    public static String datetime2String(Date date) {
        return datetime2String(date, COMMON_PATTERN_DATETIME);
    }

    /**
     * 把日期格式化为指定格式的字符串
     * @param date
     */
    public static String datetime2String(Date date, String partner) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(partner);
            return dateFormat.format(date);
        } catch (Exception e) {
            return null;
        }
    }

    public static String yyyyMMddHHmmssString(Date date) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(yyyyMMddHHmm_PATTERN);
            return dateFormat.format(date);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 字符传转换为日期
     * @param str
     */
    public static Date string2DateTime(String str){
        DateFormat sdf = new SimpleDateFormat(COMMON_PATTERN_DATETIME);
        try{
            return sdf.parse(str);
        }catch(Exception e){
            return null;
        }
    }

    public static Timestamp plusMonths(Timestamp timestamp, int months) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.plusMonths(months);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Timestamp plusDays(Timestamp timestamp, int days) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.plusDays(days);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Timestamp minusDays(Timestamp timestamp, int days) {
        return plusDays(timestamp, -days);
    }

    public static Timestamp plusMinutes(Timestamp timestamp, int minutes) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.plusMinutes(minutes);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Timestamp getTimestamp(String dateString) {
        try {
            return Timestamp.valueOf(dateString);
        } catch (Exception e) {
            return null;
        }
    }

}
