package com.iqiyi.vip.zeus.core.mapper.zeus;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.zeus.DatasourcePO;

public interface DatasourceMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(DatasourcePO record);

    int update(DatasourcePO record);

    DatasourcePO selectByPrimaryKey(Integer id);

    List<DatasourcePO> batchSelectByPrimaryKey(List<Integer> ids);

    DatasourcePO selectByName(String name);

    List<DatasourcePO> search(@Param("teamCode") String teamCode, @Param("type")String type, @Param("name")String name);

    List<DatasourcePO> listDatasourceByType(String type);

}