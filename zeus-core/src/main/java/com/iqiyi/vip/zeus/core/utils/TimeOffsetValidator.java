package com.iqiyi.vip.zeus.core.utils;

import java.util.regex.Pattern;

import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;

/**
 * @author: guojing
 * @date: 2023/12/19 15:50
 */
public class TimeOffsetValidator {

    private final static Pattern OFFSET_PATTERN = Pattern.compile("^[1-9]\\d*$");

    public static boolean offsetInvalid(String offset) {
        int lastCharIndex = offset.length() - 1;
        String numberPart = offset.substring(0, lastCharIndex);
        String unitPart = offset.substring(lastCharIndex);
        boolean numberPartMatch = OFFSET_PATTERN.matcher(numberPart).matches();
        return !numberPartMatch || OffsetTimeUnit.parseValue(unitPart) == null;
    }

}
