package com.iqiyi.vip.zeus.core.model.guard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

import com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO;

/**
 * @author: guojing
 * @date: 2025/5/19 11:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderGuardMonitor {

    private Integer id;

    private String name;

    private String category;

    private String departmentName;

    private String datasourceType;

    private Integer datasourceId;

    private String querySql;

    private String detailSql;

    private String createOpr;

    private String updateOpr;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

    public static OrderGuardMonitor buildFromPO(OrderGuardMonitorPO po) {
        if (po == null) {
            return null;
        }
        return OrderGuardMonitor.builder()
            .id(po.getId())
            .name(po.getName())
            .category(po.getCategory())
            .departmentName(po.getDepartmentName())
            .datasourceType(po.getDatasourceType())
            .datasourceId(po.getDatasourceId())
            .querySql(po.getQuerySql())
            .detailSql(po.getDetailSql())
            .createOpr(po.getCreateOpr())
            .updateOpr(po.getUpdateOpr())
            .status(po.getStatus())
            .createTime(po.getCreateTime())
            .updateTime(po.getUpdateTime())
            .build();

    }

}
