package com.iqiyi.vip.zeus.core.constants;

/**
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2025/02/10
 */
public class OrderRightConstants {
    //权益开通失败的redis订单列表
    public static final String RIGHT_FAILED_ORDERS_REDIS_KEY = "failed_orders_str";
    //使用 SET 存储订单号，快速去重
    public static final String RIGHT_FAILED_ORDERS_SET_REDIS_KEY = "failed_orders_set";
    // 记录失败订单的最新操作时间戳
    public static final String RIGHT_FAILED_TIMESTAMP_KEY = "failed_orders_ts";


    //权益已恢复的redis订单列表
    public static final String RIGHT_RECOVERED_ORDERS_REDIS_KEY = "recovered_orders_str";
    //使用 SET 存储订单号，快速去重
    public static final String RIGHT_RECOVERED_ORDERS_SET_REDIS_KEY = "recovered_orders_set";
    // 记录恢复订单的最新操作时间戳
    public static final String RIGHT_RECOVERED_TIMESTAMP_KEY = "recovered_orders_ts";

    public static final String LAST_NOTIFY_FAILED_COUNT = "vip_right:last_notify:failed_count";
    public static final String LAST_NOTIFY_RECOVERED_COUNT = "vip_right:last_notify:recovered_count";
    public static final String LAST_NOTIFY_FAILED_TS = "vip_right:last_notify:failed_ts";
    public static final String LAST_NOTIFY_RECOVERED_TS = "vip_right:last_notify:recovered_ts";
}
