package com.iqiyi.vip.zeus.core.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;
import com.iqiyi.vip.zeus.core.mapper.zeus.AlertRuleMapper;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.po.AlertRulePO;
import com.iqiyi.vip.zeus.core.req.AlertRuleSearchParam;
import com.iqiyi.vip.zeus.core.utils.MonitorToEagleTransfer;
import com.iqiyi.vip.zeus.eagleclient.api.EagleAlertRuleApi;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.AlertRule;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleGroup;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;

/**
 * @author: guojing
 * @date: 2023/12/13 17:46
 */
@Profile("!sg")
@Service
public class ZeusAlertRuleService {

    @Resource
    private AlertRuleMapper alertRuleMapper;
    @Resource
    private EagleAlertRuleApi eagleAlertRuleApi;
    @Resource
    private DevOpsComponent devOpsComponent;
    @Resource
    private ZeusMonitorService zeusMonitorService;

    public Integer create(ZeusAlertRule createParam, ZeusMonitor zeusMonitor, DashboardWithMeta dashboardWithMeta) {
        if (dashboardWithMeta != null) {
            AlertRule alertRule = MonitorToEagleTransfer.zeusAlertRuleToEagle(createParam, zeusMonitor, dashboardWithMeta);
            String alertRuleUid = eagleAlertRuleApi.createAlertRule(alertRule);
            int checkFrequencySeconds = OffsetTimeUnit.toSeconds(createParam.getCheckFrequency());
            AlertRuleGroup ruleGroup = eagleAlertRuleApi.getRuleGroup(alertRule.getFolderUID(), alertRule.getRuleGroup());
            if (ruleGroup != null && !ruleGroup.getInterval().equals(checkFrequencySeconds)) {
                AlertRuleGroup alertRuleGroup = AlertRuleGroup.newInstance(alertRule.getFolderUID(), alertRule.getRuleGroup(), checkFrequencySeconds);
                eagleAlertRuleApi.updateRuleGroup(alertRuleGroup);
            }
            createParam.setEagleUid(alertRuleUid);
        }
        AlertRulePO alertRulePO = createParam.toAlertRulePO();
        alertRuleMapper.insert(alertRulePO);
        return alertRulePO.getId();
    }

    public boolean update(ZeusAlertRule updateParam, ZeusMonitor monitor, DashboardWithMeta dashboardWithMeta) {
        if (dashboardWithMeta != null) {
            AlertRule alertRule = MonitorToEagleTransfer.zeusAlertRuleToEagle(updateParam, monitor, dashboardWithMeta);
            boolean updated = eagleAlertRuleApi.updateAlertRule(alertRule);
            if (!updated) {
                return false;
            }
            int checkFrequencySeconds = OffsetTimeUnit.toSeconds(updateParam.getCheckFrequency());
            AlertRuleGroup ruleGroup = eagleAlertRuleApi.getRuleGroup(alertRule.getFolderUID(), alertRule.getRuleGroup());
            if (ruleGroup != null && !ruleGroup.getInterval().equals(checkFrequencySeconds)) {
                AlertRuleGroup alertRuleGroup = AlertRuleGroup.newInstance(alertRule.getFolderUID(), alertRule.getRuleGroup(), checkFrequencySeconds);
                eagleAlertRuleApi.updateRuleGroup(alertRuleGroup);
            }
        }
        alertRuleMapper.update(updateParam.toAlertRulePO());
        return true;
    }

    public boolean delete(ZeusAlertRule zeusAlertRule) {
        String eagleUid = zeusAlertRule.getEagleUid();
        if (StringUtils.isNotBlank(eagleUid)) {
            eagleAlertRuleApi.deleteAlertRule(zeusAlertRule.getEagleUid());
        }
        alertRuleMapper.deleteByPrimaryKey(zeusAlertRule.getId());
        return true;
    }

    public ZeusAlertRule getByMonitor(Integer monitorId) {
        AlertRulePO alertRulePO = alertRuleMapper.selectByMonitor(monitorId);
        if (alertRulePO == null) {
            return null;
        }
        return ZeusAlertRule.buildFrom(alertRulePO);
    }

    public ZeusAlertRule getById(Integer alertRuleId) {
        if (alertRuleId == null) {
            return null;
        }
        AlertRulePO alertRulePO = alertRuleMapper.selectByPrimaryKey(alertRuleId);
        return ZeusAlertRule.buildFrom(alertRulePO);
    }

    public ZeusAlertRule getFriendlyById(Integer alertRuleId) {
        if (alertRuleId == null) {
            return null;
        }
        AlertRulePO alertRulePO = alertRuleMapper.selectByPrimaryKey(alertRuleId);
        ZeusAlertRule zeusAlertRule = ZeusAlertRule.buildFrom(alertRulePO);
        fillDisplayInfo(zeusAlertRule);
        return zeusAlertRule;
    }

    public List<ZeusAlertRule> search(AlertRuleSearchParam searchParam) {
        List<AlertRulePO> searchResult = alertRuleMapper.search(searchParam);
        if (CollectionUtils.isEmpty(searchResult)) {
            return Collections.emptyList();
        }
        return searchResult.stream().map(alertRulePO -> {
            ZeusAlertRule zeusAlertRule = ZeusAlertRule.buildFrom(alertRulePO);
            fillDisplayInfo(zeusAlertRule);
            return zeusAlertRule;
        }).collect(Collectors.toList());
    }

    private void fillDisplayInfo(ZeusAlertRule alertRule) {
        Team team = devOpsComponent.getByTeamCode(alertRule.getTeamCode());
        if (team!= null) {
            alertRule.setTeamName(team.getCnName());
        }
        ZeusMonitor monitor = zeusMonitorService.getById(alertRule.getMonitorId());
        if (monitor != null) {
            alertRule.setMonitorName(monitor.getName());
        }
    }

}
