package com.iqiyi.vip.zeus.core.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监控查询条件操作符枚举
 *
 * @author: guojing
 * @date: 2023/12/20 21:02
 */
public enum MonitorConditionOperator {

    EQUALS("=", "等于", Arrays.asList(DataSourceType.Prometheus, DataSourceType.MySQL)),
    NOT_EQUALS("!=", "不等于", Arrays.asList(DataSourceType.Prometheus, DataSourceType.MySQL)),
    REGEX_MATCH("=~", "正则匹配", Collections.singletonList(DataSourceType.Prometheus)),
    REGEX_NOT_MATCH("!~", "正则不匹配", Collections.singletonList(DataSourceType.Prometheus)),
    GT(">", "大于", Collections.singletonList(DataSourceType.MySQL)),
    GTE(">=", "大于等于", Collections.singletonList(DataSourceType.MySQL)),
    LT("<", "小于", Collections.singletonList(DataSourceType.MySQL)),
    LTE("<=", "小于等于", Collections.singletonList(DataSourceType.MySQL)),
    IN("in", "in", Collections.singletonList(DataSourceType.MySQL)) {
        @Override
        public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
            List<String> rightValue = rightType == ValueType.STRING ? right.stream().map(value -> "'" + value + "'").collect(Collectors.toList()) : right;
            return left + " IN (" + String.join(",", rightValue) + ")";
        }
    },
    NOT_IN("not_in", "not in", Collections.singletonList(DataSourceType.MySQL)) {
        @Override
        public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
            List<String> rightValue = rightType == ValueType.STRING ? right.stream().map(value -> "'" + value + "'").collect(Collectors.toList()) : right;
            return left + " NOT IN (" + String.join(",", rightValue) + ")";
        }
    },
    BETWEEN_AND("between_and", "between and", 2, Collections.singletonList(DataSourceType.MySQL)) {
        @Override
        public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
            return rightType == ValueType.STRING
                ? left + " BETWEEN '" + right.get(0) + "' AND '" + right.get(1) + "'"
                : left + " BETWEEN " + right.get(0) + " AND " + right.get(1);
        }
    },
    NOT_BETWEEN_AND("not_between_and", "not between and", 2, Collections.singletonList(DataSourceType.MySQL)) {
        @Override
        public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
            return rightType == ValueType.STRING
                ? left + " NOT BETWEEN '" + right.get(0) + "' AND '" + right.get(1) + "'"
                : left + " NOT BETWEEN " + right.get(0) + " AND " + right.get(1);
        }
    },
    IS_NULL("is_null", "is null", 0, Collections.singletonList(DataSourceType.MySQL)) {
        @Override
        public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
            return left + " IS NULL";
        }
    },
    IS_NOT_NULL("is_not_null", "is not null", 0, Collections.singletonList(DataSourceType.MySQL)) {
        @Override
        public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
            return left + " IS NOT NULL";
        }
    },
    ;

    private String value;
    private String desc;
    /**
     * 操作符支持几个值
     */
    private Integer valueCount;
    private List<DataSourceType> supportDataSourceTypes;

    MonitorConditionOperator(String value, String desc, List<DataSourceType> supportDataSourceTypes) {
        this.value = value;
        this.desc = desc;
        this.valueCount = 1;
        this.supportDataSourceTypes = supportDataSourceTypes;
    }

    MonitorConditionOperator(String value, String desc, Integer valueCount, List<DataSourceType> supportDataSourceTypes) {
        this.value = value;
        this.desc = desc;
        this.valueCount = valueCount;
        this.supportDataSourceTypes = supportDataSourceTypes;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getValueCount() {
        return valueCount;
    }

    public static List<MonitorConditionOperator> supportOperatorOf(DataSourceType dataSourceType) {
        if (dataSourceType == null) {
            return Collections.emptyList();
        }
        return Arrays.stream(MonitorConditionOperator.values())
            .filter(item -> item.supportDataSourceTypes.contains(dataSourceType))
            .collect(Collectors.toList());
    }

    private static final Map<String, MonitorConditionOperator> map = new LinkedHashMap<>();

    static {
        for (MonitorConditionOperator enumType : MonitorConditionOperator.values()) {
            map.put(enumType.getValue(), enumType);
        }
    }

    public static MonitorConditionOperator parseValue(String value) {
        return map.getOrDefault(value, null);
    }

    public String buildExpr(DataSourceType dataSourceType, String left, List<String> right, ValueType rightType) {
        String firstValue = right.get(0);
        String rightValue = rightType == ValueType.STRING ? "'" + firstValue + "'" : firstValue;
        if (dataSourceType == DataSourceType.Prometheus) {
            rightValue = "\"" + firstValue + "\"";
        }
        return left + this.value + rightValue;
    }

}
