package com.iqiyi.vip.zeus.core.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.mapper.zeus.MonitorRecordingRuleMapper;
import com.iqiyi.vip.zeus.core.model.MonitorQueryMapping;
import com.iqiyi.vip.zeus.core.model.QueryRecordMapping;
import com.iqiyi.vip.zeus.core.model.RecordMonitorMapping;
import com.iqiyi.vip.zeus.core.po.zeus.MonitorRecordingRulePO;

/**
 * @author: guojing
 * @date: 2024/4/9 16:19
 */
@Profile("!sg")
@Service
public class MonitorRecordingRuleService {

    @Resource
    private MonitorRecordingRuleMapper recordingRuleMapper;

    public void batchInsert(List<MonitorRecordingRulePO> monitorRecordingRulePOS) {
        if (CollectionUtils.isEmpty(monitorRecordingRulePOS)) {
            return;
        }
        recordingRuleMapper.batchInsert(monitorRecordingRulePOS);
    }

    public void deleteByMonitorId(Integer monitorId) {
        if (monitorId == null) {
            return;
        }
        recordingRuleMapper.deleteByMonitorIdAndQueryIds(monitorId, null);
    }

    @Transactional(transactionManager = "zeusTransactionManager", rollbackFor = Exception.class)
    public void insertAndDelete(Integer monitorId, List<MonitorRecordingRulePO> needInsertList, List<Integer> deleteQueryIds) {
        if (CollectionUtils.isNotEmpty(needInsertList)) {
            recordingRuleMapper.batchInsert(needInsertList);
        }
        if (CollectionUtils.isNotEmpty(deleteQueryIds)) {
            recordingRuleMapper.deleteByMonitorIdAndQueryIds(monitorId, deleteQueryIds);
        }
    }

    public Map<Integer, String> getByMonitorId(Integer monitorId) {
        if (monitorId == null) {
            return Collections.emptyMap();
        }
        List<QueryRecordMapping> queryRecordMappings = recordingRuleMapper.selectByMonitorId(monitorId);
        return queryRecordMappings.stream().collect(Collectors.toMap(QueryRecordMapping::getQueryId, QueryRecordMapping::getRecordName));
    }

    public Map<Integer, List<Integer>> getByRecordName(String recordName) {
        if (StringUtils.isBlank(recordName)) {
            return Collections.emptyMap();
        }
        List<MonitorQueryMapping> monitorQueryMappings = recordingRuleMapper.selectByRecordName(recordName);
        return monitorQueryMappings.stream().collect(Collectors.toMap(MonitorQueryMapping::getMonitorId, MonitorQueryMapping::getQueryIds));
    }

    public Map<String, List<MonitorQueryMapping>> batchGetByRecordName(List<String> recordNames) {
        if (CollectionUtils.isEmpty(recordNames)) {
            return Collections.emptyMap();
        }
        List<RecordMonitorMapping> recordMonitorMappings = recordingRuleMapper.batchSelectByRecordNames(recordNames);
        return recordMonitorMappings.stream().collect(Collectors.toMap(RecordMonitorMapping::getRecordName, RecordMonitorMapping::getMonitorQueryMappings));
    }

}
