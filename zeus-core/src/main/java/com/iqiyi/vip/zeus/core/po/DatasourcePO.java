package com.iqiyi.vip.zeus.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.sql.Timestamp;
import java.util.Map;

import com.iqiyi.vip.zeus.core.constants.DatasourceConstants;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasourcePO {

    private Integer id;

    private String name;
    /**
     * 宙斯数据源描述
     */
    private String description;

    private String type;

    private String teamCode;

    private String url;

    private String createUser;

    private String updateUser;

    private Integer status;

    private String extraData;

    private String sensitiveExtraData;

    private Timestamp createTime;

    private Timestamp updateTime;

    public String getEagleDatasourceUid() {
        Map<String, Object> extraDataMap = JacksonUtils.parseMap(extraData);
        return MapUtils.getString(extraDataMap, DatasourceConstants.EXTRA_FIELD_EAGLE_DATASOURCE_UID);
    }

}