package com.iqiyi.vip.zeus.core.mapper.zeus;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.model.MonitorQueryMapping;
import com.iqiyi.vip.zeus.core.model.QueryRecordMapping;
import com.iqiyi.vip.zeus.core.model.RecordMonitorMapping;
import com.iqiyi.vip.zeus.core.po.zeus.MonitorRecordingRulePO;

public interface MonitorRecordingRuleMapper {
    int deleteByPrimaryKey(Integer id);

    int deleteByMonitorIdAndQueryIds(@Param("monitorId") Integer monitorId, @Param("queryIds") List<Integer> queryIds);

    int batchInsert(List<MonitorRecordingRulePO> records);

    MonitorRecordingRulePO selectByPrimaryKey(Integer id);

    List<QueryRecordMapping> selectByMonitorId(Integer monitorId);

    List<MonitorQueryMapping> selectByRecordName(String recordName);

    List<RecordMonitorMapping> batchSelectByRecordNames(List<String> recordNames);

}