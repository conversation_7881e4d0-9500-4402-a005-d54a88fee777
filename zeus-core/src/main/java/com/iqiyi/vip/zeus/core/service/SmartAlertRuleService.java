package com.iqiyi.vip.zeus.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.core.mapper.zeus.SmartAlertRuleMapper;
import com.iqiyi.vip.zeus.core.model.SmartAlertRule;
import com.iqiyi.vip.zeus.core.po.SmartAlertRulePO;

/**
 * @author: guojing
 * @date: 2025/3/8 11:23
 */

@Profile("!sg")
@Slf4j
@Service
public class SmartAlertRuleService {

    @Resource
    private SmartAlertRuleMapper smartAlertRuleMapper;

    public boolean delete(Integer id) {
        if (id == null) {
            return false;
        }
        smartAlertRuleMapper.resetStatus(id, 0);
        return true;
    }

    public boolean deleteByMonitorId(Integer monitorId) {
        if (monitorId == null) {
            return false;
        }
        smartAlertRuleMapper.resetStatusByMonitorId(monitorId, 0);
        return true;
    }

    public Integer create(SmartAlertRule record) {
        SmartAlertRulePO po = SmartAlertRule.buildToPO(record);
        smartAlertRuleMapper.insert(po);
        record.setId(po.getId());
        return record.getId();
    }

    public boolean update(SmartAlertRule record) {
        SmartAlertRulePO po = SmartAlertRule.buildToPO(record);
        smartAlertRuleMapper.update(po);
        return true;
    }

    public boolean saveOrUpdate(SmartAlertRule record) {
        SmartAlertRulePO po = SmartAlertRule.buildToPO(record);
        if (record.getId() == null) {
            smartAlertRuleMapper.insert(po);
            record.setId(po.getId());
        } else {
            smartAlertRuleMapper.update(po);
        }
        return true;
    }

    public SmartAlertRule selectByPrimaryKey(Integer id) {
        if (id == null) {
            return null;
        }
        SmartAlertRulePO po = smartAlertRuleMapper.selectByPrimaryKey(id);
        return SmartAlertRule.buildFromPO(po);
    }

    public SmartAlertRule selectByMonitorId(Integer monitorId) {
        if (monitorId == null) {
            return null;
        }
        SmartAlertRulePO po = smartAlertRuleMapper.selectByMonitorId(monitorId);
        return SmartAlertRule.buildFromPO(po);
    }

    public SmartAlertRule selectByRuleName(String ruleName) {
        if (StringUtils.isBlank(ruleName)) {
            return null;
        }
        SmartAlertRulePO po = smartAlertRuleMapper.selectByRuleName(ruleName);
        return SmartAlertRule.buildFromPO(po);
    }
    
}
