package com.iqiyi.vip.zeus.core.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2024/1/27 18:02
 */
public enum ValueType {

    NUMBER("number", "数字"),
    STRING("string", "字符串"),
    DATE_TIME("datetime", "日期"),
    ;

    private String value;
    private String desc;

    ValueType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, ValueType> map = new LinkedHashMap<>();
    static {
        for (ValueType enumType : ValueType.values()) {
            map.put(enumType.getValue(), enumType);
        }
    }

    public static ValueType parseValue(String value) {
        return map.getOrDefault(value, STRING);
    }

}
