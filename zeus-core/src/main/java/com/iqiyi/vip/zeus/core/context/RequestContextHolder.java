package com.iqiyi.vip.zeus.core.context;

import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;

/**
 * @author: guojing
 * @date: 2023/12/13 11:23
 */
public class RequestContextHolder {

    private static final ThreadLocal<RequestContext> REQUEST_CONTEXT = ThreadLocal.withInitial(RequestContext::new);

    public static RequestContext getRequestContext() {
        return REQUEST_CONTEXT.get();
    }

    public static AuthorityUser getCurrentUser(){
        RequestContext requestContext = REQUEST_CONTEXT.get();
        return requestContext != null ? requestContext.getAuthorityUser() : null;
    }

    public static AuthorityTeamBasic getCurrentUserRealTeam(){
        RequestContext requestContext = REQUEST_CONTEXT.get();
        if (requestContext == null) {
            return null;
        }
        AuthorityUser authorityUser = requestContext.getAuthorityUser();
        return authorityUser != null ? authorityUser.getRealTeam() : null;
    }

    public static void setRequestContext(RequestContext requestContext){
        REQUEST_CONTEXT.set(requestContext);
    }

    public static void cleanRequestContext() {
        REQUEST_CONTEXT.remove();
    }

}
