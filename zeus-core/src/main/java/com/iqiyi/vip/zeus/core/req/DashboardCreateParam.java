package com.iqiyi.vip.zeus.core.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @author: guojing
 * @date: 2023/12/12 20:42
 */
@Data
@NoArgsConstructor
@ApiModel("创建Dashboard参数")
public class DashboardCreateParam {

    /**
     * dashboard title
     */
    @ApiModelProperty(value = "Dashboard标题")
    @NotBlank(message = "Dashboard标题不能为空")
    private String title;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String message;

    public DashboardCreateParam(String title) {
        this.title = title;
    }
}
