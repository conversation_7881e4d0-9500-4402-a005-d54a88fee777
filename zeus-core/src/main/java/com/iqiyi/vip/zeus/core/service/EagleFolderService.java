package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.eagleclient.api.EagleFolderApi;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientAlreadyExistsException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.FolderSearchResult;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Folder;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.FolderSimpleInfo;
import com.iqiyi.vip.zeus.eagleclient.request.UpdateFolderRequest;

/**
 * @author: guojing
 * @date: 2023/11/23 17:37
 */
@Profile("!sg")
@Service
public class EagleFolderService {

    @Resource
    private EagleFolderApi folderApi;

    /**
     * 判断Folder是否存在
     * @param folderTitle
     */
    public FolderSimpleInfo exist(String folderTitle){
        EagleFolderService thisObj = (EagleFolderService) AopContext.currentProxy();
        List<FolderSearchResult> searchResults = thisObj.searchFolderByTitle(folderTitle);
        FolderSearchResult matchedFolder = searchResults.stream()
            .filter(folder -> Objects.equals(folder.getTitle(), folderTitle))
            .findFirst()
            .orElse(null);
        if (matchedFolder == null) {
            return null;
        }
        return new FolderSimpleInfo(matchedFolder.getId(), matchedFolder.getUid(), matchedFolder.getTitle(), matchedFolder.getUrl());
    }

    /**
     * 返回或创建Folder，如果已存在则返回
     * @param folderTitle
     */
    public FolderSimpleInfo getOrCreate(String folderTitle){
        FolderSimpleInfo folderSimpleInfo = exist(folderTitle);
        if (folderSimpleInfo != null) {
            return folderSimpleInfo;
        }
        Folder folder = create(folderTitle);
        return FolderSimpleInfo.buildFromFolder(folder);
    }

    /**
     * 在鹰眼创建Folder
     * @param folderTitle
     * @return FolderId
     */
    public Folder create(String folderTitle){
        try {
            return folderApi.createFolder(folderTitle);
        } catch (EagleClientAlreadyExistsException e) {
            throw BizException.newParamException("Folder已存在");
        }
    }

    /**
     * 更新Folder
     * @param folderUid
     * @param folderTitle
     */
    public boolean update(String folderUid, String folderTitle){
        UpdateFolderRequest updateRequest = new UpdateFolderRequest(folderUid, folderTitle);
        try {
            folderApi.updateFolder(updateRequest);
            return true;
        } catch (EagleClientNotExistsException e) {
            throw BizException.newSystemException("Folder不存在");
        }
    }

    /**
     * 根据uid查询Folder信息
     * @param folderUid
     */
    public Folder getByUid(String folderUid){
        return folderApi.getFolderByUid(folderUid);
    }

    /**
     * 根据title模糊查询Folder
     * @param folderTitle
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "FolderSearchResult_searchFolderByTitle", cacheType= CacheType.LOCAL)
    public List<FolderSearchResult> searchFolderByTitle(String folderTitle){
        return folderApi.searchFolderByTitle(folderTitle);
    }

    public List<FolderSimpleInfo> getAllFolder(){
        return folderApi.getAllFolder();
    }

}
