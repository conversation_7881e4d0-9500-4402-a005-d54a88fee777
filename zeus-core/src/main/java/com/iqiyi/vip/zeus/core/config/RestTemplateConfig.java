package com.iqiyi.vip.zeus.core.config;

import com.alibaba.csp.sentinel.adapter.apache.httpclient.SentinelApacheHttpClientBuilder;
import com.alibaba.csp.sentinel.adapter.apache.httpclient.config.SentinelApacheHttpClientConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: guojing
 * @date: 2023/11/15 10:33
 */
@Configuration
public class RestTemplateConfig {

    @Lazy(false)
    @Bean(name = {"eagleRestTemplate"})
    public RestTemplate eagleRestTemplate(){
        return getRestTemplate(300, 10000);
    }

    @Lazy(false)
    @Bean(name = {"devOpsRestTemplate"})
    public RestTemplate devOpsRestTemplate(){
        return getRestTemplate(300, 10000);
    }

    @Lazy(false)
    @Bean(name = {"eagleCenterRestTemplate"})
    public RestTemplate eagleCenterRestTemplate(){
        return getRestTemplate(300, 10000);
    }

    @Lazy(false)
    @Bean(name = {"smartAlertRestTemplate"})
    public RestTemplate smartAlertRestTemplate(){
        return getRestTemplate(300, 10000);
    }

    @Lazy(false)
    @Bean(name = {"prometheusQueryRestTemplate"})
    public RestTemplate prometheusQueryRestTemplate(){
        return getRestTemplate(500, 30000);
    }

    @Lazy(false)
    @Bean(name = {"payCenterDutAccountRestTemplate"})
    public RestTemplate payCenterDutAccountRestTemplate(){
        return getRestTemplate(300, 3000);
    }

    private RestTemplate getRestTemplate(int connectTimeout, int readTimeout) {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClientBuilder().build());
        // 连接超时时间/毫秒
        requestFactory.setConnectTimeout(connectTimeout);
        // 读写超时时间/毫秒
        requestFactory.setReadTimeout(readTimeout);
        // 请求超时时间/毫秒
        requestFactory.setConnectionRequestTimeout(1000);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
//        restTemplate.getInterceptors().add(new RestTemplateLoggingRequestInterceptor());
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    private HttpClientBuilder httpClientBuilder() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        // 整个连接池最大连接数
        poolingConnectionManager.setMaxTotal(50);
        // 设定默认单个路由的最大连接数
        poolingConnectionManager.setDefaultMaxPerRoute(50);
        // 检查有效连接的间隔
        poolingConnectionManager.setValidateAfterInactivity(3000);

        SentinelApacheHttpClientConfig config = new SentinelApacheHttpClientConfig();
        config.setExtractor(request -> request.getURI().getPath());
        HttpClientBuilder httpClientBuilder = new SentinelApacheHttpClientBuilder(config);
        httpClientBuilder.setConnectionManager(poolingConnectionManager);
        return httpClientBuilder;
    }

    private List<HttpMessageConverter<?>> transConverters(List<HttpMessageConverter<?>> oldMessageConverters) {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.TEXT_HTML);
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE + ";UTF-8"));
        jacksonConverter.setSupportedMediaTypes(fastMediaTypes);

        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        for (HttpMessageConverter<?> converter : oldMessageConverters) {
            if (converter instanceof StringHttpMessageConverter) {
                messageConverters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
            } else if (converter instanceof MappingJackson2HttpMessageConverter) {
                messageConverters.add(jacksonConverter);
            } else {
                messageConverters.add(converter);
            }
        }
        return messageConverters;
    }

}
