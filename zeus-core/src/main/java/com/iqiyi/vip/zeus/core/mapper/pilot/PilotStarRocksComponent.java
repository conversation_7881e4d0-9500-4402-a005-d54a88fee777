package com.iqiyi.vip.zeus.core.mapper.pilot;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.iqiyi.bigdata.pilot.rpc.client.TPilotStatement;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.zeus.core.po.PilotQueryResult;

/**
 * <AUTHOR>
 * @date 2022/3/23 上午 10:26
 */
@Component
@Slf4j
@Data
public class PilotStarRocksComponent {

    @ConfigJsonValue("${pilot.starrocks.conn.conf:{\"hadoopUser\":\"boss\",\"cluster\":\"sr-bdxs-viporder\",\"token\":\"a2ae1c2819be5743dbd63ed924caf7aecbc3eab2\"}}")
    private Map<String, Object> pilotStarRocksConnConf;

    @Value("${pilot.starrocks.conn.url:*******************************************************************************************************;}")
    private String pilotStarRocksConnUrl;

    public List<LinkedHashMap<String, Object>> query(String querySql) {
        try (Connection connection = createConnection()) {
            return executeQuery(connection, querySql);
        } catch (SQLException sqlException) {
            log.error("query sql exception, querySql:{}", querySql, sqlException);
            return Collections.emptyList();
        }
    }

    private Connection createConnection() throws SQLException {
        Properties properties = new Properties();
        properties.putAll(pilotStarRocksConnConf);
        log.info("use StarRocks conn config from cloudConfig:{}", pilotStarRocksConnConf);
        return DriverManager.getConnection(pilotStarRocksConnUrl, properties);
    }

    private List<LinkedHashMap<String, Object>> executeQuery(Connection conn, String querySql) {
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        try (TPilotStatement statement = (TPilotStatement) conn.createStatement()) {
            statement.setFetchDuration(500);
            statement.setQueryTimeout(60);
            try (ResultSet resultSet = statement.executeQuery(querySql)) {
                if (resultSet == null) {
                    return dataList;
                }
                ResultSetMetaData rsMetaData = resultSet.getMetaData();
                int columnCount = rsMetaData != null ? rsMetaData.getColumnCount() : 0;
                if (columnCount == 0) {
                    return dataList;
                }
                List<String> columnNames = new ArrayList<>(columnCount);
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(rsMetaData.getColumnName(i));
                }
                while (resultSet.next()) {
                    LinkedHashMap<String, Object> rowDataMap = new LinkedHashMap<>();
                    for (String columnName : columnNames) {
                        rowDataMap.put(columnName, resultSet.getObject(columnName));
                    }
                    dataList.add(rowDataMap);
                }
            }
            log.info("executeQuery result size:{}, querySql:{}", dataList.size(), querySql);
        } catch (Exception e) {
            log.error("executeQuery failed，querySql:{}, error:", querySql, e);
        }
        return dataList;
    }

    public PilotQueryResult queryNew(String querySql) {
        try (Connection connection = createConnection()) {
            return executeQueryNew(connection, querySql);
        } catch (SQLException sqlException) {
            log.error("query sql exception, querySql:{}", querySql, sqlException);
            return null;
        }
    }

    private PilotQueryResult executeQueryNew(Connection conn, String querySql) {
        List<String> columnNames = new ArrayList<>();
        List<List<Object>> rowDataList = new ArrayList<>();
        try (TPilotStatement statement = (TPilotStatement) conn.createStatement()) {
            statement.setFetchDuration(500);
            statement.setQueryTimeout(60);
            try (ResultSet resultSet = statement.executeQuery(querySql)) {
                if (resultSet == null) {
                    return null;
                }
                ResultSetMetaData rsMetaData = resultSet.getMetaData();
                int columnCount = rsMetaData != null ? rsMetaData.getColumnCount() : 0;
                if (columnCount == 0) {
                    return null;
                }
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(rsMetaData.getColumnName(i));
                }
                while (resultSet.next()) {
                    List<Object> oneRowData = new ArrayList<>();
                    for (String columnName : columnNames) {
                        oneRowData.add(resultSet.getObject(columnName));
                    }
                    rowDataList.add(oneRowData);
                }
            }
            log.info("executeQueryNew result size:{}, querySql:{}", rowDataList.size(), querySql);
        } catch (Exception e) {
            log.error("executeQueryNew failed，querySql:{}, error:", querySql, e);
        }
        return new PilotQueryResult(columnNames, rowDataList);
    }

}
