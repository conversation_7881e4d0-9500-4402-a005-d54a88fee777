package com.iqiyi.vip.zeus.core.mapper.zeus;

import com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO;

import java.util.List;

public interface OrderGuardMonitorMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(OrderGuardMonitorPO record);

    int updateByPrimaryKey(OrderGuardMonitorPO record);

    OrderGuardMonitorPO selectByPrimaryKey(Integer id);

    List<OrderGuardMonitorPO> selectAll();

}