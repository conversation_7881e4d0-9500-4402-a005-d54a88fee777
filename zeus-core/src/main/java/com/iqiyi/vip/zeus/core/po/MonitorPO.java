package com.iqiyi.vip.zeus.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorPO {

    private Integer id;

    private String name;
    /**
     * 监控种类
     */
    private Integer category;

    private Integer datasourceId;

    private String query;

    private String teamCode;

    private String createUser;

    private String updateUser;

    private String dashboardUid;

    private Integer panelId;

    private Integer status;

    private String extraData;

    private Timestamp createTime;

    private Timestamp updateTime;

}