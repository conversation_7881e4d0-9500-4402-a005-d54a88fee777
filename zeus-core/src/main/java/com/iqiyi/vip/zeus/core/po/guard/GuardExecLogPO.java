package com.iqiyi.vip.zeus.core.po.guard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 稽核项执行记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardExecLogPO {
    
    /**
     * 自增主键ID
     */
    private Long id;
    
    /**
     * 执行日期
     */
    private LocalDate day;
    
    /**
     * 稽核项ID
     */
    private Integer guardItemId;
    
    /**
     * 关联的执行结果表ID
     */
    private Long execResultId;
    
    /**
     * 执行时间
     */
    private LocalDateTime execTime;
    
    /**
     * 执行的校验SQL
     */
    private String checkSql;
    
    /**
     * 执行的明细SQL
     */
    private String detailSql;
    
    /**
     * 执行状态 0：失败 1：成功
     */
    private Integer status;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
