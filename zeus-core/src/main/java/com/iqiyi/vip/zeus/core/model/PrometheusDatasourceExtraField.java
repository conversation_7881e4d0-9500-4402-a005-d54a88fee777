package com.iqiyi.vip.zeus.core.model;

import lombok.Data;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/11/25 15:47
 */
@Data
public class PrometheusDatasourceExtraField {

    private String defaultEditor = "code";
    private String httpMethod = "POST";
    private String prometheusType = "prometheus";
    private String timeInterval = "30s";

    public Map<String, Object> getExtraData() {
        Map<String, Object> extraData = new LinkedHashMap<>();
        extraData.put("defaultEditor", defaultEditor);
        extraData.put("httpMethod", httpMethod);
        extraData.put("prometheusType", prometheusType);
        extraData.put("timeInterval", timeInterval);
        return extraData;
    }

    public Map<String, Object> getSensitiveExtraData() {
        return Collections.emptyMap();
    }

    public static List<FieldConfigMeta> getExtraFieldMeta() {
        return Collections.emptyList();
    }

}
