package com.iqiyi.vip.zeus.core.enums;

/**
 * <AUTHOR>
 * @date 2024/4/8 13:12
 */
public enum ExpiringDataEnum {
    QIYUE_RULE(0),
    PACKAGE(1),
    STORE_CONFIG(2),
    STORE_SWITCH(3),
    PAY_TYPE(4),
    POINTS(5),
    BUNDLE(6),
    SMART_STORE(7),
    PAY_CHANNEL_MARKETING(8),
    PAY_TYPE_MARKETING(9),
    GIFT(10),
    ;

    private Integer value;

    ExpiringDataEnum(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }
}
