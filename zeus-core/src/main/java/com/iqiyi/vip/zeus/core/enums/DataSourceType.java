package com.iqiyi.vip.zeus.core.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import com.iqiyi.vip.zeus.core.model.FieldConfigMeta;

/**
 * 宙斯数据源类型
 *
 * @author: guojing
 * @date: 2023/11/25 14:15
 */
public enum DataSourceType {

    Prometheus("Prometheus", true) {
        @Override
        public String panelTargetJsonTmpResourcePath() {
            return "tmp/panel-target-prometheus.json";
        }
    },
    MySQL("MySQL", true) {
        @Override
        public List<FieldConfigMeta> getDSFieldConfigMeta() {
            FieldConfigMeta databaseField = FieldConfigMeta.newStringField("数据库", "database", "请输入数据库名称", true);
            FieldConfigMeta usernameField = FieldConfigMeta.newStringField("用户名", "user", "请输入用户名", true);
            FieldConfigMeta passwordField = FieldConfigMeta.newStringSensitiveField("密码", "password", "请输入密码", true);
            return Arrays.asList(databaseField, usernameField, passwordField);
        }

        @Override
        public String panelTargetJsonTmpResourcePath() {
            return "tmp/panel-target-mysql.json";
        }
    },
    ;

    private String value;
    /**
     * 是否支持推送到鹰眼
     */
    private boolean supportPushToEagle;
    /**
     * 对应Grafana的数据源类型
     */
    private String grafanaDatasourceType;


    DataSourceType(String value, Boolean supportPushToEagle) {
        this.value = value;
        this.supportPushToEagle = supportPushToEagle;
    }

    DataSourceType(String value, boolean supportPushToEagle, String grafanaDatasourceType) {
        this.value = value;
        this.supportPushToEagle = supportPushToEagle;
        this.grafanaDatasourceType = grafanaDatasourceType;
    }

    public String getValue() {
        return value;
    }

    public boolean supportPushToEagle() {
        return supportPushToEagle;
    }

    public String getGrafanaDatasourceType() {
        return grafanaDatasourceType == null ? value.toLowerCase() : grafanaDatasourceType;
    }

    private static final HashMap<String, DataSourceType> map = new HashMap<>();

    static {
        for (DataSourceType enumType : DataSourceType.values()) {
            map.put(enumType.name(), enumType);
        }
    }

    public static DataSourceType parseValue(String value) {
        return map.getOrDefault(value, null);
    }

    /**
     * 获取数据源配置字段元数据
     */
    public List<FieldConfigMeta> getDSFieldConfigMeta() {
        return Collections.emptyList();
    }

    /**
     * Panel Target节点模板的资源路径
     */
    public String panelTargetJsonTmpResourcePath() {
        return null;
    }

}
