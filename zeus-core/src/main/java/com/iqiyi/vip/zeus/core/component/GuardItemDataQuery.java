package com.iqiyi.vip.zeus.core.component;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.GuardDataQueryType;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;

/**
 * @author: guojing
 * @date: 2025/5/26 18:39
 */
public interface GuardItemDataQuery {

    /**
     * 获取数据源查询类型
     */
    GuardDataQueryType getGuardDataQueryType();

    /**
     * 查询数据
     * 
     * @param querySql
     * @param startDay
     * @param endDay
     */
    Map<String, OrderGuardianQueryData> queryData(GuardDatasource datasource, String querySql, String startDay, String endDay);

    /**
     * 查询明细数据
     * @param datasource
     * @param detailSql
     * @param queryDay
     */
    OrderGuardianDetailData queryDetailData(GuardDatasource datasource, String detailSql, String queryDay);

}
