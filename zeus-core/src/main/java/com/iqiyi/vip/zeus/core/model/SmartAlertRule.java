package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;
import com.iqiyi.vip.zeus.core.po.SmartAlertRulePO;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "智能告警规则")
public class SmartAlertRule {

    public static final String DEFAULT_DURATION = "2m";
    public static final String DEFAULT_CHECK_FREQUENCY = "30s";

    @ApiModelProperty(value = "id")
    private Integer id;
    @ApiModelProperty(value = "监控id")
    private Integer monitorId;
    @ApiModelProperty(value = "规则名称")
    private String ruleName;
    /**
     * 持续异常触发报警时间，单位秒
     */
    @ApiModelProperty(value = "持续时间，单位:秒")
    private String duration;
    /**
     * 执行周期，单位秒
     */
    @ApiModelProperty(value = "检测频率，单位:秒")
    private String checkFrequency;
    /**
     * 告警接收人
     */
    @ApiModelProperty(value = "告警接收人")
    private String receivers;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "扩展数据")
    private Map<String, Object> extraData;
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    public static SmartAlertRule buildFromZeusRule(ZeusAlertRule rule) {
        if (rule == null) {
            return null;
        }
        return SmartAlertRule.builder()
            .id(rule.getId())
            .monitorId(rule.getMonitorId())
            .ruleName(rule.getSmartAlertRule())
            .duration(rule.getDuration())
            .checkFrequency(rule.getCheckFrequency())
            .receivers(rule.getReceivers())
            .status(rule.getStatus())
            .extraData(rule.getExtraData())
            .createTime(rule.getCreateTime())
            .updateTime(rule.getUpdateTime())
            .build();
    }

    public static SmartAlertRule buildFromPO(SmartAlertRulePO po) {
        if (po == null) {
            return null;
        }
        return SmartAlertRule.builder()
            .id(po.getId())
            .monitorId(po.getMonitorId())
            .ruleName(po.getRuleName())
            .duration(OffsetTimeUnit.toOffsetTimeUnitName(po.getDuration()))
            .checkFrequency(OffsetTimeUnit.toOffsetTimeUnitName(po.getCheckFrequency()))
            .receivers(po.getReceivers())
            .status(po.getStatus())
            .extraData(JacksonUtils.parseMap(po.getExtraData()))
            .createTime(po.getCreateTime())
            .updateTime(po.getUpdateTime())
            .build();
    }

    public static SmartAlertRulePO buildToPO(SmartAlertRule rule) {
        if (rule == null) {
            return null;
        }
        return SmartAlertRulePO.builder()
            .id(rule.getId())
            .monitorId(rule.getMonitorId())
            .ruleName(rule.getRuleName())
            .duration(OffsetTimeUnit.toSeconds(rule.getDuration()))
            .checkFrequency(OffsetTimeUnit.toSeconds(rule.getCheckFrequency()))
            .receivers(rule.getReceivers())
            .status(rule.getStatus())
            .extraData(JacksonUtils.toJsonString(rule.getExtraData()))
            .createTime(rule.getCreateTime())
            .updateTime(rule.getUpdateTime())
            .build();
    }

}
