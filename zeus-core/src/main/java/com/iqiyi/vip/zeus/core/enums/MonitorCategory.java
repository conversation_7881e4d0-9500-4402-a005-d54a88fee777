package com.iqiyi.vip.zeus.core.enums;

import java.util.HashMap;

/**
 * 监控类型
 * @author: guojing
 * @date: 2023/11/29 14:32
 */
public enum MonitorCategory {

    DATA_TREND(1, "数据趋势类"),
    DATA_CONSISTENCY(2, "数据一致类"),
    DATA_ACCURACY(3, "数据准确类"),
    ;

    private Integer value;
    private String desc;

    MonitorCategory(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    private static final HashMap<Integer, MonitorCategory> map = new HashMap<>();

    static {
        for (MonitorCategory enumType : MonitorCategory.values()) {
            map.put(enumType.getValue(), enumType);
        }
    }

    public static MonitorCategory parseValue(Integer value) {
        return map.getOrDefault(value, null);
    }

}
