package com.iqiyi.vip.zeus.core.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.iqiyi.vip.zeus.core.constants.GrafanaSQLConstants;

/**
 * @author: guojing
 * @date: 2024/1/26 18:27
 */
public class SQLParseUtils {

    private static final Pattern SELECT_SQL_PATTERN = Pattern.compile("^\\s*SELECT\\s+(.+?)\\s+FROM\\s+",
        Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    public static String parseSelectSQL(String sql) {
        Matcher matcher = SELECT_SQL_PATTERN.matcher(sql);
        if (!matcher.find()) {
            return null;
        }
        return matcher.group(1).replace("\n", "");
    }

    public static boolean isSelectSQL(String sql) {
        return StringUtils.startsWithIgnoreCase(sql, GrafanaSQLConstants.SELECT);
    }

}
