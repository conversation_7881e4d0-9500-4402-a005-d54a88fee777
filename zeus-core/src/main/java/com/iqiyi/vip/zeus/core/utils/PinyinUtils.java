package com.iqiyi.vip.zeus.core.utils;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;

/**
 * 汉字转拼音
 * @author: guojing
 * @date: 2024/11/22 11:49
 */
@Slf4j
public class PinyinUtils {

    static HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();

    private static final String HAN_ZI_REGEX = "[\\u4E00-\\u9FA5]+";

    static {
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        defaultFormat.setVCharType(HanyuPinyinVCharType.WITH_V);
    }

    public static String toPinYin(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return null;
        }

        StringBuilder pinyin = new StringBuilder();
        char[] charArray = chinese.toCharArray();
        try {
            for (int i = 0; i < charArray.length; i++) {
                // 判断为中文,则转换为汉语拼音
                if (String.valueOf(charArray[i]).matches(HAN_ZI_REGEX)) {
                    pinyin.append(PinyinHelper.toHanyuPinyinStringArray(charArray[i], defaultFormat)[0]);
                } else {
                    // 不为中文,则不转换
                    pinyin.append(charArray[i]);
                }

            }
            return pinyin.toString();
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            log.error("toPinYin error, chinese:{}", chinese, e);
            return null;
        }
    }

}
