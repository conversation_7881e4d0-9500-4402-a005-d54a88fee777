package com.iqiyi.vip.zeus.core.exception;

import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

import com.iqiyi.vip.zeus.eagleclient.CodeEnum;

/**
 * Created at: 2021-06-21
 *
 * <AUTHOR>
 */
@ToString(callSuper = true)
@Getter
public class BizException extends RuntimeException {

    private String code;

    public BizException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public BizException(CodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }

    public static BizException newParamException(String msg) {
        return new BizException(CodeEnum.PARAM_ERROR.getCode(), msg);
    }

    public static BizException newSystemException(String msg) {
        return new BizException(CodeEnum.SYSTEM_ERROR.getCode(), msg);
    }

    public static BizException newException(CodeEnum codeEnum) {
        return new BizException(codeEnum);
    }

    public static BizException newException(CodeEnum codeEnum, String msg) {
        return new BizException(codeEnum.getCode(), msg);
    }

    public static BizException newException(String code, String errorMsg) {
        return new BizException(code, errorMsg);
    }

    public boolean isSystemException() {
        return Objects.equals(CodeEnum.SYSTEM_ERROR.getCode(), code);
    }

    public boolean isParamException() {
        return Objects.equals(CodeEnum.PARAM_ERROR.getCode(), code);
    }

}
