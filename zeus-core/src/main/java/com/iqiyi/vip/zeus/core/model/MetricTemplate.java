package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.PrometheusMetricType;
import com.iqiyi.vip.zeus.core.po.zeus.MetricTemplatePO;

/**
 * 监控实体类
 * @author: guojing
 * @date: 2023/12/2 16:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("指标模版信息")
public class MetricTemplate {

    private static final String GROUP_BY_VARIABLE = "${groupBy}";

    /**
     * 指标模版id
     */
    @ApiModelProperty(value = "指标模版")
    private Integer id;
    /**
     * 指标模版名称
     */
    @ApiModelProperty(value = "指标模版名称")
    private String name;
    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型")
    private String datasourceType;
    /**
     * 指标模版类型
     */
    @ApiModelProperty(value = "指标模版类型")
    private Integer type;
    /**
     * Prometheus指标类型
     * @see PrometheusMetricType
     */
    @ApiModelProperty(value = "Prometheus指标类型, 取值：Counter、Gauge")
    private String metricType;
    /**
     * 模版内容
     */
    @ApiModelProperty(value = "模版内容")
    private String content;
    /**
     * 是否需要时间范围字段
     */
    @ApiModelProperty(value = "是否需要时间范围字段")
    private boolean needTimeFilter;
    /**
     * 是否支持分组
     */
    @ApiModelProperty(value = "是否支持分组")
    private boolean needGroupBy;
    /**
     * 是否需要展示说明
     */
    @ApiModelProperty(value = "是否需要展示说明")
    private boolean needDisplayName;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String description;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

    public boolean invalid() {
        return status != null && status == 0;
    }

    public MetricTemplatePO toMetricTemplatePO() {
        return MetricTemplatePO.builder()
            .id(id)
            .name(name)
            .datasourceType(datasourceType)
            .type(type)
            .metricType(metricType)
            .content(content)
            .description(description)
            .createUser(createUser)
            .updateUser(updateUser)
            .status(status)
            .createTime(createTime)
            .updateTime(updateTime)
            .build();
    }

    public static MetricTemplate buildFrom(MetricTemplatePO metricTemplatePO) {
        if (metricTemplatePO == null) {
            return null;
        }

        boolean needGroupBy = metricTemplatePO.getContent().contains(GROUP_BY_VARIABLE);
        boolean needDisplayName = DataSourceType.Prometheus.getValue().equals(metricTemplatePO.getDatasourceType());
        boolean needTimeFilter = DataSourceType.MySQL.getValue().equals(metricTemplatePO.getDatasourceType());
        return MetricTemplate.builder()
            .id(metricTemplatePO.getId())
            .name(metricTemplatePO.getName())
            .datasourceType(metricTemplatePO.getDatasourceType())
            .type(metricTemplatePO.getType())
            .metricType(metricTemplatePO.getMetricType())
            .content(metricTemplatePO.getContent())
            .needTimeFilter(needTimeFilter)
            .needGroupBy(needGroupBy)
            .needDisplayName(needDisplayName)
            .description(metricTemplatePO.getDescription())
            .createUser(metricTemplatePO.getCreateUser())
            .updateUser(metricTemplatePO.getUpdateUser())
            .status(metricTemplatePO.getStatus())
            .createTime(metricTemplatePO.getCreateTime())
            .updateTime(metricTemplatePO.getUpdateTime())
            .build();
    }

}
