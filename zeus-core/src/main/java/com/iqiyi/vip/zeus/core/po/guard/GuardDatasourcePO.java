package com.iqiyi.vip.zeus.core.po.guard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 数据源实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardDatasourcePO {
    
    /**
     * 自增主键id
     */
    private Integer id;
    
    /**
     * 数据源名称
     */
    private String name;
    
    /**
     * 数据源类型
     */
    private String type;
    
    /**
     * 数据源连接url
     */
    private String connUrl;
    
    /**
     * 连接配置(JSON格式)
     */
    private String connConfig;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    /**
     * 状态,0:无效;1:有效
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
