package com.iqiyi.vip.zeus.core.model;

import lombok.Data;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/11/25 18:00
 */
@Data
public class RocketMQDatasourceExtraField {

    /**
     * topic
     */
    private String topic;
    /**
     * 消费组
     */
    private String consumerGroup;
    /**
     * 消费者token
     */
    private String consumerToken;

    public Map<String, Object> getExtraData() {
        Map<String, Object> extraData = new LinkedHashMap<>();
        extraData.put("topic", topic);
        extraData.put("consumerGroup", consumerGroup);
        return extraData;
    }

    public Map<String, Object> getSensitiveExtraData() {
        Map<String, Object> extraData = new LinkedHashMap<>();
        extraData.put("consumerToken", consumerToken);
        return extraData;
    }

    public static List<FieldConfigMeta> getExtraFieldMeta() {
        FieldConfigMeta topicField = FieldConfigMeta.newStringField("Topic", "topic", "请输入topic", true);
        FieldConfigMeta consumerGroupField = FieldConfigMeta.newStringField("消费组", "consumerGroup", "请输入消费组", true);
        FieldConfigMeta consumerTokenField = FieldConfigMeta.newStringSensitiveField("消费者token", "consumerToken", "请输入消费者token", true);
        return Arrays.asList(topicField, consumerGroupField, consumerTokenField);
    }

}
