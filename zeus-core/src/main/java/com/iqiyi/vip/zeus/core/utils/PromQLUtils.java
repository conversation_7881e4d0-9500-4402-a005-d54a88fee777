package com.iqiyi.vip.zeus.core.utils;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.constants.GrafanaSQLConstants;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MonitorConditionOperator;
import com.iqiyi.vip.zeus.core.enums.ValueType;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQueryCondition;
import com.iqiyi.vip.zeus.eagleclient.api.PrometheusRecordingRuleApi;
import com.iqiyi.vip.zeus.utils.HashUtils;

/**
 * @author: guojing
 * @date: 2023/12/21 14:44
 */
public class PromQLUtils {

    /**
     * 生成PromQL
     * @param metricTemplate
     * @param query
     */
    public static String genPromQL(MetricTemplate metricTemplate, ZeusMonitorQuery query) {
        List<String> conditionExprList = new ArrayList<>();
        for (ZeusMonitorQueryCondition condition : query.getConditions()) {
            MonitorConditionOperator conditionOperator = MonitorConditionOperator.parseValue(condition.getOperator());
            ValueType valueType = ValueType.STRING;
            String expr = conditionOperator.buildExpr(DataSourceType.Prometheus, condition.getKey(), condition.getValues(), valueType);
            conditionExprList.add(expr);
        }
        String labels = String.join(GrafanaSQLConstants.COMMA, conditionExprList);

        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("metric", query.getSource());
        valueMap.put("labels", labels);
        if (metricTemplate.isNeedGroupBy()) {
            valueMap.put("groupBy", query.getGroupBy());
        }
        StringSubstitutor sub = new StringSubstitutor(valueMap);
        return sub.replace(metricTemplate.getContent());
    }

    /**
     * 生成Grafana面板的LegendFormat
     * @param query
     */
    public static String buildLegendFormat(ZeusMonitorQuery query) {
        String displayName = query.getDisplayName();
        String groupBy = query.getGroupBy();
        if (StringUtils.isBlank(displayName) && StringUtils.isBlank(groupBy)) {
            return null;
        }
        if (StringUtils.isNotBlank(groupBy)) {
            groupBy = Arrays.stream(groupBy.split(","))
                .map(item -> "{{" + item.trim() + "}}")
                .collect(Collectors.joining("-"));
        }
        return StringUtils.isNotBlank(displayName)
            ? displayName + (StringUtils.isNotBlank(groupBy) ? "-" + groupBy : "")
            : groupBy;
    }

    /**
     * 生成MySQL语句
     * @param metricTemplate
     * @param query
     */
    public static String genMySQL(MetricTemplate metricTemplate, ZeusMonitorQuery query) {
        List<String> conditionExprList = new ArrayList<>();
        for (ZeusMonitorQueryCondition condition : query.getConditions()) {
            MonitorConditionOperator conditionOperator = MonitorConditionOperator.parseValue(condition.getOperator());
            ValueType valueType = ValueType.parseValue(condition.getValueType());
            String expr = conditionOperator.buildExpr(DataSourceType.MySQL, condition.getKey(), condition.getValues(), valueType);
            conditionExprList.add(expr);
        }
        String conditionExpr = String.join(GrafanaSQLConstants.AND, conditionExprList);

        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("table", query.getSource());
        valueMap.put("conditions", conditionExpr);
        if (metricTemplate.isNeedTimeFilter()) {
            valueMap.put("timeColumn", query.getTimeFilter());
        }
        if (metricTemplate.isNeedGroupBy()) {
            valueMap.put("groupBy", query.getGroupBy());
        }
        StringSubstitutor sub = new StringSubstitutor(valueMap);
        return sub.replace(metricTemplate.getContent());
    }

    /**
     * 根据PromQL生成Record Name，带vip_xuanwu:前缀
     * @param monitorQuery
     * @param promQL
     */
    public static String genRecordName(ZeusMonitorQuery monitorQuery, String promQL) {
        String metricName = monitorQuery.getSource();
        String groupBy = monitorQuery.getGroupBy();
        List<String> conditionKeys = monitorQuery.getConditions().stream().map(ZeusMonitorQueryCondition::getKey).collect(Collectors.toList());
        StringBuilder recordNameBuilder = new StringBuilder(PrometheusRecordingRuleApi.RECORD_NAME_PREFIX);
        if (StringUtils.isNotBlank(groupBy)) {
            recordNameBuilder.append(groupBy).append(":");
        }
        recordNameBuilder.append(metricName).append(":");
        recordNameBuilder.append(String.join("_", conditionKeys)).append(":");
        recordNameBuilder.append(HashUtils.shortSHA256(promQL));
        return recordNameBuilder.toString();
    }

}
