package com.iqiyi.vip.zeus.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.component.OrderGuardDataQuery;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.eagleclient.PrometheusQueryClient;
import com.iqiyi.vip.zeus.eagleclient.response.MetricMetadataResponse;
import com.iqiyi.vip.zeus.eagleclient.response.prometheus.PrometheusQueryResp;
import com.iqiyi.vip.zeus.eagleclient.response.prometheus.PrometheusQueryResult;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

/**
 * @author: guojing
 * @date: 2025/5/23 16:28
 */
@Slf4j
@Component
public class PrometheusQueryService implements OrderGuardDataQuery {

    /**
     * 范围查询API
     * Prometheus 查询指标数据API：
     * @see <a href="https://prometheus.io/docs/prometheus/latest/querying/api/#range-queries">Prometheus Range Queries</a>
     */
    private static final String RANGE_QUERY_API = "/api/v1/query_range";
    private static final String QUERY_API = "/api/v1/query";

    @Value("${prometheus.query.domain:http://mimir-ui.qiyi.domain/vip-eagle}")
    private String prometheusQueryDomain;

    @Resource
    private PrometheusQueryClient prometheusQueryClient;

    @Override
    public GuardDatasourceType getDatasourceType() {
        return GuardDatasourceType.Prometheus;
    }

    @Override
    public Map<String, OrderGuardianQueryData> queryData(String querySql, String startDay, String endDay) {
        Map<String, String> params = new HashMap<>();
        params.put("query", querySql);
        params.put("start", startDay + "T23:59:59+08:00");
        params.put("end", endDay + "T23:59:59+08:00");
        params.put("step", "1d");
        MetricMetadataResponse<PrometheusQueryResp> response = prometheusQueryClient.doPost(prometheusQueryDomain, RANGE_QUERY_API, params);
        if (response.returnFail() || response.getData() == null) {
            return Collections.emptyMap();
        }
        PrometheusQueryResp queryResp = response.getData();
        if (CollectionUtils.isEmpty(queryResp.getResult())) {
            return Collections.emptyMap();
        }
        PrometheusQueryResult prometheusQueryResult = queryResp.getResult().get(0);
        if (CollectionUtils.isEmpty(prometheusQueryResult.getValues())) {
            return Collections.emptyMap();
        }

        Map<String, OrderGuardianQueryData> dataMap = new HashMap<>();
        List<List<String>> metricValues = prometheusQueryResult.getValues();
        for (List<String> item : metricValues) {
            if (item.size() < 2) {
                continue;
            }

            String day = CommonDateUtils.secondsToDateStr(Long.parseLong(item.get(0)));
            Double count = NumberUtils.createDouble(item.get(1));
            if (count != null) {
                count = Math.ceil(count);
            }
            dataMap.put(day, new OrderGuardianQueryData(day, count));
        }
        return dataMap;
    }

    @Override
    public OrderGuardianDetailData queryDetailData(String detailSql, String queryDay) {
        Map<String, String> params = new HashMap<>();
        params.put("query", detailSql);
        params.put("time", queryDay + "T23:59:59+08:00");
        MetricMetadataResponse<PrometheusQueryResp> response = prometheusQueryClient.doPost(prometheusQueryDomain, QUERY_API, params);
        if (response.returnFail() || response.getData() == null) {
            return null;
        }
        PrometheusQueryResp queryResp = response.getData();
        if (CollectionUtils.isEmpty(queryResp.getResult())) {
            return null;
        }
        List<PrometheusQueryResult> prometheusQueryResults = queryResp.getResult();
        if (CollectionUtils.isEmpty(prometheusQueryResults)) {
            return null;
        }

        
        List<LinkedHashMap<String, Object>> queryResult = new ArrayList<>();
        for (PrometheusQueryResult prometheusQueryResult : prometheusQueryResults) {
            if (MapUtils.isEmpty(prometheusQueryResult.getMetric())) {
                continue;
            }
            String metricValueStr = prometheusQueryResult.getValue().get(1);
            String valueStr = StringUtils.isNotBlank(metricValueStr) ? String.valueOf((long) Double.parseDouble(metricValueStr)) : "0";
            if (Objects.equals(valueStr, "0")) {
                continue;
            }

            LinkedHashMap<String, Object> rowData = new LinkedHashMap<>();
            Map<String, Object> metricMap = prometheusQueryResult.getMetric();
            // 将metric下的所有key-value添加到rowData中
            if (MapUtils.isEmpty(metricMap)) {
                rowData.put("column", "total");
            } else {
                rowData.putAll(metricMap);
            }
            rowData.put("value", valueStr);
            queryResult.add(rowData);
        }
        // 按value值倒序排序
        queryResult.sort((o1, o2) -> {
            long value1 = Long.parseLong(String.valueOf(o1.get("value")));
            long value2 = Long.parseLong(String.valueOf(o2.get("value")));
            return Long.compare(value2, value1);
        });
        return new OrderGuardianDetailData(detailSql, queryResult);
    }

    

}
