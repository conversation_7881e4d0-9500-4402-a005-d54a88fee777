package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created at: 2021-02-23
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("字段配置元数据")
public class FieldConfigMeta {

    /**
     * 配置展示名
     */
    @ApiModelProperty(value = "配置展示名")
    private String displayName;
    /**
     * 配置Key
     */
    @ApiModelProperty(value = "配置Key")
    private String key;
    /**
     * 输入类型: 1-文本框输入型,2-下拉单选,3-下拉多选
     */
    @ApiModelProperty(value = "输入类型: 1-文本框输入型,2-下拉单选,3-下拉多选")
    private Integer inputType;
    /**
     * 输入提示
     */
    @ApiModelProperty(value = "输入提示")
    private String placeholder;
    /**
     * 输入值类型: 1-整数 2-字符串
     */
    @ApiModelProperty(value = "输入值类型: 1-整数 2-字符串")
    private Integer valueType;
    /**
     * 默认值
     */
    private Object defaultValue;
    /**
     * 是否必填:true-是 false-否
     */
    @ApiModelProperty(value = "是否必填:true-是 false-否")
    private Boolean required;
    /**
     * 敏感字段:true-是 false-否
     */
    @ApiModelProperty(value = "敏感字段:true-是 false-否")
    private Boolean sensitiveField;
    /**
     * 当为选项型参数时的候选项
     */
    @ApiModelProperty(value = "当为选项型参数时的候选项")
    private List<FieldOption> optionValue;

    @Data
    public static class FieldOption {
        /**
         * 选项显示值
         */
        private String displayName;
        /**
         * 选项选中值
         */
        private String value;

        public FieldOption() {}

        public FieldOption(String displayName, String value) {
            this.displayName = displayName;
            this.value = value;
        }
    }

    public static FieldConfigMeta newNumberField(String displayName, String key, String placeholder, Boolean required) {
        return FieldConfigMeta.builder()
            .displayName(displayName)
            .key(key)
            .inputType(1)
            .placeholder(placeholder)
            .valueType(1)
            .required(required)
            .sensitiveField(false)
            .build();
    }

    /**
     * 生成字符串类型的配置元数据，非敏感字段
     * @param displayName
     * @param key
     * @param placeholder
     * @param required
     */
    public static FieldConfigMeta newStringField(String displayName, String key, String placeholder, Boolean required) {
        return FieldConfigMeta.builder()
            .displayName(displayName)
            .key(key)
            .inputType(1)
            .placeholder(placeholder)
            .valueType(2)
            .required(required)
            .sensitiveField(false)
            .build();
    }

    /**
     * 生成字符串类型的配置元数据，敏感字段
     * @param displayName
     * @param key
     * @param placeholder
     * @param required
     */
    public static FieldConfigMeta newStringSensitiveField(String displayName, String key, String placeholder, Boolean required) {
        return FieldConfigMeta.builder()
            .displayName(displayName)
            .key(key)
            .inputType(1)
            .placeholder(placeholder)
            .valueType(2)
            .required(required)
            .sensitiveField(true)
            .build();
    }

    public static FieldConfigMeta newStringField(String displayName, String key, String placeholder, Boolean required, String defaultValue) {
        return FieldConfigMeta.builder()
            .displayName(displayName)
            .key(key)
            .inputType(1)
            .placeholder(placeholder)
            .valueType(2)
            .required(required)
            .sensitiveField(false)
            .defaultValue(defaultValue)
            .build();
    }

    /**
     * 下拉单选框
     * @param displayName
     * @param key
     * @param placeholder
     * @param required
     */
    public static FieldConfigMeta newSingleSelectField(String displayName, String key, String placeholder, Boolean required, List<FieldOption> optionValues) {
        return FieldConfigMeta.builder()
            .displayName(displayName)
            .key(key)
            .inputType(2)
            .placeholder(placeholder)
            .valueType(1)
            .required(required)
            .sensitiveField(false)
            .optionValue(optionValues)
            .build();
    }

}
