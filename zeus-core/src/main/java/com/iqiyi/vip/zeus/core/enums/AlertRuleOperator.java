package com.iqiyi.vip.zeus.core.enums;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 告警规则条件操作符
 *
 * @author: guojing
 * @date: 2023/12/20 21:02
 */
public enum AlertRuleOperator {

    gt("大于"),
    lt("小于"),
    whthin_range("存在于"),
    outside_range("不存在于"),
    ;

    private String desc;

    AlertRuleOperator(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<String, AlertRuleOperator> map = new LinkedHashMap<>();

    static {
        for (AlertRuleOperator enumType : AlertRuleOperator.values()) {
            map.put(enumType.name(), enumType);
        }
    }

    public static AlertRuleOperator parseValue(String type) {
        return map.getOrDefault(type, null);
    }

}
