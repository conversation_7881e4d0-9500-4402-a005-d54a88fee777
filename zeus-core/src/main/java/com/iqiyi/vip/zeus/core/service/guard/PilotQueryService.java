package com.iqiyi.vip.zeus.core.service.guard;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.component.GuardItemDataQuery;
import com.iqiyi.vip.zeus.core.component.PilotComponent;
import com.iqiyi.vip.zeus.core.enums.GuardDataQueryType;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.core.po.DatasourceQueryResult;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.core.utils.NumberConvertUtils;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

@Slf4j
@Service
public class PilotQueryService implements GuardItemDataQuery {

    @Resource
    private PilotComponent pilotComponent;

    @Override
    public GuardDataQueryType getGuardDataQueryType() {
        return GuardDataQueryType.Pilot;
    }

    @Override
    public Map<String, OrderGuardianQueryData> queryData(GuardDatasource datasource, String querySql, String startDay, String endDay) {
        if (datasource == null || StringUtils.isBlank(querySql)) {
            return Collections.emptyMap();
        }
        GuardDatasourceType dataSourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (dataSourceType == null) {
            log.error("datasource type not found, type: {}, guardDatasource:{}", datasource.getType(), JacksonUtils.toJsonString(datasource));
            return Collections.emptyMap();
        }

        Map<String, String> values = new HashMap<>();
        if (dataSourceType == GuardDatasourceType.Hive) {
            values.put("dt", CommonDateUtils.yesterdayStr());
        } else {
            values.put("startDay", startDay);
            values.put("endDay", endDay);
        }
        values.put("startTime", startDay + " 00:00:00");
        values.put("endTime", endDay + " 23:59:59");
        String finalSql = new StringSubstitutor(values).replace(querySql);
        log.info("pilot query, querySql: {}", finalSql);
        DatasourceQueryResult queryResult = pilotComponent.query(datasource, finalSql);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getRowDataList())) {
            return Collections.emptyMap();
        }

        Map<String, OrderGuardianQueryData> dataMap = new HashMap<>();
        for (List<Object> oneRowData : queryResult.getRowDataList()) {
            if (CollectionUtils.isEmpty(oneRowData) || oneRowData.size() < 2) {
                continue;
            }

            String day = (String) oneRowData.get(0);
            Object countObj = oneRowData.get(1);
            Double count = NumberConvertUtils.convertToDouble(countObj);
            OrderGuardianQueryData dayData = new OrderGuardianQueryData(day, count);
            if (oneRowData.size() >= 3) {
                Object secondCount = oneRowData.get(2);
                if (secondCount == null) {
                    continue;
                }
                if (secondCount instanceof String) {
                    dayData.setSecondCount((String) secondCount);
                } else {
                    dayData.setSecondCount(String.valueOf(secondCount));
                }
            }
            dataMap.put(day, dayData);
        }

        return dataMap;
    }

    @Override
    public OrderGuardianDetailData queryDetailData(GuardDatasource datasource, String detailSql, String queryDay) {
        if (datasource == null || StringUtils.isBlank(detailSql)) {
            return null;
        }
        GuardDatasourceType dataSourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (dataSourceType == null) {
            log.error("datasource type not found, type: {}, guardDatasource:{}", datasource.getType(), JacksonUtils.toJsonString(datasource));
            return null;
        }
        Map<String, String> values = new HashMap<>();
        if (dataSourceType == GuardDatasourceType.Hive) {
            values.put("dt", CommonDateUtils.yesterdayStr());
        } else {
            values.put("queryDay", queryDay);
        }
        values.put("startTime", queryDay + " 00:00:00");
        values.put("endTime", queryDay + " 23:59:59");
        String finalSql = new StringSubstitutor(values).replace(detailSql);
        List<LinkedHashMap<String, Object>> queryResult = pilotComponent.detailQuery(datasource, finalSql);
        return new OrderGuardianDetailData(finalSql, queryResult);
    }

}
