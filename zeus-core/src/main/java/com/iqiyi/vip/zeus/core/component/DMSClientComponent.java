package com.iqiyi.vip.zeus.core.component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.token.client.client.Client;
import com.iqiyi.token.client.util.SSLCipherSuiteUtil;
import com.iqiyi.token.common.domain.Request;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.po.DatasourceQueryResult;
import com.iqiyi.vip.zeus.core.resp.DMSQueryResp;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.eagleclient.CodeEnum;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * DMS OpenAPI查询组件
 * <a href="https://iq.feishu.cn/docx/Du7qdiII8oANDOx8ojwc2vTDnSb">DMS OpenAPI</a>
 * 
 * @author: guojing
 * @date: 2025/09/11 10:00
 */
@Slf4j
@Component
public class DMSClientComponent {

    private static final String DOMAIN_KEY = "domain";
    private static final String SQL_KEY = "sql";

    private static final String FIELD_KEY = "field";

    @Value("${vip_system.access.key:scjt4n7y2juditfz3ybwa6dzckdq8bdk}")
    private String vipSystemAccessKey;
    @Value("${vip_system.secret.key:d2kj6obxndmueqkjm6tixe7p5amrq98n}")
    private String vipSystemSecretKey;
    @Value("${dsm.query.url:http://dms.cloud.qiyi.domain/api/v1/dms/selectQuery/executeQueryByAKSK}")
    private String dmsQueryUrl;

    /**
     * 查询数据
     * @param datasource
     * @param querySql
     */
    public DatasourceQueryResult query(GuardDatasource datasource, String querySql) {
        DMSQueryResp queryResp = doQuery(datasource, querySql);
        if (queryResp == null || CollectionUtils.isEmpty(queryResp.getColumns())) {
            return null;
        }

        List<LinkedHashMap<String, Object>> respDataList = queryResp.getData();
        List<LinkedHashMap<String, Object>> respColumns = queryResp.getColumns();
        List<String> columnNames = respColumns.stream()
            .map(columnMetadataMap -> MapUtils.getString(columnMetadataMap, FIELD_KEY))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(respDataList)) {
            return new DatasourceQueryResult(columnNames, Collections.emptyList());
        }

        List<List<Object>> rowDataList = new ArrayList<>();
        for (LinkedHashMap<String, Object> oneRowDataMap : respDataList) {
            List<Object> oneRowData = new ArrayList<>();
            for (String columnName : columnNames) {
                oneRowData.add(MapUtils.getObject(oneRowDataMap, columnName));
            }
            rowDataList.add(oneRowData);
        }
        return new DatasourceQueryResult(columnNames, rowDataList);
    }

    /**
     * 查询明细数据
     * @param datasource
     * @param querySql
     */
    public List<LinkedHashMap<String, Object>> queryDetail(GuardDatasource datasource, String querySql) {
        DMSQueryResp queryResp = doQuery(datasource, querySql);
        if (queryResp == null || CollectionUtils.isEmpty(queryResp.getData())) {
            return Collections.emptyList();
        }

        return queryResp.getData().stream()
            .map(rowDataMap -> {
                LinkedHashMap<String, Object> orderedRowData = new LinkedHashMap<>();
                queryResp.getColumns().stream()
                    .map(columnMetadataMap -> MapUtils.getString(columnMetadataMap, FIELD_KEY))
                    .filter(StringUtils::isNotBlank)
                    .forEach(columnName -> 
                        orderedRowData.put(columnName, MapUtils.getObject(rowDataMap, columnName))
                    );
                return orderedRowData;
            })
            .collect(Collectors.toList());
    }

    /**
     * 请求DMS OpenAPI接口查询数据
     * @param guardDatasource
     * @param checkSql
     */
    private DMSQueryResp doQuery(GuardDatasource guardDatasource, String checkSql) {
        if (guardDatasource == null || StringUtils.isBlank(checkSql)) {
            return null;
        }
        CloseableHttpClient client = null;
        try {
            String requestBody = buildRequestBody(guardDatasource.getConnUrl(), guardDatasource.getConnConfig(), checkSql);
            if (StringUtils.isBlank(requestBody)) {
                return null;
            }
            Request internalRequest = new Request();
            // 填入申请到的AK/SK
            internalRequest.setKey(vipSystemAccessKey);
            internalRequest.setSecret(vipSystemSecretKey);
            internalRequest.setMethod("POST");
            internalRequest.setUrl(dmsQueryUrl);
            // 填入请求体
            internalRequest.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
            internalRequest.setBody(requestBody);

            HttpRequestBase signedRequest = Client.sign(internalRequest);
            client = (CloseableHttpClient) SSLCipherSuiteUtil.createHttpClient("TLSv1.2");

            log.info("dms api query, url:{}, body: {}", dmsQueryUrl, requestBody);
            HttpResponse httpResponse = client.execute(signedRequest);
            HttpEntity respEntity = httpResponse.getEntity();
            if (respEntity == null) {
                return null;
            }
            String respBody = EntityUtils.toString(respEntity);
            if (StringUtils.isBlank(respBody)) {
                return null;
            }
            BaseResponse<DMSQueryResp> baseResponse = JacksonUtils.parseObject(respBody, new TypeReference<BaseResponse<DMSQueryResp>>() {});
            if (!Objects.equal(baseResponse.getCode(), CodeEnum.SUCCESS.getCode())) {
                log.error("dms query failed, datasource:{}, checkSql:{}, respCode:{}, respMsg:{}",
                    JacksonUtils.toJsonString(guardDatasource), checkSql, baseResponse.getCode(), baseResponse.getMsg());
                return null;
            }
            return baseResponse.getData();
        } catch (Exception e) {
            log.error("dms query exception, datasource:{}, checkSql:{}", JacksonUtils.toJsonString(guardDatasource), checkSql, e);
            throw BizException.newSystemException("查询DMS数据源异常");
        } finally {
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    log.error("close client occurred exception", e);
                }
            }
        }
    }

    /**
     * 构建DMS Open API请求体
     * @param connConfig
     * @param checkSql
     */
    private String buildRequestBody(String connUrl, Map<String, Object> connConfig, String checkSql) {
        if (StringUtils.isBlank(connUrl) || MapUtils.isEmpty(connConfig) || StringUtils.isBlank(checkSql)) {
            return null;
        }

        Map<String, Object> requestBody = new HashMap<>(connConfig);
        requestBody.put(SQL_KEY, checkSql);
        requestBody.put(DOMAIN_KEY, connUrl);
        return JacksonUtils.toJsonString(requestBody);
    }

}
