package com.iqiyi.vip.zeus.core.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.MonitorQueryMapping;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.po.MonitorRecordingRulePO;
import com.iqiyi.vip.zeus.core.utils.PromQLUtils;
import com.iqiyi.vip.zeus.eagleclient.api.PrometheusRecordingRuleApi;
import com.iqiyi.vip.zeus.eagleclient.model.RecordRule;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelTarget;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PrometheusPanelTarget;
import com.iqiyi.vip.zeus.eagleclient.utils.RefIdUtils;

/**
 * @author: guojing
 * @date: 2024/4/7 16:24
 */
@Profile("!sg")
@Component
public class PrometheusRecordingRuleComponent {

    @Resource
    private MonitorRecordingRuleService monitorRecordingRuleService;
    @Resource
    private PrometheusRecordingRuleApi prometheusRecordingRuleApi;

    /**
     * 根据PromQL生成RecordName并保存到本地表和Prometheus中
     */
    public List<MonitorRecordingRulePO> autoCreateRecordWhenCreateMonitor(ZeusMonitor monitor, List<PanelTarget> panelTargets, Map<Integer, MetricTemplate> metricTemplateMap) {
        Map<String, ZeusMonitorQuery> queryIdToQueryMap = monitor.getQuery().stream()
            .collect(Collectors.toMap(query -> RefIdUtils.buildRefIdFromMonitorQueryId(query.getId()), query -> query));
        Map<String, RecordRule> monitorRecordNameToRecordRuleMap = new LinkedHashMap<>();
        List<MonitorRecordingRulePO> monitorRecordingRulePOS = new ArrayList<>();
        for (PanelTarget panelTarget : panelTargets) {
            if (!(panelTarget instanceof PrometheusPanelTarget)) {
                continue;
            }
            ZeusMonitorQuery monitorQuery = queryIdToQueryMap.get(panelTarget.getRefId());
            PrometheusPanelTarget prometheusPanelTarget = (PrometheusPanelTarget) panelTarget;
            String promQLExpr = prometheusPanelTarget.getExpr();
            String recordName = PromQLUtils.genRecordName(monitorQuery, promQLExpr);
            RecordRule recordRule = new RecordRule(recordName, promQLExpr);
            if (!monitorRecordNameToRecordRuleMap.containsKey(recordName)) {
                monitorRecordNameToRecordRuleMap.put(recordName, recordRule);
            }
            monitorRecordingRulePOS.add(new MonitorRecordingRulePO(monitor.getId(), monitorQuery.getId(), recordName));
            //Panel表达式更新为Record
            prometheusPanelTarget.setExpr(recordName);
        }

        List<String> monitorRecordNames = new ArrayList<>(monitorRecordNameToRecordRuleMap.keySet());
        Map<String, List<MonitorQueryMapping>> recordNameToMonitorMapFromDB = monitorRecordingRuleService.batchGetByRecordName(monitorRecordNames);
        List<RecordRule> needPushToPrometheusRecords = new ArrayList<>();
        //监控和Record关联表中没有查询到记录
        if (MapUtils.isEmpty(recordNameToMonitorMapFromDB)) {
            needPushToPrometheusRecords = new ArrayList<>(monitorRecordNameToRecordRuleMap.values());
        } else {
            for (MonitorRecordingRulePO monitorRecordingRulePO : monitorRecordingRulePOS) {
                String recordName = monitorRecordingRulePO.getRecordName();
                RecordRule recordRule = monitorRecordNameToRecordRuleMap.get(recordName);
                if (!recordNameToMonitorMapFromDB.containsKey(recordName)) {
                    needPushToPrometheusRecords.add(recordRule);
                }
            }
        }

        prometheusRecordingRuleApi.batchCreate(needPushToPrometheusRecords);
        return monitorRecordingRulePOS;
    }

    /**
     * 根据PromQL生成RecordName并更新到本地表和Prometheus中
     */
    public void autoCreateAndDeleteRecordWhenUpdateMonitor(ZeusMonitor updateMonitor, ZeusMonitor monitorFromDB, List<PanelTarget> panelTargets, Map<Integer, MetricTemplate> metricTemplateMap) {
        Integer monitorId = updateMonitor.getId();
        List<Integer> monitorQueryIdsFromParam = updateMonitor.getQuery().stream().map(ZeusMonitorQuery::getId).collect(Collectors.toList());
        List<Integer> monitorQueryIdsFromDB = monitorFromDB.getQuery().stream().map(ZeusMonitorQuery::getId).collect(Collectors.toList());
        Map<String, ZeusMonitorQuery> queryIdToQueryMap = updateMonitor.getQuery().stream()
            .collect(Collectors.toMap(query -> RefIdUtils.buildRefIdFromMonitorQueryId(query.getId()), query -> query));
        List<MonitorRecordingRulePO> monitorRecordingRulesFromParam = new ArrayList<>();
        Map<Integer, RecordRule> queryIdToRecordRuleMapFromParam = new LinkedHashMap<>();
        for (PanelTarget panelTarget : panelTargets) {
            if (!(panelTarget instanceof PrometheusPanelTarget)) {
                continue;
            }
            ZeusMonitorQuery monitorQuery = queryIdToQueryMap.get(panelTarget.getRefId());
            PrometheusPanelTarget prometheusPanelTarget = (PrometheusPanelTarget) panelTarget;
            String promQLExpr = prometheusPanelTarget.getExpr();
            String recordName = PromQLUtils.genRecordName(monitorQuery, promQLExpr);
            RecordRule recordRule = new RecordRule(recordName, promQLExpr);
            monitorRecordingRulesFromParam.add(new MonitorRecordingRulePO(monitorId, monitorQuery.getId(), recordName));
            queryIdToRecordRuleMapFromParam.put(monitorQuery.getId(), recordRule);
            //Panel表达式更新为Record
            prometheusPanelTarget.setExpr(recordName);
        }
        Map<Integer, String> queryToRecordMapFromDB = monitorRecordingRuleService.getByMonitorId(monitorId);

        List<Integer> needInsertQueryIds = monitorQueryIdsFromParam.stream()
            .filter(queryId -> !monitorQueryIdsFromDB.contains(queryId))
            .collect(Collectors.toList());
        List<Integer> needUpdateQueryIds = monitorQueryIdsFromParam.stream()
            .filter(monitorQueryIdsFromDB::contains)
            .collect(Collectors.toList());
        List<Integer> needDeleteQueryIds = monitorQueryIdsFromDB.stream()
            .filter(queryId -> !monitorQueryIdsFromParam.contains(queryId))
            .collect(Collectors.toList());
        List<String> monitorRecordNamesFromDB = new ArrayList<>(queryToRecordMapFromDB.values());
        Map<String, List<MonitorQueryMapping>> recordNameToMonitorMapFromDB = monitorRecordingRuleService.batchGetByRecordName(monitorRecordNamesFromDB);
        Map<String, Map<Integer, List<Integer>>> recordNameToMonitorAndQueryMapFromDB = recordNameToMonitorMapFromDB.entrySet().stream()
            .collect(Collectors.toMap(Entry::getKey, entry -> entry.getValue().stream()
                .collect(Collectors.toMap(MonitorQueryMapping::getMonitorId, MonitorQueryMapping::getQueryIds))));

        List<RecordRule> needCreateToPrometheusRecords = new ArrayList<>();
        List<String> needDeleteFromPrometheusRecordNames = new ArrayList<>();
        for (Integer queryId : needDeleteQueryIds) {
            String recordNameFromDB = queryToRecordMapFromDB.get(queryId);
            Map<Integer, List<Integer>> monitorIdToQueryIdsMapFromDB = recordNameToMonitorAndQueryMapFromDB.get(recordNameFromDB);
            if (CollectionUtils.size(monitorIdToQueryIdsMapFromDB) == 1 && monitorIdToQueryIdsMapFromDB.containsKey(monitorId)) {
                List<Integer> queryIds = monitorIdToQueryIdsMapFromDB.get(monitorId);
                if (CollectionUtils.size(queryIds) == 1 && queryId.equals(queryIds.get(0))) {
                    needDeleteFromPrometheusRecordNames.add(recordNameFromDB);
                }
            }
        }
        for (Integer queryId : needUpdateQueryIds) {
            RecordRule queryRecordRuleFromParam = queryIdToRecordRuleMapFromParam.get(queryId);
            String queryRecordNameFromDB = queryToRecordMapFromDB.get(queryId);
            Map<Integer, List<Integer>> monitorIdToQueryIdsMap = monitorRecordingRuleService.getByRecordName(queryRecordRuleFromParam.getRecord());
            if (MapUtils.isEmpty(monitorIdToQueryIdsMap)) {
                needCreateToPrometheusRecords.add(queryRecordRuleFromParam);
            }
            //当前监控除了当前查询条件是否还有其他查询使用
            boolean otherQueryNeedUse = queryIdToRecordRuleMapFromParam.entrySet().stream()
                .filter(entry -> !Objects.equals(entry.getKey(), queryId) && !needDeleteQueryIds.contains(queryId))
                .anyMatch(entry -> Objects.equals(entry.getValue(), queryRecordRuleFromParam));
            if (!otherQueryNeedUse) {
                needDeleteFromPrometheusRecordNames.add(queryRecordNameFromDB);
            }
        }
        for (Integer queryId : needInsertQueryIds) {
            needCreateToPrometheusRecords.add(queryIdToRecordRuleMapFromParam.get(queryId));
        }

        prometheusRecordingRuleApi.batchDelete(needDeleteFromPrometheusRecordNames);
        prometheusRecordingRuleApi.batchCreate(needCreateToPrometheusRecords);
        monitorRecordingRuleService.insertAndDelete(monitorId, monitorRecordingRulesFromParam, needDeleteQueryIds);
    }

}
