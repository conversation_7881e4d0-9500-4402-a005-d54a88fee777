package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import com.iqiyi.vip.zeus.eagleclient.api.DevOpsApi;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Project;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;

/**
 * @author: guojing
 * @date: 2023/12/26 15:08
 */
@Profile("!sg")
@Component
public class DevOpsComponent {

    @Resource
    private DevOpsApi devOpsApi;

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Team_getByTeamCode", cacheType= CacheType.LOCAL)
    public Team getByTeamCode(String teamCode) {
        return devOpsApi.getByTeamCode(teamCode);
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Team_getVipRdTeams", cacheType= CacheType.LOCAL)
    public List<Team> getVipRdTeams() {
        return devOpsApi.getVipRdTeams();
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "Project_existsNameUnderTeam", cacheType= CacheType.LOCAL)
    public Project existsNameUnderTeam(String name, String teamCode) {
        List<Project> projectList = devOpsApi.searchProject(name, teamCode, null);
        if (CollectionUtils.isEmpty(projectList)) {
            return null;
        }
        return projectList.stream().filter(project -> Objects.equals(project.getName(), name)).findFirst().orElse(null);
    }


}
