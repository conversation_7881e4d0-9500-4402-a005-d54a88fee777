package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.iqiyi.vip.zeus.core.component.DatasourceSchemaPullerFactory;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.mapper.zeus.DatasourceMapper;
import com.iqiyi.vip.zeus.core.mapper.zeus.DatasourceSchemaMapper;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.po.DatasourcePO;
import com.iqiyi.vip.zeus.core.req.DatasourceSearchParam;
import com.iqiyi.vip.zeus.eagleclient.api.EagleDatasourceApi;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientAlreadyExistsException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Datasource;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDatasourceRequest;
import com.iqiyi.vip.zeus.eagleclient.response.CreateDatasourceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: guojing
 * @date: 2023/11/24 15:05
 */
@Profile("!sg")
@Slf4j
@Service
public class ZeusDatasourceService {

    @Resource
    private DatasourceMapper datasourceMapper;
    @Resource
    private EagleDatasourceApi eagleDatasourceApi;
    @Resource
    private DevOpsComponent devOpsComponent;
    @Resource
    private DatasourceSchemaMapper datasourceSchemaMapper;
    @Resource
    private DatasourceSchemaPullerFactory datasourceSchemaPullerFactory;
    @Resource
    private ThreadPoolTaskExecutor datasourceSchemaAsyncExecutor;

    public boolean exist(String name) {
        DatasourcePO datasourcePO = datasourceMapper.selectByName(name);
        return datasourcePO != null;
    }

    /**
     * 创建数据源
     * @param createParam
     * @return 数据源id
     */
    public Integer create(ZeusDatasource createParam) {
        DataSourceType dataSourceType = DataSourceType.parseValue(createParam.getType());
        String eagleDatasourceUid = null;
        if (dataSourceType != null && dataSourceType.supportPushToEagle()) {
            CreateDatasourceRequest createRequest = createParam.toEagleDSRequest();
            try {
                CreateDatasourceResponse createDatasourceResponse = eagleDatasourceApi.createDataSource(createRequest);
                eagleDatasourceUid = createDatasourceResponse.getDatasource().getUid();
            } catch (EagleClientAlreadyExistsException e) {
                Datasource eagleDatasource = eagleDatasourceApi.getDataSourceByName(createParam.getName());
                eagleDatasourceUid = eagleDatasource.getUid();
                createRequest.setUid(eagleDatasourceUid);
                eagleDatasourceApi.updateDataSource(createRequest);
            }
        }
        if (StringUtils.isNotBlank(eagleDatasourceUid)) {
            createParam.setEagleDatasourceUid(eagleDatasourceUid);
        }
        DatasourcePO createDatasourcePO = createParam.toDatasourcePO();
        datasourceMapper.insert(createDatasourcePO);
        Integer datasourceId = createDatasourcePO.getId();
        try {
            createParam.setId(datasourceId);
            datasourceSchemaAsyncExecutor.execute(() -> {
                try {
                    datasourceSchemaPullerFactory.getPuller(dataSourceType).pull(Collections.singletonList(createParam));
                } catch (Exception e) {
                    log.error("数据源schema拉取失败, datasource id:{}, name:{}", datasourceId, createParam.getName(), e);
                }
            });
        } catch (Exception e) {
            log.error("数据源创建成功后，数据源schema拉取异步执行失败, datasource id:{}, name:{}", datasourceId, createParam.getName(), e);
        }
        return datasourceId;
    }

    public boolean update(ZeusDatasource updateParam) {
        DataSourceType dataSourceType = DataSourceType.parseValue(updateParam.getType());
        if (dataSourceType != null && dataSourceType.supportPushToEagle()) {
            String eagleDatasourceUid = updateParam.getEagleDatasourceUid();
            if (StringUtils.isBlank(eagleDatasourceUid)) {
                throw BizException.newParamException("extraData中eagleDatasourceUid字段不能为空");
            }
            CreateDatasourceRequest updateRequest = updateParam.toEagleDSRequest();
            try {
                eagleDatasourceApi.updateDataSource(updateRequest);
            } catch (EagleClientNotExistsException e) {
                throw BizException.newParamException("根据extraData中eagleDatasourceUid字段值未查询到鹰眼数据源");
            }
        }
        datasourceMapper.update(updateParam.toDatasourcePO());
        return true;
    }

    public boolean delete(ZeusDatasource zeusDatasource) {
        DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
        if (dataSourceType != null && dataSourceType.supportPushToEagle()) {
            eagleDatasourceApi.deleteDataSource(zeusDatasource.getEagleDatasourceUid());
        }
        datasourceSchemaMapper.deleteByDatasourceId(zeusDatasource.getId());
        datasourceMapper.deleteByPrimaryKey(zeusDatasource.getId());
        return true;
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ZeusDatasource_getByIdFromCache", cacheType= CacheType.LOCAL)
    public ZeusDatasource getByIdFromCache(Integer id) {
        if (id == null) {
            return null;
        }
        DatasourcePO datasourcePO = datasourceMapper.selectByPrimaryKey(id);
        datasourcePO.setSensitiveExtraData(null);
        return ZeusDatasource.buildFrom(datasourcePO);
    }

    public ZeusDatasource getById(Integer id) {
        if (id == null) {
            return null;
        }
        DatasourcePO datasourcePO = datasourceMapper.selectByPrimaryKey(id);
        datasourcePO.setSensitiveExtraData(null);
        return ZeusDatasource.buildFrom(datasourcePO);
    }

    public ZeusDatasource getByIdWithSensitiveData(Integer id) {
        if (id == null) {
            return null;
        }
        DatasourcePO datasourcePO = datasourceMapper.selectByPrimaryKey(id);
        return ZeusDatasource.buildFrom(datasourcePO);
    }

    public Map<Integer, ZeusDatasource> batchGetByIdWithSensitiveData(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<DatasourcePO> datasourcePOS = datasourceMapper.batchSelectByPrimaryKey(ids);
        return datasourcePOS.stream()
            .map(ZeusDatasource::buildFrom)
            .collect(Collectors.toMap(ZeusDatasource::getId, datasource -> datasource));
    }

    public ZeusDatasource getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        DatasourcePO datasourcePO = datasourceMapper.selectByName(name);
//        datasourcePO.setSensitiveExtraData(null);
        return ZeusDatasource.buildFrom(datasourcePO);
    }

    /**
     * 搜索数据源
     * @param searchParam
     */
    public List<ZeusDatasource> search(DatasourceSearchParam searchParam) {
        List<DatasourcePO> datasourcePOS = datasourceMapper.search(searchParam.getTeamCode(), searchParam.getType(), searchParam.getName());
        if (CollectionUtils.isEmpty(datasourcePOS)) {
            return Collections.emptyList();
        }
        return datasourcePOS.stream().map(datasourcePO -> {
            datasourcePO.setSensitiveExtraData(null);
            ZeusDatasource zeusDatasource = ZeusDatasource.buildFrom(datasourcePO);
            Team team = devOpsComponent.getByTeamCode(zeusDatasource.getTeamCode());
            if (team != null) {
                zeusDatasource.setTeamName(team.getCnName());
            }
            return zeusDatasource;
        }).collect(Collectors.toList());
    }

    public List<ZeusDatasource> getByType(DataSourceType dataSourceType) {
        if (dataSourceType == null) {
            return Collections.emptyList();
        }
        List<DatasourcePO> mysqlDatasourceList = datasourceMapper.listDatasourceByType(dataSourceType.getValue());
        if (CollectionUtils.isEmpty(mysqlDatasourceList)) {
            return Collections.emptyList();
        }
        return mysqlDatasourceList.stream().map(ZeusDatasource::buildFrom).collect(Collectors.toList());
    }

}
