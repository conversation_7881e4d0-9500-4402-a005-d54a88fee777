package com.iqiyi.vip.zeus.core.utils;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MetricTemplateType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRuleCondition;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.eagleclient.constants.FieldConstants;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.AlertRule;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleAnnotations;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleConditionModel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleExt;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleQuery;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition.AlertRuleConditionModelCondition;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition.ConditionOperator;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardPanel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.MysqlPanelTarget;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelTarget;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PrometheusPanelTarget;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.utils.RefIdUtils;

/**
 * @author: guojing
 * @date: 2023/12/21 14:55
 */
public class MonitorToEagleTransfer {

    /**
     * 获取监控指标类型对应的Dashboard Panel的json样例
     */
    public static DashboardPanel getDashboardPanelDemo(DataSourceType dataSourceType, List<MetricTemplate> metricTemplates) {
        String panelJsonTmpResourcePath = null;
        if (dataSourceType == DataSourceType.Prometheus) {
            panelJsonTmpResourcePath = MetricTemplateType.PROMETHEUS_TIME_SERIES.panelJsonTmpResourcePath();
        }
        if (dataSourceType == DataSourceType.MySQL) {
            MetricTemplate metricTemplate = metricTemplates.get(0);
            MetricTemplateType metricTemplateType = MetricTemplateType.parseValue(metricTemplate.getType());
            panelJsonTmpResourcePath = metricTemplateType.panelJsonTmpResourcePath();
        }
        if (panelJsonTmpResourcePath == null) {
            throw BizException.newParamException("此数据源未匹配到鹰眼Panel Json模版文件，dataSourceType: " + dataSourceType);
        }
        DashboardPanel dashboardPanel = JacksonUtils.loadFromJsonFile(panelJsonTmpResourcePath, DashboardPanel.class);
        if (dashboardPanel == null) {
            throw BizException.newSystemException("构建鹰眼Panel数据出现异常,jsonFile:" + panelJsonTmpResourcePath);
        }
        return dashboardPanel;
    }

    /**
     * 宙斯监控查询条件转换为鹰眼PanelTarget对象
     */
    public static List<PanelTarget> monitorQueryToPanelTarget(List<ZeusMonitorQuery> monitorQueries, Map<Integer, MetricTemplate> metricTemplateMap, ZeusDatasource zeusDatasource) {
        if (CollectionUtils.isEmpty(monitorQueries)) {
            return Collections.emptyList();
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
        List<PanelTarget> panelTargets = new ArrayList<>();
        for (ZeusMonitorQuery query : monitorQueries) {
            MetricTemplate currentMetricTemplate = metricTemplateMap.get(query.getMetricTmpId());
            MetricTemplateType metricTemplateType = MetricTemplateType.parseValue(currentMetricTemplate.getType());
            PanelTarget panelTarget = null;
            switch (dataSourceType) {
                case Prometheus:
                    PrometheusPanelTarget prometheusPanelTarget = JacksonUtils.loadFromJsonFile(dataSourceType.panelTargetJsonTmpResourcePath(), PrometheusPanelTarget.class);
                    if (prometheusPanelTarget == null) {
                        throw BizException.newSystemException("构建鹰眼PanelTarget数据出现异常,jsonFile:" + dataSourceType.panelTargetJsonTmpResourcePath());
                    }
                    prometheusPanelTarget.setExpr(PromQLUtils.genPromQL(currentMetricTemplate, query));
                    String legendFormat = PromQLUtils.buildLegendFormat(query);
                    if (StringUtils.isNotBlank(legendFormat)) {
                        prometheusPanelTarget.setLegendFormat(legendFormat);
                    }
                    panelTarget = prometheusPanelTarget;
                    break;
                case MySQL:
                    MysqlPanelTarget mysqlPanelTarget = JacksonUtils.loadFromJsonFile(dataSourceType.panelTargetJsonTmpResourcePath(), MysqlPanelTarget.class);
                    if (mysqlPanelTarget == null) {
                        throw BizException.newSystemException("构建鹰眼PanelTarget数据出现异常,jsonFile:" + dataSourceType.panelTargetJsonTmpResourcePath());
                    }
                    mysqlPanelTarget.setDataset(zeusDatasource.getDatabaseItem());
                    mysqlPanelTarget.setFormat(metricTemplateType.getPanelTargetType());
                    mysqlPanelTarget.setRawSql(PromQLUtils.genMySQL(currentMetricTemplate, query));
                    panelTarget = mysqlPanelTarget;
                    break;
                default:
                    break;
            }
            if (panelTarget != null) {
                panelTarget.setRefId(RefIdUtils.buildRefIdFromMonitorQueryId(query.getId()));
                panelTarget.getDatasource().setUid(zeusDatasource.getEagleDatasourceUid());
                panelTargets.add(panelTarget);
            }
        }
        return panelTargets;
    }


    /**
     * 宙斯告警规则转换为鹰眼AlertRule对象
     */
    public static AlertRule zeusAlertRuleToEagle(ZeusAlertRule zeusAlertRule, ZeusMonitor zeusMonitor, DashboardWithMeta dashboardWithMeta) {
        Integer panelId = zeusMonitor.getPanelId();
        DashboardPanel dashboardPanel = dashboardWithMeta.getDashboard().getPanels().stream()
            .filter(panel -> Objects.equals(panel.getId(), panelId))
            .findFirst().orElse(null);
        List<Map<String, Object>> targets = dashboardPanel.getTargets();
        AlertRuleQuery alertRuleCondition = buildAlertRuleCondition(zeusAlertRule.getConditions(), targets.size());
        List<AlertRuleQuery> alertRuleQueryList = buildAlertRuleQueryList(targets);
        alertRuleQueryList.add(alertRuleCondition);

        AlertRule alertRule = JacksonUtils.loadFromJsonFile("tmp/alert-rule.json", AlertRule.class);
        if (alertRule == null) {
            throw BizException.newSystemException("构建鹰眼监控数据出现异常[alert-rule.json]");
        }
        alertRule.setUid(zeusAlertRule.getEagleUid());
        alertRule.setFolderUID(dashboardWithMeta.getMeta().getFolderUid());
        alertRule.setRuleGroup("检测频率-" + zeusAlertRule.getCheckFrequency());
        alertRule.setTitle(zeusAlertRule.getName());
        alertRule.setDuration(zeusAlertRule.getDuration());
        alertRule.setCondition(alertRuleCondition.getRefId());
        alertRule.setData(alertRuleQueryList);
        AlertRuleAnnotations annotations = alertRule.getAnnotations();
        annotations.set__dashboardUid__(zeusMonitor.getDashboardUid());
        annotations.set__panelId__(panelId.toString());
        annotations.setSummary(zeusAlertRule.getName() + "超过阈值");
        alertRule.setLevel(zeusAlertRule.getLevel());
        alertRule.setExt(AlertRuleExt.newInstance(zeusAlertRule.getReceivers()));
        return alertRule;
    }

    /**
     * 构建Eagle告警规则查询对象
     */
    private static List<AlertRuleQuery> buildAlertRuleQueryList(List<Map<String, Object>> targets) {
        List<AlertRuleQuery> alertRuleQueryList = new ArrayList<>();
        for (Map<String, Object> target : targets) {
            String refId = MapUtils.getString(target, FieldConstants.REF_ID);
            Map<String, Object> datasourceMap = (Map<String, Object>) MapUtils.getMap(target, FieldConstants.DATASOURCE);
            String datasourceUid = MapUtils.getString(datasourceMap, FieldConstants.UID);
            AlertRuleQuery alertRuleQuery = AlertRuleQuery.newQueryInstance(refId, datasourceUid);
            alertRuleQuery.setModel(target);
            alertRuleQueryList.add(alertRuleQuery);
        }
        return alertRuleQueryList;
    }

    /**
     * 构建Eagle告警规则表达式对象
     */
    private static AlertRuleQuery buildAlertRuleCondition(List<ZeusAlertRuleCondition> zeusAlertRuleConditions, int targetSize) {
        String conditionRefId = RefIdUtils.buildConditionRefIdFromMonitorQueryId(targetSize + 1);
        AlertRuleConditionModel alertRuleConditionModel = AlertRuleConditionModel.newDefault(conditionRefId);
        List<AlertRuleConditionModelCondition> conditions = new ArrayList<>();
        for (ZeusAlertRuleCondition condition : zeusAlertRuleConditions) {
            String queryRefId = RefIdUtils.buildRefIdFromMonitorQueryId(condition.getMonitorQueryId());
            List<Float> threshold = Lists.newArrayList(condition.getLeftThreshold());
            if (condition.getRightThreshold() != null) {
                threshold.add(condition.getRightThreshold());
            }
            AlertRuleConditionModelCondition alertRuleConditionModelCondition = AlertRuleConditionModelCondition.newCondition(queryRefId, condition.getOperator(), threshold);
            conditions.add(alertRuleConditionModelCondition);
        }
        conditions.get(0).setOperator(ConditionOperator.when());
        alertRuleConditionModel.setConditions(conditions);

        AlertRuleQuery alertRuleCondition = AlertRuleQuery.newConditionInstance(conditionRefId);
        alertRuleCondition.setModel(JacksonUtils.beanToMap(alertRuleConditionModel));
        return alertRuleCondition;
    }

}
