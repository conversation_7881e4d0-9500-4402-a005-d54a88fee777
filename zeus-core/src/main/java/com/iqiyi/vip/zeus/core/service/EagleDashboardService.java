package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.DashboardDisplayInfo;
import com.iqiyi.vip.zeus.core.req.DashboardCreateParam;
import com.iqiyi.vip.zeus.core.req.DashboardSearchParam;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.eagleclient.api.EagleDashboardApi;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientAlreadyExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.FolderSimpleInfo;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardSearchResult;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDashboardRequest;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.response.CreateDashboardResponse;

/**
 * @author: guojing
 * @date: 2023/11/25 19:08
 */
@Profile("!sg")
@Service
public class EagleDashboardService {

    @Value("${eagle.server.domain}")
    private String eagleServerDomain;
    @Resource
    private EagleDashboardApi dashboardApi;
    @Resource
    private EagleFolderService folderService;
    @Resource
    private DevOpsComponent devOpsComponent;

    /**
     * 在指定folder下精确匹配是否存在标题为title的Dashboard
     * @param title Dashboard标题
     * @return 存在则返回Dashboard uid，否则返回null
     */
    public String exist(Integer folderId, String title) {
        List<DashboardSearchResult> resultList = dashboardApi.searchDashboardByTitle(folderId, title);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        DashboardSearchResult matchedResult = resultList.stream()
            .filter(dashboard -> Objects.equals(dashboard.getTitle(), title))
            .findFirst()
            .orElse(null);
        return matchedResult != null ? matchedResult.getUid() : null;
    }

    /**
     * 创建Dashboard
     * @param createParam
     * @return Dashboard uid
     */
    public DashboardDisplayInfo create(DashboardCreateParam createParam) {
        AuthorityUser authorityUser = RequestContextHolder.getCurrentUser();
        String teamCnName = authorityUser.getRealTeam().getCnName();
        FolderSimpleInfo folderSimpleInfo = folderService.getOrCreate(teamCnName);
        Dashboard dashboard = JacksonUtils.loadFromJsonFile("tmp/dashboard.json", Dashboard.class);
        dashboard.setTitle(createParam.getTitle());
        CreateDashboardRequest createRequest = CreateDashboardRequest.newCreateInstance(dashboard, folderSimpleInfo.getUid(), createParam.getMessage());
        try {
            CreateDashboardResponse response = dashboardApi.createOrUpdateDashboard(createRequest);
            return DashboardDisplayInfo.builder()
                .uid(response.getUid())
                .title(createParam.getTitle())
                .url(eagleServerDomain + response.getUrl())
                .folderUid(folderSimpleInfo.getUid())
                .folderTitle(folderSimpleInfo.getTitle())
                .folderUrl(eagleServerDomain + folderSimpleInfo.getUrl())
                .build();
        } catch (EagleClientAlreadyExistsException e) {
            throw BizException.newParamException("Dashboard已存在");
        }
    }

    public CreateDashboardResponse createOrUpdateDashboard(CreateDashboardRequest request) {
        return dashboardApi.createOrUpdateDashboard(request);
    }

//    /**
//     * 更新Dashboard title
//     * @param updateParam
//     * @return Dashboard uid
//     */
//    public boolean update(DashboardCreateParam updateParam) {
//        AuthorityUser authorityUser = RequestContextHolder.getCurrentUser();
//        String teamCnName = authorityUser.getRealTeam().getCnName();
//        FolderSimpleInfo folderSimpleInfo = folderService.exist(teamCnName);
//        if (folderSimpleInfo == null) {
//            throw BizException.newParamException("Folder不存在");
//        }
//        DashboardWithMeta dashboardWithMeta = getByUid(updateParam.getDashboardUid());
//        if (dashboardWithMeta == null) {
//            throw BizException.newParamException("Dashboard不存在");
//        }
//        Dashboard storedDashboard = dashboardWithMeta.getDashboard();
//        storedDashboard.setTitle(updateParam.getTitle());
//        CreateDashboardRequest updateRequest = CreateDashboardRequest.newUpdateInstance(storedDashboard, folderSimpleInfo.getUid(), updateParam.getMessage());
//        try {
//            dashboardApi.createOrUpdateDashboard(updateRequest);
//            return true;
//        } catch (EagleClientNotExistsException e) {
//            throw BizException.newParamException("Dashboard不存在");
//        }
//    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "DashboardWithMeta_getByUid", cacheType= CacheType.LOCAL)
    public DashboardWithMeta getByUid(String dashboardUid) {
        return dashboardApi.getDashboardByUid(dashboardUid);
    }

    public DashboardWithMeta getByUidWithoutCache(String dashboardUid) {
        return dashboardApi.getDashboardByUid(dashboardUid);
    }

    public List<DashboardSearchResult> search(DashboardSearchParam searchParam, AuthorityTeamBasic userRealTeam) {
        String title = searchParam.getTitle();
        String teamCode = StringUtils.isNotBlank(searchParam.getTeamCode()) ? searchParam.getTeamCode() : userRealTeam.getTeamCode();
        Team team = devOpsComponent.getByTeamCode(teamCode);
        if (team == null) {
            throw BizException.newParamException("未查询到团队信息");
        }
        FolderSimpleInfo folderSimpleInfo = folderService.exist(team.getCnName());
        if (folderSimpleInfo == null) {
            return Collections.emptyList();
        }
        EagleDashboardService thisObj = (EagleDashboardService) AopContext.currentProxy();
        List<DashboardSearchResult> searchResults = StringUtils.isBlank(title)
            ? thisObj.getAllUnderFolder(folderSimpleInfo.getId())
            : thisObj.searchByTitle(folderSimpleInfo.getId(), title);
        searchResults.forEach(dashboard -> {
            dashboard.setUrl(eagleServerDomain + dashboard.getUrl());
            dashboard.setFolderUrl(eagleServerDomain + dashboard.getFolderUrl());
        });
        return searchResults;
    }

    /**
     * 获取指定Folder下的所有Dashboard
     * @param folderId
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "DashboardSearchResult_getAllUnderFolder", cacheType= CacheType.LOCAL)
    public List<DashboardSearchResult> getAllUnderFolder(Integer folderId) {
        return dashboardApi.getAllUnderFolder(folderId);
    }

    /**
     * 在指定Folder下根据title模糊搜索Dashboard
     * @param folderId
     * @param title
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "DashboardSearchResult_searchByTitle", cacheType= CacheType.LOCAL)
    public List<DashboardSearchResult> searchByTitle(Integer folderId, String title) {
        return dashboardApi.searchDashboardByTitle(folderId, title);
    }

}
