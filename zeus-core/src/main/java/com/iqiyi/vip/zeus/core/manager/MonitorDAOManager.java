package com.iqiyi.vip.zeus.core.manager;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.core.mapper.zeus.MonitorMapper;
import com.iqiyi.vip.zeus.core.mapper.zeus.MonitorRecordingRuleMapper;
import com.iqiyi.vip.zeus.core.po.zeus.MonitorPO;
import com.iqiyi.vip.zeus.core.po.zeus.MonitorRecordingRulePO;

/**
 * @author: guojing
 * @date: 2024/4/15 11:25
 */
@Profile("!sg")
@Component
public class MonitorDAOManager {

    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private MonitorRecordingRuleMapper monitorRecordingRuleMapper;

    /**
     * 保存监控PO到数据库
     * @param monitorPO
     * @param monitorRecordingRulePOS
     */
    @Transactional(transactionManager = "zeusTransactionManager", rollbackFor = Exception.class)
    public Integer createMonitor(MonitorPO monitorPO, List<MonitorRecordingRulePO> monitorRecordingRulePOS) {
        monitorMapper.insert(monitorPO);
        if (CollectionUtils.isNotEmpty(monitorRecordingRulePOS)) {
            monitorRecordingRulePOS.forEach(monitorRecordingRulePO -> monitorRecordingRulePO.setMonitorId(monitorPO.getId()));
            monitorRecordingRuleMapper.batchInsert(monitorRecordingRulePOS);
        }
        return monitorPO.getId();
    }

    public boolean deleteMonitor(Integer monitorId) {
        monitorMapper.deleteByPrimaryKey(monitorId);
        return true;
    }

}
