package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.manager.MonitorDAOManager;
import com.iqiyi.vip.zeus.core.mapper.zeus.MonitorMapper;
import com.iqiyi.vip.zeus.core.model.DashboardDisplayInfo;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorDetail;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.po.MonitorPO;
import com.iqiyi.vip.zeus.core.po.MonitorRecordingRulePO;
import com.iqiyi.vip.zeus.core.req.MonitorSearchParam;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.core.utils.MonitorToEagleTransfer;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardMeta;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardPanel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelTarget;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDashboardRequest;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;

/**
 * @author: guojing
 * @date: 2023/11/29 14:31
 */
@Slf4j
@Profile("!sg")
@Service
public class ZeusMonitorService {

    @Value("${eagle.server.domain}")
    private String eagleServerDomain;
    @Resource
    private MonitorMapper monitorMapper;
    @Resource
    private ZeusDatasourceService datasourceService;
    @Resource
    private EagleDashboardService eagleDashboardService;
    @Resource
    private DevOpsComponent devOpsComponent;
    @Resource
    private ZeusAlertRuleService alertRuleService;
    @Resource
    private MetricTemplateService metricTemplateService;
    @Resource
    private PrometheusRecordingRuleComponent prometheusRecordingRuleComponent;
    @Resource
    private MonitorDAOManager monitorDAOManager;

    public Integer create(ZeusMonitor createParam, ZeusDatasource zeusDatasource, DashboardWithMeta dashboardWithMeta) {
        Boolean notCreateRecord = MapUtils.getBoolean(createParam.getExtraData(), ZeusMonitor.NOT_CREATE_RECORD);
        List<MonitorRecordingRulePO> monitorRecordingRulePOS = null;
        List<PanelTarget> panelTargets = null;
        if (dashboardWithMeta != null) {
            List<ZeusMonitorQuery> monitorQueries = createParam.getQuery();
            List<MetricTemplate> metricTemplates = monitorQueries.stream()
                .map(ZeusMonitorQuery::getMetricTmpId).distinct()
                .map(metricTmpId -> metricTemplateService.getById(metricTmpId))
                .filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, MetricTemplate> metricTemplateMap = metricTemplates.stream()
                .collect(Collectors.toMap(MetricTemplate::getId, metricTemplate -> metricTemplate));

            DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
            Dashboard dashboard = dashboardWithMeta.getDashboard();
            DashboardPanel dashboardPanel = MonitorToEagleTransfer.getDashboardPanelDemo(dataSourceType, metricTemplates);
            List<DashboardPanel> panels = CollectionUtils.isNotEmpty(dashboard.getPanels()) ? dashboard.getPanels() : new ArrayList<>();
            int maxPanelId = panels.stream().map(DashboardPanel::getId).max(Integer::compareTo).orElse(0);
            dashboardPanel.setId(maxPanelId + 1);
            dashboardPanel.setTitle(createParam.getName());
            dashboardPanel.getDatasource().setUid(zeusDatasource.getEagleDatasourceUid());
            panelTargets = MonitorToEagleTransfer.monitorQueryToPanelTarget(monitorQueries, metricTemplateMap, zeusDatasource);
            //自动生成Prometheus Record
            if (dataSourceType == DataSourceType.Prometheus && BooleanUtils.isFalse(notCreateRecord)) {
                monitorRecordingRulePOS = prometheusRecordingRuleComponent.autoCreateRecordWhenCreateMonitor(createParam, panelTargets, metricTemplateMap);
            }

            List<Map<String, Object>> panelTargetMapList = panelTargets.stream().map(JacksonUtils::beanToMap).collect(Collectors.toList());
            dashboardPanel.setTargets(panelTargetMapList);
            dashboard.addPanel(dashboardPanel);

            DashboardMeta dashboardMeta = dashboardWithMeta.getMeta();
            String message = "创建Panel:" + dashboardPanel.getTitle();
            CreateDashboardRequest createDashboardRequest = CreateDashboardRequest.newUpdateInstance(dashboard, dashboardMeta.getFolderUid(), message);
            eagleDashboardService.createOrUpdateDashboard(createDashboardRequest);
            createParam.setPanelId(dashboardPanel.getId());
        }
        return monitorDAOManager.createMonitor(createParam.toMonitorPO(), monitorRecordingRulePOS);
    }

    public boolean update(ZeusMonitor updateParam, ZeusMonitor monitorFromDB, DashboardWithMeta dashboardWithMeta) {
        Boolean notCreateRecord = MapUtils.getBoolean(monitorFromDB.getExtraData(), ZeusMonitor.NOT_CREATE_RECORD);
        if (dashboardWithMeta != null) {
            ZeusDatasource zeusDatasource = datasourceService.getById(monitorFromDB.getDatasourceId());
            DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
            Integer panelId = monitorFromDB.getPanelId();
            Dashboard dashboard = dashboardWithMeta.getDashboard();
            List<DashboardPanel> panels = CollectionUtils.isNotEmpty(dashboard.getPanels()) ? dashboard.getPanels() : new ArrayList<>();
            DashboardPanel dashboardPanel = panels.stream().filter(panel -> panel.getId().equals(panelId)).findFirst().orElse(null);

            List<ZeusMonitorQuery> updateMonitorQueries = updateParam.getQuery();
            List<MetricTemplate> metricTemplates = updateMonitorQueries.stream()
                .map(ZeusMonitorQuery::getMetricTmpId).distinct()
                .map(metricTmpId -> metricTemplateService.getById(metricTmpId))
                .filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, MetricTemplate> metricTemplateMap = metricTemplates.stream()
                .collect(Collectors.toMap(MetricTemplate::getId, metricTemplate -> metricTemplate));
            boolean needCreatePanel = dashboardPanel == null;
            if (needCreatePanel) {
                dashboardPanel = MonitorToEagleTransfer.getDashboardPanelDemo(dataSourceType, metricTemplates);
                int maxPanelId = panels.stream().map(DashboardPanel::getId).max(Integer::compareTo).orElse(0);
                dashboardPanel.setId(maxPanelId + 1);
                dashboardPanel.getDatasource().setUid(zeusDatasource.getEagleDatasourceUid());
            }

            dashboardPanel.setTitle(updateParam.getName());
            List<PanelTarget> panelTargets = MonitorToEagleTransfer.monitorQueryToPanelTarget(updateMonitorQueries, metricTemplateMap, zeusDatasource);
            //自动生成和删除不用的Prometheus Record
            if (dataSourceType == DataSourceType.Prometheus && BooleanUtils.isFalse(notCreateRecord)) {
                prometheusRecordingRuleComponent.autoCreateAndDeleteRecordWhenUpdateMonitor(updateParam, monitorFromDB, panelTargets, metricTemplateMap);
            }
            List<Map<String, Object>> panelTargetMapList = panelTargets.stream().map(JacksonUtils::beanToMap).collect(Collectors.toList());
            dashboardPanel.setTargets(panelTargetMapList);
            if (needCreatePanel) {
                dashboard.addPanel(dashboardPanel);
            }

            DashboardMeta dashboardMeta = dashboardWithMeta.getMeta();
            String message = "更新Panel:" + dashboardPanel.getTitle();
            CreateDashboardRequest createDashboardRequest = CreateDashboardRequest.newUpdateInstance(dashboard, dashboardMeta.getFolderUid(), message);
            eagleDashboardService.createOrUpdateDashboard(createDashboardRequest);
        }
        monitorMapper.update(updateParam.toMonitorPO());
        return true;
    }

    public boolean delete(ZeusMonitor zeusMonitor) {
        String dashboardUid = zeusMonitor.getDashboardUid();
        Integer panelId = zeusMonitor.getPanelId();
        DashboardWithMeta dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(dashboardUid);
        if (dashboardWithMeta != null && panelId != null) {
            Dashboard dashboard = dashboardWithMeta.getDashboard();
            List<DashboardPanel> panels = dashboard.getPanels();
            if (CollectionUtils.isNotEmpty(panels)) {
                List<DashboardPanel> dashboardPanels = panels.stream().filter(panel -> !Objects.equals(panel.getId(), panelId)).collect(Collectors.toList());
                dashboard.setPanels(dashboardPanels);
                DashboardMeta dashboardMeta = dashboardWithMeta.getMeta();
                String message = "删除Panel:" + zeusMonitor.getName();
                CreateDashboardRequest createDashboardRequest = CreateDashboardRequest.newUpdateInstance(dashboard, dashboardMeta.getFolderUid(), message);
                eagleDashboardService.createOrUpdateDashboard(createDashboardRequest);
            }
        }
        monitorMapper.deleteByPrimaryKey(zeusMonitor.getId());
        ZeusAlertRule zeusAlertRule = alertRuleService.getByMonitor(zeusMonitor.getId());
        if (zeusAlertRule != null) {
            alertRuleService.delete(zeusAlertRule);
        }
        return true;
    }

    public ZeusMonitorDetail getDetailById(Integer monitorId) {
        MonitorPO monitorPO = monitorMapper.selectByPrimaryKey(monitorId);
        if (monitorPO == null) {
            return null;
        }
        ZeusDatasource zeusDatasource = datasourceService.getByIdFromCache(monitorPO.getDatasourceId());
        ZeusMonitor zeusMonitor = ZeusMonitor.buildFrom(monitorPO);
        ZeusAlertRule zeusAlertRule = alertRuleService.getByMonitor(zeusMonitor.getId());
        return new ZeusMonitorDetail(zeusDatasource, zeusMonitor, zeusAlertRule);
    }

    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ZeusMonitor_getById", cacheType= CacheType.LOCAL)
    public ZeusMonitor getById(Integer monitorId) {
        if (monitorId == null) {
            return null;
        }
        MonitorPO monitorPO = monitorMapper.selectByPrimaryKey(monitorId);
        return ZeusMonitor.buildFrom(monitorPO);
    }

    /**
     * 获取监控信息，包含数据源和团队信息
     * @param monitorId
     */
    @CacheRefresh(refresh = 300, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ZeusMonitor_getFriendlyMonitorById", cacheType= CacheType.LOCAL)
    public ZeusMonitor getFriendlyMonitorById(Integer monitorId) {
        MonitorPO monitorPO = monitorMapper.selectByPrimaryKey(monitorId);
        if (monitorPO == null) {
            return null;
        }
        return buildFriendly(monitorPO);
    }

    public boolean databaseInUse(Integer datasourceId) {
        return monitorMapper.getCountByDatasource(datasourceId) > 0;
    }

    public List<ZeusMonitor> search(MonitorSearchParam searchParam) {
        List<MonitorPO> searchResult = new ArrayList<>();
        if (searchParam.getMonitorId() != null) {
            MonitorPO monitorPO = monitorMapper.selectByPrimaryKey(searchParam.getMonitorId());
            if (monitorPO != null) {
                searchResult.add(monitorPO);
            }
        } else {
            searchResult = monitorMapper.search(searchParam);
        }
        if (CollectionUtils.isEmpty(searchResult)) {
            return Collections.emptyList();
        }
        return searchResult.stream().map(this::buildFriendly).collect(Collectors.toList());
    }

    private ZeusMonitor buildFriendly(MonitorPO monitorPO) {
        ZeusMonitor zeusMonitor = ZeusMonitor.buildFrom(monitorPO);
        Team team = devOpsComponent.getByTeamCode(zeusMonitor.getTeamCode());
        if (team != null) {
            zeusMonitor.setTeamName(team.getCnName());
        }
        ZeusDatasource zeusDatasource = datasourceService.getByIdFromCache(monitorPO.getDatasourceId());
        zeusMonitor.setDatasourceName(zeusDatasource.getName());
        zeusMonitor.setDatasourceType(zeusDatasource.getType());
        if (StringUtils.isNotBlank(zeusMonitor.getDashboardUid())) {
            DashboardWithMeta dashboardWithMeta = eagleDashboardService.getByUid(zeusMonitor.getDashboardUid());
            if (dashboardWithMeta != null) {
                DashboardDisplayInfo dashboardDisplayInfo = DashboardDisplayInfo.buildFrom(eagleServerDomain, dashboardWithMeta);
                zeusMonitor.setDashboardDisplayInfo(dashboardDisplayInfo);
                if (zeusMonitor.getPanelId() != null) {
                    zeusMonitor.setPanelIdUrl(dashboardDisplayInfo.getUrl() + "?viewPanel=" + zeusMonitor.getPanelId());
                }
            }
        }
        return zeusMonitor;
    }

}
