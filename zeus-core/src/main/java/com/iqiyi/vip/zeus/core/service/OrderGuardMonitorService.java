package com.iqiyi.vip.zeus.core.service;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.mapper.zeus.OrderGuardMonitorMapper;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardMonitor;
import com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO;

/**
 * @author: guojing
 * @date: 2025/5/19 10:16
 */
@Service
public class OrderGuardMonitorService {

    @Resource
    private OrderGuardMonitorMapper orderGuardMonitorMapper;

    public OrderGuardMonitor getById(Integer id) {
        if (id == null) {
            return null;
        }
        OrderGuardMonitorPO orderGuardMonitorPO = orderGuardMonitorMapper.selectByPrimaryKey(id);
        if (orderGuardMonitorPO == null) {
            return null;
        }
        return OrderGuardMonitor.buildFromPO(orderGuardMonitorPO);
    }

    public List<OrderGuardMonitor> getAll() {
        List<OrderGuardMonitorPO> orderGuardMonitorPOS = orderGuardMonitorMapper.selectAll();
        if (CollectionUtils.isEmpty(orderGuardMonitorPOS)) {
            return Collections.emptyList();
        }

        return orderGuardMonitorPOS.stream()
            .map(OrderGuardMonitor::buildFromPO)
            .collect(Collectors.toList());
    }

}
