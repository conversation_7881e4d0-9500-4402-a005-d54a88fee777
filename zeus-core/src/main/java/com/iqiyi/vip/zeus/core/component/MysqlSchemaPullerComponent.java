package com.iqiyi.vip.zeus.core.component;

import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.ValueType;
import com.iqiyi.vip.zeus.core.mapper.mysql.MysqlInformationSchemaMapper;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.po.DatasourceSchemaPO;
import com.iqiyi.vip.zeus.core.service.DatasourceSchemaService;

/**
 * 从Mysql库拉取表和字段schema信息
 * @author: guojing
 * @date: 2024/2/2 11:52
 */
@Profile("!sg")
@Component
public class MysqlSchemaPullerComponent implements DatasourceSchemaPuller {

    private static final String DB_INFORMATION_SCHEMA = "information_schema";

    private static final Set<String> MYSQL_COLUMN_NUMERIC_TYPES = new HashSet<>(Arrays.asList(
        "tinyint", "smallint", "mediumint", "int", "bigint", "float", "double", "decimal"
    ));

    private static final Set<String> MYSQL_COLUMN_DATETIME_TYPES = new HashSet<>(Arrays.asList("datetime", "timestamp"));

    @Resource
    private CustomDBConfigComponent customDBConfigComponent;
    @Resource
    private DatasourceSchemaService datasourceSchemaService;

    @Override
    public DataSourceType getDatasourceType() {
        return DataSourceType.MySQL;
    }

    @Override
    public void pull(List<ZeusDatasource> datasourceList) {
        if (CollectionUtils.isEmpty(datasourceList)) {
            return;
        }

        for (ZeusDatasource datasource : datasourceList) {
            SqlSessionTemplate sqlSessionTemplate = customDBConfigComponent.genSqlSessionTemplate(datasource, DB_INFORMATION_SCHEMA);
            if (sqlSessionTemplate == null) {
                continue;
            }
            List<DatasourceSchemaPO> datasourceSchemaPOList = sqlSessionTemplate.getMapper(MysqlInformationSchemaMapper.class)
                .selectByDatabaseName(datasource.getDatabaseItem());
            if (CollectionUtils.isEmpty(datasourceSchemaPOList)) {
                continue;
            }
            datasourceSchemaPOList.forEach(datasourceSchemaPO -> {
                datasourceSchemaPO.setDatasourceId(datasource.getId());
                datasourceSchemaPO.setTableTeamCode(datasource.getTeamCode());
                ValueType columnType = convertColumnType(datasourceSchemaPO.getColumnType());
                datasourceSchemaPO.setColumnType(columnType.getValue());
            });
            datasourceSchemaService.refresh(datasourceSchemaPOList);
        }

    }

    private static ValueType convertColumnType(String columnType) {
        if (MYSQL_COLUMN_NUMERIC_TYPES.contains(columnType.toLowerCase())) {
            return ValueType.NUMBER;
        }
        if (MYSQL_COLUMN_DATETIME_TYPES.contains(columnType.toLowerCase())) {
            return ValueType.DATE_TIME;
        }
        return ValueType.STRING;
    }

}
