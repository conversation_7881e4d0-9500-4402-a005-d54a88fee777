package com.iqiyi.vip.zeus.core.po.guard;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 业务信息实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BusinessPO {
    
    /**
     * 自增主键id
     */
    private Integer id;
    
    /**
     * 业务CODE
     */
    private String code;
    
    /**
     * 业务名称
     */
    private String name;
    
    /**
     * 业务描述
     */
    private String description;
    
    /**
     * 所属部门
     */
    private String department;
    
    /**
     * 部门邮件，支持多个
     */
    private String departmentEmails;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    /**
     * 状态,0:无效;1:有效
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
