package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/1/18 21:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "监控查询条件配置")
public class ZeusMonitorQueryCondition {

    /**
     * 条件Key
     */
    @ApiModelProperty(value = "条件字段Key")
    @NotNull(message = "条件字段Key不能为空")
    private String key;
    /**
     * 条件操作符
     */
    @ApiModelProperty(value = "条件操作符")
    @NotBlank(message = "条件操作符不能为空")
    private String operator;
    /**
     * 条件值
     */
    @ApiModelProperty(value = "条件值")
    private List<String> values;
    /**
     * 条件值类型，默认为字符串
     * @see com.iqiyi.vip.zeus.core.enums.ValueType
     */
    @ApiModelProperty(value = "条件值类型，默认为字符串")
    private String valueType;

    public ZeusMonitorQueryCondition(String key, String operator, List<String> values) {
        this.key = key;
        this.operator = operator;
        this.values = values;
    }

}
