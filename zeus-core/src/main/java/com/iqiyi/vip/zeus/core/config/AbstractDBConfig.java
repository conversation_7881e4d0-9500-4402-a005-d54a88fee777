package com.iqiyi.vip.zeus.core.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.util.Objects;

/**
 * @Author: <PERSON>
 * @Date: 2020/7/13
 */
public class AbstractDBConfig {
    //default maxLifeTime
    private static long MAX_LIFE_TIME_DEFAULT = 1800000;
    //default idleTimeout
    private static long IDLE_TIMEOUT_DEFAULT = 600000;
    //default 30000ms
    private static long CONNECTION_TIMEOUT_DEFAULT =  30000;

    private static int MAXIMUM_POOLSIZE_DEFAULT = 10;
    private static final String DRIVER_CLASS_NAME = "com.mysql.jdbc.Driver";

    protected DataSource dataSource(AbstractResourceConfig resourceConfig) {
        HikariConfig config = getHikariConfig(resourceConfig);
        //是否自定义配置，为true时下面两个参数才生效 ### 缓存开启
        config.addDataSourceProperty("cachePrepStmts", "true");
        //连接池大小默认25，官方推荐250-500
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        //单条语句最大长度默认256，官方推荐2048
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        //新版本MySQL支持服务器端准备，开启能够得到显著性能提升
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("useLocalTransactionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        return new HikariDataSource(config);
    }

    private static HikariConfig getHikariConfig(AbstractResourceConfig resourceConfig) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(resourceConfig.getUrl());
        config.setUsername(resourceConfig.getUsername());
        config.setPassword(resourceConfig.getPassword());
        config.setDriverClassName(StringUtils.isNoneBlank(resourceConfig.getDriverClassName()) ? resourceConfig.getDriverClassName() : DRIVER_CLASS_NAME);
        config.setMaximumPoolSize(Objects.isNull(resourceConfig.getMaximumPoolSize()) ? MAXIMUM_POOLSIZE_DEFAULT : resourceConfig.getMaximumPoolSize());
        config.setMaxLifetime(Objects.isNull(resourceConfig.getMaxLifetime()) ? MAX_LIFE_TIME_DEFAULT : resourceConfig.getMaxLifetime());
        config.setIdleTimeout(Objects.isNull(resourceConfig.getIdleTimeout()) ? IDLE_TIMEOUT_DEFAULT : resourceConfig.getIdleTimeout());
        config.setConnectionTimeout(Objects.isNull(resourceConfig.getConnectionTimeout()) ? CONNECTION_TIMEOUT_DEFAULT : resourceConfig.getConnectionTimeout());
        return config;
    }
}
