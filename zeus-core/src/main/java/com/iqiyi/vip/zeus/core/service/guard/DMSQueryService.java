package com.iqiyi.vip.zeus.core.service.guard;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.zeus.core.component.DMSClientComponent;
import com.iqiyi.vip.zeus.core.component.GuardItemDataQuery;
import com.iqiyi.vip.zeus.core.enums.GuardDataQueryType;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.core.po.DatasourceQueryResult;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.core.utils.NumberConvertUtils;

/**
 * @author: guojing
 * @date: 2025/9/11 19:23
 */
@Slf4j
@Service
public class DMSQueryService implements GuardItemDataQuery {

    @ConfigJsonValue("${day.column.list:[\"day\", \"date\"]}")
    private List<String> dayColumnList;

    @Resource
    private DMSClientComponent dmsClientComponent;

    @Override
    public GuardDataQueryType getGuardDataQueryType() {
        return GuardDataQueryType.DMS_API;
    }

    @Override
    public Map<String, OrderGuardianQueryData> queryData(GuardDatasource datasource, String querySql, String startDay, String endDay) {
        if (datasource == null || StringUtils.isBlank(querySql)) {
            return Collections.emptyMap();
        }
        GuardDatasourceType dataSourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (dataSourceType == null) {
            log.error("datasource type not found, type: {}, guardDatasource:{}", datasource.getType(), JacksonUtils.toJsonString(datasource));
            return Collections.emptyMap();
        }

        Map<String, String> values = new HashMap<>();
        values.put("startDay", startDay);
        values.put("endDay", endDay);
        values.put("startTime", startDay + " 00:00:00");
        values.put("endTime", endDay + " 23:59:59");
        String finalSql = new StringSubstitutor(values).replace(querySql);
        DatasourceQueryResult queryResult = dmsClientComponent.query(datasource, finalSql);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getColumnNames())
            || CollectionUtils.isEmpty(queryResult.getRowDataList())) {
            return Collections.emptyMap();
        }

        boolean hasDayColumn = dayColumnList.contains(queryResult.getColumnNames().get(0));
        Map<String, OrderGuardianQueryData> dataMap = new HashMap<>();
        for (List<Object> oneRowData : queryResult.getRowDataList()) {
            if (CollectionUtils.isEmpty(oneRowData)) {
                continue;
            }

            String day = hasDayColumn ? (String) oneRowData.get(0) : endDay;
            int valueStartIndex = hasDayColumn ? 1 : 0;
            Object countObj = oneRowData.get(valueStartIndex);
            Double count = NumberConvertUtils.convertToDouble(countObj);
            OrderGuardianQueryData dayData = new OrderGuardianQueryData(day, count);
            if (oneRowData.size() >= (hasDayColumn ? 3 : 2)) {
                Object secondCount = oneRowData.get(valueStartIndex + 1);
                if (secondCount == null) {
                    continue;
                }
                if (secondCount instanceof String) {
                    dayData.setSecondCount((String) secondCount);
                } else {
                    dayData.setSecondCount(String.valueOf(secondCount));
                }
            }
            dataMap.put(day, dayData);
        }

        return dataMap;
    }

    @Override
    public OrderGuardianDetailData queryDetailData(GuardDatasource datasource, String detailSql, String queryDay) {
        if (datasource == null || StringUtils.isBlank(detailSql)) {
            return null;
        }
        GuardDatasourceType dataSourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (dataSourceType == null) {
            log.error("datasource type not found, type: {}, guardDatasource:{}", datasource.getType(), JacksonUtils.toJsonString(datasource));
            return null;
        }

        Map<String, String> values = new HashMap<>();
        values.put("queryDay", queryDay);
        values.put("startTime", queryDay + " 00:00:00");
        values.put("endTime", queryDay + " 23:59:59");
        String finalSql = new StringSubstitutor(values).replace(detailSql);
        List<LinkedHashMap<String, Object>> queryResult = dmsClientComponent.queryDetail(datasource, finalSql);
        if (CollectionUtils.isEmpty(queryResult)) {
            return null;
        }
        return new OrderGuardianDetailData(finalSql, queryResult);
    }
}
