package com.iqiyi.vip.zeus.core.mapper.zeus;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.model.ColumnInfo;
import com.iqiyi.vip.zeus.core.model.DatasourceTableSchema;
import com.iqiyi.vip.zeus.core.po.DatasourceSchemaPO;

public interface DatasourceSchemaMapper {
    int deleteByPrimaryKey(Integer id);

    int deleteByDatasourceId(Integer datasourceId);

    int insert(DatasourceSchemaPO record);

    int batchInsert(List<DatasourceSchemaPO> records);

    DatasourceSchemaPO selectByPrimaryKey(Long id);

    DatasourceSchemaPO selectByTableAndColumn(@Param("datasourceId") Integer datasourceId, @Param("table") String table, @Param("column") String column);

    List<DatasourceTableSchema> selectByDatasourceId(@Param("datasourceId") Integer datasourceId, @Param("teamCode") String teamCode);

    List<ColumnInfo> selectByDatasourceIdAndTable(@Param("datasourceId") Integer datasourceId, @Param("table") String table, @Param("teamCode") String teamCode);

}