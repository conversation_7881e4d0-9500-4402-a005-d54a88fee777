package com.iqiyi.vip.zeus.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmartAlertRulePO {

    private Integer id;

    private Integer monitorId;

    private String ruleName;

    /**
     * 持续时间，单位秒
     */
    private Integer duration;
    /**
     * 检测频率，单位秒
     */
    private Integer checkFrequency;
    /**
     * 告警接接收人
     */
    private String receivers;

    private Integer status;

    private String extraData;

    private Timestamp createTime;

    private Timestamp updateTime;

}