package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.iqiyi.vip.zeus.core.enums.ValueType;
import com.iqiyi.vip.zeus.core.mapper.zeus.DatasourceSchemaMapper;
import com.iqiyi.vip.zeus.core.model.ColumnInfo;
import com.iqiyi.vip.zeus.core.model.DatasourceTableSchema;
import com.iqiyi.vip.zeus.core.po.zeus.DatasourceSchemaPO;
import com.iqiyi.vip.zeus.eagleclient.api.PrometheusSchemaApi;

/**
 * @author: guojing
 * @date: 2024/1/29 14:44
 */
@Profile("!sg")
@Service
public class DatasourceSchemaService {

    @Resource
    private DatasourceSchemaMapper datasourceSchemaMapper;
    @Resource
    private PrometheusSchemaApi prometheusSchemaApi;
    @Value("${datasource.schema.batch.insert.count:100}")
    private int batchInsertCount;

    @Transactional(transactionManager = "zeusTransactionManager", rollbackFor = Exception.class)
    public void refresh(List<DatasourceSchemaPO> datasourceTableSchemas) {
        if (CollectionUtils.isEmpty(datasourceTableSchemas)) {
            return;
        }

        datasourceSchemaMapper.deleteByDatasourceId(datasourceTableSchemas.get(0).getDatasourceId());
        if (datasourceTableSchemas.size() > batchInsertCount) {
            int totalSize = datasourceTableSchemas.size();
            int chunkCount = (int) Math.ceil(totalSize / (double) batchInsertCount);
            List<List<DatasourceSchemaPO>> partitionList = IntStream.range(0, chunkCount)
                .mapToObj(i -> datasourceTableSchemas.subList(i * batchInsertCount, Math.min(totalSize, (i + 1) * batchInsertCount)))
                .collect(Collectors.toList());
            for (List<DatasourceSchemaPO> datasourceSchemaPOS : partitionList) {
                datasourceSchemaMapper.batchInsert(datasourceSchemaPOS);
            }
        } else {
            datasourceSchemaMapper.batchInsert(datasourceTableSchemas);
        }
    }

    /**
     * 根据数据源ID获取数据源表和字段schema信息
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "DatasourceTableSchema_getTableSchemaByDatasourceId", cacheType= CacheType.LOCAL)
    public List<DatasourceTableSchema> getByDatasourceId(String teamCode, Integer datasourceId) {
        if (datasourceId == null) {
            return Collections.emptyList();
        }
        return datasourceSchemaMapper.selectByDatasourceId(datasourceId, teamCode);
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "ColumnInfo_getByDatasourceIdAndTable", cacheType= CacheType.LOCAL)
    public List<ColumnInfo> getByDatasourceIdAndTable(String teamCode, Integer datasourceId, String table) {
        if (datasourceId == null) {
            return Collections.emptyList();
        }
        return datasourceSchemaMapper.selectByDatasourceIdAndTable(datasourceId, table, teamCode);
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 900)
    @Cached(name = "DatasourceTableSchema_getTableSchemaByDatasourceId", cacheType= CacheType.LOCAL)
    public boolean datetimeColumn(Integer datasourceId, String table, String column) {
        if (datasourceId == null) {
            return false;
        }
        DatasourceSchemaPO datasourceSchemaPO = datasourceSchemaMapper.selectByTableAndColumn(datasourceId, table, column);
        if (datasourceSchemaPO == null) {
            return false;
        }
        return ValueType.DATE_TIME.getValue().equals(datasourceSchemaPO.getColumnType());
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 900)
    @Cached(name = "DatasourceSchemaService_getPrometheusLabelValues", cacheType= CacheType.LOCAL, cacheNullValue = true)
    public List<String> getPrometheusLabelValues(String eagleDatasourceUid, String teamCode, String metric, String label) {
        if (StringUtils.isBlank(eagleDatasourceUid)) {
            return Collections.emptyList();
        }
        return prometheusSchemaApi.getMetricLabelValues(eagleDatasourceUid, teamCode, metric, label);
    }

}
