package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.api.EagleNotificationApi;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertLevel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertNoticeType;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertReceiver;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertService;

/**
 * @author: guojing
 * @date: 2023/12/20 11:40
 */
@Profile("!sg")
@Service
public class ZeusAlertNotificationService {

    @Resource
    private EagleNotificationApi eagleNotificationApi;

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AlertReceiver_alertReceivers", cacheType= CacheType.LOCAL)
    public List<AlertReceiver> alertReceivers() {
        return eagleNotificationApi.getAlerReceiverList();
    }

    /**
     * 告警级别列表
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AlertLevel_alertLevels", cacheType= CacheType.LOCAL)
    public List<AlertLevel> alertLevels() {
        return eagleNotificationApi.getAlertLevel();
    }

    /**
     * 告警通知类型列表
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AlertNoticeType_alertNoticeTypes", cacheType= CacheType.LOCAL)
    public List<AlertNoticeType> alertNoticeTypes() {
        return eagleNotificationApi.getNoticeType();
    }

    /**
     * 服务列表
     */
    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AlertService_alertServices", cacheType= CacheType.LOCAL)
    public List<AlertService> alertServices() {
        return eagleNotificationApi.getAlertServiceList();
    }

}
