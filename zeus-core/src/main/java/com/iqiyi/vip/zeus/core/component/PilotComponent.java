package com.iqiyi.vip.zeus.core.component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.iqiyi.bigdata.pilot.rpc.client.TPilotStatement;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.po.DatasourceQueryResult;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * <AUTHOR>
 * @date 2022/3/23 上午 10:26
 */
@Component
@Slf4j
@Data
public class PilotComponent {

    @ConfigJsonValue("${pilot.hive.conn.conf:{\"hiveconf.mapreduce.job.queuename\": \"root.vip.order_analysis\",\"sparkconf.spark.sql.broadcastTimeout\": \"1000\",\"sparkconf.spark.driver.memory\": \"8g\",\"useSpark3\": \"true\",\"sparkconf.spark.sql.adaptive.coalescePartitions.enabled\": \"false\"}}")
    private Map<String, Object> pilotHiveConnConf;

    @Value("${pilot.hive.conn.url:**************************************************************************************************************************************;}")
    private String pilotHiveConnUrl;

    @ConfigJsonValue("${pilot.clickhouse.conn.conf:{\"clickhouseconf.token\": \"SMPpgMoeldH0drKhaNK7gif4qeSmBXyr\",\"clickhouseconf.database\": \"vip_order_analysis\",\"clickhouseconf.cluster\": \"vip_order_analysis\",\"clickhouseconf.project\": \"clickhouse\",\"clickhouseconf.env\": \"online\"}}")
    private Map<String, Object> pilotClickHouseConnConf;

    @Value("${pilot.clickhouse.conn.url:**************************************************************************;}")
    private String pilotClickHouseConnUrl;

    @ConfigJsonValue("${pilot.starrocks.conn.conf:{\"hadoopUser\":\"boss\",\"cluster\":\"sr-bdxs-viporder\",\"token\":\"a2ae1c2819be5743dbd63ed924caf7aecbc3eab2\"}}")
    private Map<String, Object> pilotStarRocksConnConf;

    @Value("${pilot.starrocks.conn.url:*******************************************************************************************************;}")
    private String pilotStarRocksConnUrl;
    
    /**
     * 创建连接
     * @param dataSourceType
     * @throws SQLException
     */
    private Connection createConnection(GuardDatasourceType dataSourceType) throws SQLException {
        Map<String, Object> connConf = null;
        String connUrl = null;
        if (dataSourceType == GuardDatasourceType.StarRocks) {
            connConf = pilotStarRocksConnConf;
            connUrl = pilotStarRocksConnUrl;
        }
        if (dataSourceType == GuardDatasourceType.ClickHouse) {
            connConf = pilotClickHouseConnConf;
            connUrl = pilotClickHouseConnUrl;
        }
        if (dataSourceType == GuardDatasourceType.Hive) {
            connConf = pilotHiveConnConf;
            connUrl = pilotHiveConnUrl;
        }
        Properties properties = new Properties();
        properties.putAll(connConf);
        log.info("use {} conn config from cloudConfig:{}", dataSourceType.getValue(), connConf);
        return DriverManager.getConnection(connUrl, properties);
    }

    /**
     * 查询数据
     * @param dataSourceType
     * @param querySql
     */
    public DatasourceQueryResult query(GuardDatasourceType dataSourceType, String querySql) {
        try (Connection connection = createConnection(dataSourceType)) {
            return executeQuery(dataSourceType, connection, querySql);
        } catch (SQLException sqlException) {
            log.error("{} query sql exception, querySql:{}", dataSourceType.getValue(), querySql, sqlException);
            return null;
        }
    }

    /**
     * 执行查询数据
     * @param dataSourceType
     * @param conn
     * @param querySql
     */
    private DatasourceQueryResult executeQuery(GuardDatasourceType dataSourceType, Connection conn, String querySql) {
        List<String> columnNames = new ArrayList<>();
        List<List<Object>> rowDataList = new ArrayList<>();
        try (TPilotStatement statement = (TPilotStatement) conn.createStatement()) {
            statement.setFetchDuration(500);
            if (dataSourceType == GuardDatasourceType.StarRocks) {
                statement.setQueryTimeout(60);
            }
            try (ResultSet resultSet = statement.executeQuery(querySql)) {
                if (resultSet == null) {
                    return null;
                }
                ResultSetMetaData rsMetaData = resultSet.getMetaData();
                int columnCount = rsMetaData != null ? rsMetaData.getColumnCount() : 0;
                if (columnCount == 0) {
                    return null;
                }
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(rsMetaData.getColumnName(i));
                }
                while (resultSet.next()) {
                    List<Object> oneRowData = new ArrayList<>();
                    for (String columnName : columnNames) {
                        oneRowData.add(resultSet.getObject(columnName));
                    }
                    rowDataList.add(oneRowData);
                }
            }
            log.info("executeQueryNew result size:{}, querySql:{}", rowDataList.size(), querySql);
        } catch (Exception e) {
            log.error("executeQueryNew fail，querySql:{}, error:", querySql, e);
        }
        return new DatasourceQueryResult(columnNames, rowDataList);
    }

    /**
     * 查询明细数据
     * @param dataSourceType
     * @param querySql
     */
    public List<LinkedHashMap<String, Object>> detailQuery(GuardDatasourceType dataSourceType, String querySql) {
        try (Connection connection = createConnection(dataSourceType)) {
            return executeDetailQuery(dataSourceType, connection, querySql);
        } catch (SQLException sqlException) {
            log.error("detailQuery sql exception, querySql:{}", querySql, sqlException);
            return Collections.emptyList();
        }
    }

    /**
     * 执行查询明细数据
     * @param dataSourceType
     * @param conn
     * @param querySql
     */
    private List<LinkedHashMap<String, Object>> executeDetailQuery(GuardDatasourceType dataSourceType, Connection conn, String querySql) {
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        try (TPilotStatement statement = (TPilotStatement) conn.createStatement()) {
            statement.setFetchDuration(500);
            if (dataSourceType == GuardDatasourceType.StarRocks) {
                statement.setQueryTimeout(60);
            }
            try (ResultSet resultSet = statement.executeQuery(querySql)) {
                if (resultSet == null) {
                    return dataList;
                }
                ResultSetMetaData rsMetaData = resultSet.getMetaData();
                int columnCount = rsMetaData != null ? rsMetaData.getColumnCount() : 0;
                if (columnCount == 0) {
                    return dataList;
                }
                List<String> columnNames = new ArrayList<>(columnCount);
                for (int i = 1; i <= columnCount; i++) {
                    columnNames.add(rsMetaData.getColumnName(i));
                }
                while (resultSet.next()) {
                    LinkedHashMap<String, Object> rowDataMap = new LinkedHashMap<>();
                    for (String columnName : columnNames) {
                        rowDataMap.put(columnName, resultSet.getObject(columnName));
                    }
                    dataList.add(rowDataMap);
                }
            }
            log.info("executeQuery result size:{}, querySql:{}", dataList.size(), querySql);
        } catch (Exception e) {
            log.error("executeQuery fail，querySql:{}, error:", querySql, e);
        }
        return dataList;
    }

    /**
     * 创建连接
     * @param connUrl
     * @param connConf
     * @throws SQLException
     */
    private Connection createConnection(String connUrl, Map<String, Object> connConf) throws SQLException {
        Properties properties = new Properties();
        properties.putAll(connConf);
        log.info("use {} conn config from cloudConfig:{}", connUrl, connConf);
        return DriverManager.getConnection(connUrl, properties);
    }

    /**
     * 查询数据
     * @param dataSourceType
     * @param querySql
     */
    public DatasourceQueryResult query(GuardDatasource datasource, String querySql) {
        GuardDatasourceType dataSourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (dataSourceType == null) {
            log.error("{} datasource type not found, guardDatasource:{}", datasource.getType(), JacksonUtils.toJsonString(datasource));
            return null;
        }
        try (Connection connection = createConnection(datasource.getConnUrl(), datasource.getConnConfig())) {
            return executeQuery(dataSourceType, connection, querySql);
        } catch (Exception sqlException) {
            log.error("query sql exception, querySql:{}, datasource:{}", querySql, JacksonUtils.toJsonString(datasource), sqlException);
            return null;
        }
    }

    /**
     * 查询明细数据
     * @param dataSourceType
     * @param querySql
     */
    public List<LinkedHashMap<String, Object>> detailQuery(GuardDatasource datasource, String querySql) {
        GuardDatasourceType dataSourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (dataSourceType == null) {
            log.error("{} datasource type not found, guardDatasource:{}", datasource.getType(), JacksonUtils.toJsonString(datasource));
            return null;
        }
        try (Connection connection = createConnection(datasource.getConnUrl(), datasource.getConnConfig())) {
            return executeDetailQuery(dataSourceType, connection, querySql);
        } catch (Exception sqlException) {
            log.error("detailQuery sql exception, querySql:{}, datasource:{}", querySql, JacksonUtils.toJsonString(datasource), sqlException);
            return Collections.emptyList();
        }
    }

}
