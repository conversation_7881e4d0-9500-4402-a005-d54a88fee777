package com.iqiyi.vip.zeus.core.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.manager.MonitorDAOManager;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.SmartAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.eagleclient.api.SmartAlertApi;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardMeta;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardPanel;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDashboardRequest;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;

/**
 * @author: guojing
 * @date: 2023/12/27 20:23
 */
@Profile("!sg")
@Service
public class ZeusMonitorAssembleService {

    @Resource
    private ZeusMonitorService monitorService;
    @Resource
    private ZeusAlertRuleService alertRuleService;
    @Resource
    private SmartAlertComponent smartAlertComponent;
    @Resource
    private MetricTemplateService metricTemplateService;
    @Resource
    private SmartAlertRuleService smartAlertRuleService;
    @Resource
    private EagleDashboardService eagleDashboardService;
    @Resource
    private MonitorDAOManager monitorDAOManager;
    @Resource
    private SmartAlertApi smartAlertApi;

    /**
     * 创建监控和告警规则
     */
    public Integer createMonitorAndAlertRule(ZeusMonitor monitorCreateParam, ZeusAlertRule alertRuleCreateParam,
        ZeusDatasource zeusDatasource, DashboardWithMeta dashboardWithMeta) {
        Integer monitorId = monitorService.create(monitorCreateParam, zeusDatasource, dashboardWithMeta);
        if (monitorId == null) {
            throw BizException.newSystemException("监控创建失败");
        }
        if (alertRuleCreateParam != null) {
            ZeusMonitor monitor = monitorService.getById(monitorId);
            alertRuleCreateParam.setMonitorId(monitorId);
            alertRuleCreateParam.setName(monitorCreateParam.getName());
            alertRuleCreateParam.setTeamCode(monitorCreateParam.getTeamCode());
            alertRuleCreateParam.setCreateUser(monitorCreateParam.getCreateUser());
            alertRuleCreateParam.setUpdateUser(monitorCreateParam.getCreateUser());
            alertRuleService.create(alertRuleCreateParam, monitor, dashboardWithMeta);

            //启用智能告警并且为Prometheus数据源时创建智能告警
            if (StringUtils.isNotBlank(alertRuleCreateParam.getSmartAlertRule()) && zeusDatasource.prometheusType()) {
                String receivers = StringUtils.isNotBlank(alertRuleCreateParam.getReceivers())
                    ? alertRuleCreateParam.getReceivers()
                    : monitorCreateParam.getCreateUser();
                SmartAlertRule smartAlertRule = SmartAlertRule.buildFromZeusRule(alertRuleCreateParam);
                smartAlertRule.setReceivers(receivers);
                saveOrUpdateSmartAlert(smartAlertRule, monitorCreateParam.getQuery());
            }
        }
        return monitorId;
    }

    /**
     * 保存或更新智能告警
     */
    private boolean saveOrUpdateSmartAlert(SmartAlertRule smartAlertRule, List<ZeusMonitorQuery> monitorQueries) {
        List<MetricTemplate> metricTemplates = monitorQueries.stream()
            .map(ZeusMonitorQuery::getMetricTmpId).distinct()
            .map(metricTmpId -> metricTemplateService.getById(metricTmpId))
            .filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, MetricTemplate> metricTemplateMap = metricTemplates.stream()
            .collect(Collectors.toMap(MetricTemplate::getId, metricTemplate -> metricTemplate));

        String smartAlertRuleConfigYaml = smartAlertComponent.buildRuleYaml(smartAlertRule, monitorQueries, metricTemplateMap);
        return smartAlertComponent.saveOrUpdate(smartAlertRule.getRuleName(), smartAlertRuleConfigYaml);
    }

    /**
     * 更新监控和告警规则
     */
    public boolean updateMonitorAndAlertRule(ZeusMonitor monitorUpdateParam, ZeusAlertRule alertRuleUpdateParam,
        ZeusMonitor monitorFromDB, ZeusDatasource zeusDatasource, DashboardWithMeta dashboardWithMeta
    ) {
        Integer monitorId = monitorUpdateParam.getId();
        monitorService.update(monitorUpdateParam, monitorFromDB, dashboardWithMeta);
        ZeusAlertRule zeusAlertRuleFromDB = alertRuleService.getByMonitor(monitorId);
        if (zeusAlertRuleFromDB != null && alertRuleUpdateParam != null && alertRuleUpdateParam.getId() == null) {
            throw BizException.newParamException("告警规则id不能为空");
        }
        if (alertRuleUpdateParam != null) {
            alertRuleUpdateParam.setMonitorId(monitorId);
            alertRuleUpdateParam.setName(monitorUpdateParam.getName());
            alertRuleUpdateParam.setTeamCode(monitorFromDB.getTeamCode());
            alertRuleUpdateParam.setUpdateUser(monitorFromDB.getCreateUser());
        }
        if (zeusAlertRuleFromDB == null && alertRuleUpdateParam != null) {
            ZeusMonitor latestMonitorFromDB = monitorService.getById(monitorId);
            alertRuleUpdateParam.setCreateUser(monitorFromDB.getCreateUser());
            alertRuleService.create(alertRuleUpdateParam, latestMonitorFromDB, dashboardWithMeta);

            //启用智能告警并且为Prometheus数据源时创建智能告警
            if (StringUtils.isNotBlank(alertRuleUpdateParam.getSmartAlertRule()) && zeusDatasource.prometheusType()) {
                String receivers = StringUtils.isNotBlank(alertRuleUpdateParam.getReceivers())
                    ? alertRuleUpdateParam.getReceivers()
                    : monitorUpdateParam.getUpdateUser();
                SmartAlertRule smartAlertRule = SmartAlertRule.buildFromZeusRule(alertRuleUpdateParam);
                smartAlertRule.setReceivers(receivers);
                saveOrUpdateSmartAlert(smartAlertRule, monitorUpdateParam.getQuery());
            }
        }
        if (zeusAlertRuleFromDB != null && alertRuleUpdateParam != null) {
            //智能告警规则名称不能修改
            if (StringUtils.isNotBlank(alertRuleUpdateParam.getSmartAlertRule())) {
                if (StringUtils.isNotBlank(zeusAlertRuleFromDB.getSmartAlertRule())) {
                    alertRuleUpdateParam.setSmartAlertRule(zeusAlertRuleFromDB.getSmartAlertRule());
                }
            } else {
                if (StringUtils.isNotBlank(zeusAlertRuleFromDB.getSmartAlertRule())) {
                    alertRuleUpdateParam.setSmartAlertRule(null);
                }
            }
            alertRuleService.update(alertRuleUpdateParam, monitorFromDB, dashboardWithMeta);

            if (zeusDatasource.prometheusType()) {
                //更新智能告警规则Yaml配置文件
                if (StringUtils.isNotBlank(alertRuleUpdateParam.getSmartAlertRule())) {
                    String receivers = StringUtils.isNotBlank(alertRuleUpdateParam.getReceivers())
                        ? alertRuleUpdateParam.getReceivers()
                        : monitorUpdateParam.getUpdateUser();
                    SmartAlertRule smartAlertRule = SmartAlertRule.buildFromZeusRule(alertRuleUpdateParam);
                    smartAlertRule.setReceivers(receivers);
                    saveOrUpdateSmartAlert(smartAlertRule, monitorUpdateParam.getQuery());
                } else {
                    //删除智能告警
                    if (StringUtils.isNotBlank(zeusAlertRuleFromDB.getSmartAlertRule())) {
                        smartAlertComponent.delete(zeusAlertRuleFromDB.getSmartAlertRule());
                    }
                }
            }
        }
        if (zeusAlertRuleFromDB != null && alertRuleUpdateParam == null) {
            alertRuleService.delete(zeusAlertRuleFromDB);
        }
        return true;
    }

    /**
     * 根据天眼请求创建监控和告警规则
     */
    public Integer createMonitorAndAlertRuleFromTianyan(
        ZeusMonitor monitorCreateParam,
        SmartAlertRule smartAlertRuleCreateParam,
        ZeusDatasource zeusDatasource,
        DashboardWithMeta dashboardWithMeta
    ) {
        Integer monitorId = monitorService.create(monitorCreateParam, zeusDatasource, dashboardWithMeta);
        if (monitorId == null) {
            throw BizException.newSystemException("监控创建失败");
        }
        if (smartAlertRuleCreateParam == null) {
            return monitorId;
        }

        boolean saved = saveOrUpdateSmartAlert(smartAlertRuleCreateParam, monitorCreateParam.getQuery());
        if (saved) {
            smartAlertRuleCreateParam.setMonitorId(monitorId);
            smartAlertRuleService.create(smartAlertRuleCreateParam);
        }
        return monitorId;
    }

    /**
     * 根据天眼请求更新监控和告警规则
     */
    public boolean updateMonitorAndAlertRuleFromTianyan(
        ZeusMonitor updateMonitorParam,
        SmartAlertRule updateSmartAlertRuleParam,
        ZeusMonitor monitorFromDB,
        DashboardWithMeta dashboardWithMeta
    ) {
        boolean updated = monitorService.update(updateMonitorParam, monitorFromDB, dashboardWithMeta);
        if (!updated) {
            return false;
        }
        if (updateSmartAlertRuleParam == null) {
            return true;
        }

        boolean saved = saveOrUpdateSmartAlert(updateSmartAlertRuleParam, updateMonitorParam.getQuery());
        if (saved) {
            smartAlertRuleService.saveOrUpdate(updateSmartAlertRuleParam);
        }
        return true;
    }

    public boolean deleteTianyanMonitor(ZeusMonitor zeusMonitor) {
        String dashboardUid = zeusMonitor.getDashboardUid();
        Integer panelId = zeusMonitor.getPanelId();
        DashboardWithMeta dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(dashboardUid);
        if (dashboardWithMeta != null && panelId != null) {
            Dashboard dashboard = dashboardWithMeta.getDashboard();
            List<DashboardPanel> panels = dashboard.getPanels();
            if (CollectionUtils.isNotEmpty(panels)) {
                List<DashboardPanel> dashboardPanels = panels.stream().filter(panel -> !Objects.equals(panel.getId(), panelId)).collect(Collectors.toList());
                dashboard.setPanels(dashboardPanels);
                DashboardMeta dashboardMeta = dashboardWithMeta.getMeta();
                String message = "删除Panel:" + zeusMonitor.getName();
                CreateDashboardRequest createDashboardRequest = CreateDashboardRequest.newUpdateInstance(dashboard, dashboardMeta.getFolderUid(), message);
                eagleDashboardService.createOrUpdateDashboard(createDashboardRequest);
            }
        }
        monitorDAOManager.deleteMonitor(zeusMonitor.getId());
        SmartAlertRule smartAlertRule = smartAlertRuleService.selectByMonitorId(zeusMonitor.getId());
        if (StringUtils.isNotBlank(smartAlertRule.getRuleName())) {
            smartAlertApi.deleteRule(smartAlertRule.getRuleName());
        }
        smartAlertRuleService.deleteByMonitorId(zeusMonitor.getId());
        return true;
    }

}
