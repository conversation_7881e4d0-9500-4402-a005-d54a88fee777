package com.iqiyi.vip.zeus.core.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: guojing
 * @date: 2023/12/11 16:07
 */
@Data
@ApiModel("项目搜索参数模型")
public class ProjectSearchParam {

    /**
     * 团队code
     */
    @ApiModelProperty(value = "团队code")
    @NotBlank(message = "团队Code不能为空")
    private String teamCode;

    /**
     * 项目名称, 英文字母
     */
    @ApiModelProperty(value = "项目名称,英文字母")
    private String name;

}
