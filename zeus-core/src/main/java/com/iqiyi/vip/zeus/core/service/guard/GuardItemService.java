package com.iqiyi.vip.zeus.core.service.guard;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.core.mapper.guard.GuardItemMapper;

/**
 * 稽核项服务类
 * 提供稽核项的增删改查功能
 * 
 * <AUTHOR>
 * @date 2025-09-08 10:00:00
 */
@Slf4j
@Service
public class GuardItemService {

    @Resource
    private GuardItemMapper guardItemMapper;

    /**
     * 查询指定数据源ID的稽核项数量
     * 
     * @param datasourceId 数据源ID
     * @return 稽核项数量
     */
    public int countByDatasourceId(Integer datasourceId) {
        return guardItemMapper.countByDatasourceId(datasourceId);
    }

}
