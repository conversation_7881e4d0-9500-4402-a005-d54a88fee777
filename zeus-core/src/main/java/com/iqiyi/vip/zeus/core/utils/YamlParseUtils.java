package com.iqiyi.vip.zeus.core.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator;
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;

import com.iqiyi.vip.zeus.core.model.smartalert.RuleConfig;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

/**
 * @author: guojing
 * @date: 2024/11/21 15:03
 */
@Slf4j
public class YamlParseUtils {

    private static final ObjectMapper MAPPER = new YAMLMapper().configure(YAMLGenerator.Feature.MINIMIZE_QUOTES, true);

    public static <T> T loadFromYamlFile(String resourcePath, Class<T> clazz) {
        if (StringUtils.isBlank(resourcePath)) {
            return null;
        }

        try {
            ClassPathResource classPathResource = new ClassPathResource(resourcePath);
            return MAPPER.readValue(classPathResource.getInputStream(), clazz);
        } catch (IOException e) {
            log.error("loadFromYamlFile occurred exception, resourcePath: {}", resourcePath, e);
            throw new RuntimeException("解析Yaml文件出现异常", e);
        }
    }

    public static RuleConfig yamlToObject(String yamlStr) {
        if (StringUtils.isBlank(yamlStr)) {
            return null;
        }

        try {
            return MAPPER.readValue(yamlStr, RuleConfig.class);
        } catch (JsonProcessingException e) {
            log.error("yamlToObject occurred exception, yamlStr: {}", yamlStr, e);
            throw new RuntimeException("解析Yaml文件出现异常", e);
        }
    }

    public static String objectToYaml(RuleConfig ruleConfig) {
        if (ruleConfig == null) {
            return null;
        }

        try {
            return MAPPER.writeValueAsString(ruleConfig);
        } catch (JsonProcessingException e) {
            log.error("objectToYaml occurred exception, ruleConfig: {}", JsonUtils.toJsonString(ruleConfig), e);
            throw new RuntimeException("解析Yaml文件出现异常", e);
        }
    }

}
