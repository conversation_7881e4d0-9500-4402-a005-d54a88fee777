package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardMeta;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardSearchResult;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;

/**
 * @author: guojing
 * @date: 2023/11/16 21:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "dashboard展示信息")
public class DashboardDisplayInfo {

    @ApiModelProperty(value = "dashboard uid")
    private String uid;
    @ApiModelProperty(value = "dashboard名称")
    private String title;
    @ApiModelProperty(value = "dashboard url")
    private String url;
    @ApiModelProperty(value = "dashboard所属Folder的uid")
    private String folderUid;
    @ApiModelProperty(value = "dashboard所属Folder的名称")
    private String folderTitle;
    @ApiModelProperty(value = "dashboard所属Folder的url")
    private String folderUrl;

    public static DashboardDisplayInfo buildFrom(String eagleServerDomain, DashboardSearchResult dashboardSearchResult) {
        return DashboardDisplayInfo.builder()
            .uid(dashboardSearchResult.getUid())
            .title(dashboardSearchResult.getTitle())
            .url(eagleServerDomain + dashboardSearchResult.getUrl())
            .folderUid(dashboardSearchResult.getFolderUid())
            .folderTitle(dashboardSearchResult.getFolderTitle())
            .folderUrl(eagleServerDomain + dashboardSearchResult.getFolderUrl())
            .build();
    }

    public static DashboardDisplayInfo buildFrom(String eagleServerDomain, DashboardWithMeta dashboardWithMeta) {
        Dashboard dashboard = dashboardWithMeta.getDashboard();
        DashboardMeta dashboardMeta = dashboardWithMeta.getMeta();
        return DashboardDisplayInfo.builder()
            .uid(dashboard.getUid())
            .title(dashboard.getTitle())
            .url(eagleServerDomain + dashboardMeta.getUrl())
            .folderUid(dashboardMeta.getFolderUid())
            .folderTitle(dashboardMeta.getFolderTitle())
            .folderUrl(eagleServerDomain + dashboardMeta.getFolderUrl())
            .build();
    }

}
