package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.UserSubscriptionPO;

/**
 * 稽核项订阅Mapper接口
 */
public interface UserSubscriptionMapper {
    
    /**
     * 根据ID查询订阅记录
     */
    UserSubscriptionPO selectById(@Param("id") Integer id);
    
    /**
     * 根据员工邮箱前缀和稽核项ID查询订阅记录
     */
    UserSubscriptionPO selectByOaNameAndGuardItemId(@Param("oaName") String oaName, 
                                                  @Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据员工邮箱前缀查询订阅记录列表
     */
    List<UserSubscriptionPO> selectByOaName(@Param("oaName") String oaName);
    
    /**
     * 根据稽核项ID查询订阅记录列表
     */
    List<UserSubscriptionPO> selectByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 分页查询订阅记录
     */
    List<UserSubscriptionPO> selectByPage(@Param("offset") Integer offset, 
                                        @Param("limit") Integer limit,
                                        @Param("oaName") String oaName,
                                        @Param("guardItemId") Integer guardItemId);
    
    /**
     * 统计订阅记录总数
     */
    Long countByCondition(@Param("oaName") String oaName,
                         @Param("guardItemId") Integer guardItemId);
    
    /**
     * 插入订阅记录
     */
    int insert(UserSubscriptionPO subscription);
    
    /**
     * 批量插入订阅记录
     */
    int batchInsert(@Param("list") List<UserSubscriptionPO> subscriptionList);
    
    /**
     * 更新订阅记录
     */
    int updateById(UserSubscriptionPO subscription);
    
    /**
     * 根据员工邮箱前缀和稽核项ID删除订阅记录
     */
    int deleteByOaNameAndGuardItemId(@Param("oaName") String oaName, 
                                   @Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据稽核项ID删除订阅记录
     */
    int deleteByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 检查订阅记录是否存在
     */
    boolean existsByOaNameAndGuardItemId(@Param("oaName") String oaName, 
                                       @Param("guardItemId") Integer guardItemId);
}