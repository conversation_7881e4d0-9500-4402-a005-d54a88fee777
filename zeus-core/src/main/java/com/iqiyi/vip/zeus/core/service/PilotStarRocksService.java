package com.iqiyi.vip.zeus.core.service;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.component.OrderGuardDataQuery;
import com.iqiyi.vip.zeus.core.component.PilotComponent;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.core.po.DatasourceQueryResult;
import com.iqiyi.vip.zeus.core.utils.NumberConvertUtils;

/**
 * @author: guojing
 * @date: 2025/5/26 17:31
 */
@Service
@Component
public class PilotStarRocksService implements OrderGuardDataQuery {

    @Resource
    private PilotComponent pilotComponent;

    @Override
    public GuardDatasourceType getDatasourceType() {
        return GuardDatasourceType.StarRocks;
    }

    @Override
    public Map<String, OrderGuardianQueryData> queryData(String querySql, String startDay, String endDay) {
        Map<String, String> values = new HashMap<>();
        values.put("startDay", startDay);
        values.put("endDay", endDay);
        String finalSql = new StringSubstitutor(values).replace(querySql);
        DatasourceQueryResult queryResult = pilotComponent.query(getDatasourceType(), finalSql);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getRowDataList())) {
            return Collections.emptyMap();
        }

        Map<String, OrderGuardianQueryData> dataMap = new HashMap<>();
        for (List<Object> oneRowData : queryResult.getRowDataList()) {
            if (CollectionUtils.isEmpty(oneRowData) || oneRowData.size() < 2) {
                continue;
            }

            String day = (String) oneRowData.get(0);
            Object countObj = oneRowData.get(1);
            Double count = NumberConvertUtils.convertToDouble(countObj);
            OrderGuardianQueryData dayData = new OrderGuardianQueryData(day, count);
            if (oneRowData.size() >= 3) {
                Object secondCount = oneRowData.get(2);
                if (secondCount == null) {
                    continue;
                }
                if (secondCount instanceof String) {
                    dayData.setSecondCount((String) secondCount);
                } else {
                    dayData.setSecondCount(String.valueOf(secondCount));
                }
            }
            dataMap.put(day, dayData);
        }

        return dataMap;
    }

    @Override
    public OrderGuardianDetailData queryDetailData(String detailSql, String queryDay) {
        if (StringUtils.isBlank(detailSql)) {
            return null;
        }
        Map<String, String> values = new HashMap<>();
        values.put("queryDay", queryDay);
        String finalSql = new StringSubstitutor(values).replace(detailSql);
        List<LinkedHashMap<String, Object>> queryResult = pilotComponent.detailQuery(getDatasourceType(), finalSql);
        return new OrderGuardianDetailData(finalSql, queryResult);
    }

}
