package com.iqiyi.vip.zeus.core.po.guard;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 稽核执行结果实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GuardExecResultPO {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 执行日期
     */
    private LocalDate day;
    
    /**
     * 稽核项ID
     */
    private Integer guardItemId;
    
    /**
     * 校验结果字段1的值
     */
    private BigDecimal firstValue;
    
    /**
     * 校验结果字段2的值
     */
    private String secondValue;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
