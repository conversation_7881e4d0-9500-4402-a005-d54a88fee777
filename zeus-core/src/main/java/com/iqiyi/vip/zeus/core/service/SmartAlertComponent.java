package com.iqiyi.vip.zeus.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;
import com.iqiyi.vip.zeus.core.enums.PrometheusMetricType;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.SmartAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.model.smartalert.MetricConfig;
import com.iqiyi.vip.zeus.core.model.smartalert.RuleConfig;
import com.iqiyi.vip.zeus.core.utils.PinyinUtils;
import com.iqiyi.vip.zeus.core.utils.PromQLUtils;
import com.iqiyi.vip.zeus.core.utils.YamlParseUtils;
import com.iqiyi.vip.zeus.eagleclient.api.SmartAlertApi;

/**
 * @author: guojing
 * @date: 2024/11/22 14:40
 */
@Profile("!sg")
@Slf4j
@Component
public class SmartAlertComponent {

    @Value("${baseline.judge.window.in.seconds:600}")
    private Integer judgeWindow;

    @Resource
    private SmartAlertApi smartAlertApi;

    /**
     * 组装智能告警规则Yaml配置文件
     */
    public String buildRuleYaml(SmartAlertRule smartAlertRule, List<ZeusMonitorQuery> monitorQueries, Map<Integer, MetricTemplate> metricTemplateMap) {
        if (smartAlertRule == null || StringUtils.isBlank(smartAlertRule.getRuleName()) || CollectionUtils.isEmpty(monitorQueries)) {
            return null;
        }
        List<String> receiverList = Arrays.asList(smartAlertRule.getReceivers().split(","));
        RuleConfig ruleConfig = YamlParseUtils.loadFromYamlFile("tmp/smart-alert-rule-tmp.yml", RuleConfig.class);
        ruleConfig.setName(smartAlertRule.getRuleName());
//        ruleConfig.setHubble_group("vip-order-analysis");
//        ruleConfig.setHubble_users(receiverList);
        ruleConfig.getNotice_configs().get(0).setUsers(receiverList);
        if (StringUtils.isNotBlank(smartAlertRule.getDuration())) {
            ruleConfig.setKeep_firing_for(smartAlertRule.getDuration());
        }
        if (StringUtils.isNotBlank(smartAlertRule.getCheckFrequency())) {
            ruleConfig.setInterval(smartAlertRule.getCheckFrequency());
            int seconds = OffsetTimeUnit.toSeconds(smartAlertRule.getCheckFrequency());
            ruleConfig.setBaseline_judge_wnd(judgeWindow / seconds);
        }
        MetricConfig metricConfigSample = ruleConfig.getMetric_configs().get(0);
        List<MetricConfig> metricConfigs = new ArrayList<>();
        for (int i = 0; i < monitorQueries.size(); i++) {
            ZeusMonitorQuery monitorQuery = monitorQueries.get(i);
            MetricTemplate currentMetricTemplate = metricTemplateMap.get(monitorQuery.getMetricTmpId());
            PrometheusMetricType prometheusMetricType = PrometheusMetricType.findByValue(currentMetricTemplate.getMetricType());
            String promQL = PromQLUtils.genPromQL(currentMetricTemplate, monitorQuery);
            MetricConfig metricConfig = metricConfigSample.copy();
            metricConfig.setName(String.format("%d-%s", i, monitorQuery.getSource()));
            metricConfig.setExpr(promQL);
            if (prometheusMetricType != null) {
                metricConfig.setMetric_type(prometheusMetricType.getSmartAlertMetricType());
            }
            metricConfigs.add(metricConfig);
        }
        ruleConfig.setMetric_configs(metricConfigs);
        return YamlParseUtils.objectToYaml(ruleConfig);
    }

    /**
     * 检查智能告警规则名称是否存在，存在重新生成
     * @param ruleName
     */
    public String checkAndGenerateRuleName(String ruleName) {
        String smartAlertRuleName = PinyinUtils.toPinYin(ruleName);
        boolean existed = smartAlertApi.exist(smartAlertRuleName);
        if (!existed) {
            return smartAlertRuleName;
        }
        return smartAlertRuleName + "_" + RandomStringUtils.randomNumeric(6);
    }

    public boolean exist(String rule) {
        return smartAlertApi.exist(rule);
    }

    public String getRuleConfig(String rule) {
        return smartAlertApi.getRuleConfig(rule);
    }

    public boolean saveOrUpdate(String rule, String ruleConfigYaml) {
        if (StringUtils.isBlank(ruleConfigYaml)) {
            return false;
        }

        return smartAlertApi.saveOrUpdate(rule, ruleConfigYaml);
    }

    public boolean delete(String rule) {
        if (StringUtils.isBlank(rule)) {
            return false;
        }
        smartAlertApi.deleteRule(rule);
        return true;
    }

}
