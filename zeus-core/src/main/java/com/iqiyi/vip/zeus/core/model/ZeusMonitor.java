package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.po.MonitorPO;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * 监控实体类
 * @author: guojing
 * @date: 2023/12/2 16:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("监控模型")
public class ZeusMonitor implements Serializable {

    /**
     * 不创建Record
     */
    public static final String NOT_CREATE_RECORD = "notCreateRecord";

    /**
     * 宙斯监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer id;
    /**
     * 监控名称
     */
    @ApiModelProperty(value = "监控名称")
    private String name;
    /**
     * 监控种类
     * @see com.iqiyi.vip.zeus.core.enums.MonitorCategory
     */
    @ApiModelProperty(value = "监控种类")
    private Integer category;
    /**
     * 宙斯数据源id
     */
    @ApiModelProperty(value = "监控数据源id")
    private Integer datasourceId;
    /**
     * 宙斯数据源类型
     */
    @ApiModelProperty(value = "监控数据源id")
    private String datasourceType;
    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String datasourceName;
    /**
     * 监控查询配置信息
     */
    @ApiModelProperty(value = "监控查询配置信息")
    private List<ZeusMonitorQuery> query;
    /**
     * 团队code
     */
    @ApiModelProperty(value = "监控所属团队code")
    private String teamCode;
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    private String teamName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;
    /**
     * 鹰眼Dashboard uid
     */
    @ApiModelProperty(value = "监控DashboardUid")
    private String dashboardUid;
    @ApiModelProperty(value = "dashboard展示信息")
    private DashboardDisplayInfo dashboardDisplayInfo;
    /**
     * 鹰眼监控面板id，dashboard下唯一
     */
    @ApiModelProperty(value = "监控PanelID")
    private Integer panelId;
    @ApiModelProperty(value = "监控Panel在鹰眼的url")
    private String panelIdUrl;

    private Integer status;
    /**
     * 扩展信息
     */
    private Map<String, Object> extraData;

    private Timestamp createTime;

    private Timestamp updateTime;

    public void setExtraDataItem(String key, Object value) {
        if (extraData == null) {
            extraData = new HashMap<>();
        }
        extraData.put(key, value);
    }

    public MonitorPO toMonitorPO() {
        return MonitorPO.builder()
            .id(id)
            .name(name)
            .category(category)
            .datasourceId(datasourceId)
            .query(JacksonUtils.toJsonString(query))
            .teamCode(teamCode)
            .createUser(createUser)
            .updateUser(updateUser)
            .dashboardUid(dashboardUid)
            .panelId(panelId)
            .status(status)
            .extraData(JacksonUtils.toJsonString(extraData))
            .createTime(createTime)
            .updateTime(updateTime)
            .build();
    }

    public static ZeusMonitor buildFrom(MonitorPO monitorPO) {
        if (monitorPO == null) {
            return null;
        }
        List<ZeusMonitorQuery> queryList = JacksonUtils.parseArray(monitorPO.getQuery(), ZeusMonitorQuery.class);
        if (CollectionUtils.isNotEmpty(queryList)) {
            queryList.forEach(query -> {
                query.setNeedTimeFilter(StringUtils.isNotBlank(query.getTimeFilter()));
                query.setNeedGroupBy(StringUtils.isNotBlank(query.getGroupBy()));
                query.setNeedDisplayName(StringUtils.isNotBlank(query.getDisplayName()));
            });
        }
        return ZeusMonitor.builder()
            .id(monitorPO.getId())
            .name(monitorPO.getName())
            .category(monitorPO.getCategory())
            .datasourceId(monitorPO.getDatasourceId())
            .query(queryList)
            .teamCode(monitorPO.getTeamCode())
            .createUser(monitorPO.getCreateUser())
            .updateUser(monitorPO.getUpdateUser())
            .dashboardUid(monitorPO.getDashboardUid())
            .panelId(monitorPO.getPanelId())
            .status(monitorPO.getStatus())
            .extraData(JacksonUtils.parseMap(monitorPO.getExtraData()))
            .createTime(monitorPO.getCreateTime())
            .updateTime(monitorPO.getUpdateTime())
            .build();
    }

}
