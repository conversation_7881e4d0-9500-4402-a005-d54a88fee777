package com.iqiyi.vip.zeus.core.component;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.ValueType;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.po.zeus.DatasourceSchemaPO;
import com.iqiyi.vip.zeus.core.service.DatasourceSchemaService;
import com.iqiyi.vip.zeus.eagleclient.api.PrometheusSchemaApi;
import com.iqiyi.vip.zeus.eagleclient.constants.PrometheusConstants;
import com.iqiyi.vip.zeus.eagleclient.model.MetricMetadata;

/**
 * 从Prometheus拉取metric的schema信息
 * @author: guojing
 * @date: 2024/1/29 15:33
 */
@Profile("!sg")
@Component
public class PrometheusSchemaPullerComponent implements DatasourceSchemaPuller {

    @Value("${support.metric.type.list:[\"counter\",\"gauge\"]}")
    private List<String> supportMetricTypeList;

    @Resource
    private PrometheusSchemaApi prometheusSchemaApi;
    @Resource
    private DatasourceSchemaService datasourceSchemaService;

    @Override
    public DataSourceType getDatasourceType() {
        return DataSourceType.Prometheus;
    }

    @Override
    public void pull(List<ZeusDatasource> datasourceList) {
        if (CollectionUtils.isEmpty(datasourceList)) {
            return;
        }

        for (ZeusDatasource datasource : datasourceList) {
            String datasourceUid = datasource.getEagleDatasourceUid();
            List<MetricMetadata> allMetrics = prometheusSchemaApi.getAllMetricsByUid(datasourceUid);
            if (CollectionUtils.isEmpty(allMetrics)) {
                continue;
            }

            List<DatasourceSchemaPO> prometheusSchemaList = new ArrayList<>();
            for (MetricMetadata metricMetadata : allMetrics) {
                if (!supportMetricTypeList.contains(metricMetadata.getType())
                    || metricMetadata.getName().endsWith(PrometheusConstants.METRIC_TYPE_COUNTER_SUFFIX)) {
                    continue;
                }
                String metric = buildMetricName(metricMetadata);
                List<String> metricLabels = prometheusSchemaApi.getMetricLabels(datasourceUid, metric);
                if (CollectionUtils.isEmpty(metricLabels)) {
                    continue;
                }
                if (notBusinessLabel(metricLabels)) {
                    continue;
                }
                List<String> teamCodeLabelValues = prometheusSchemaApi.getMetricLabelValues(datasourceUid, metric, PrometheusConstants.LABEL_FIELD_TEAM_CODE);
                if (CollectionUtils.isEmpty(teamCodeLabelValues)) {
                    continue;
                }
                for (String teamCode : teamCodeLabelValues) {
                    List<DatasourceSchemaPO> datasourceSchemaPOS = metricLabels.stream()
                        .filter(label -> !PrometheusConstants.LABEL_FIELD_NAME.equals(label))
                        .map(label -> new DatasourceSchemaPO(datasource.getId(), teamCode, metric, label, ValueType.STRING.getValue()))
                        .collect(Collectors.toList());
                    prometheusSchemaList.addAll(datasourceSchemaPOS);
                }
            }
            datasourceSchemaService.refresh(prometheusSchemaList);
        }
    }

    /**
     * Prometheus的metric元数据列表接口返回的metric名字不包含类型后缀，需要手动拼接
     * @param metricMetadata
     */
    private String buildMetricName(MetricMetadata metricMetadata) {
        String metric = metricMetadata.getName();
        return metric.endsWith(PrometheusConstants.METRIC_TYPE_COUNTER_SUFFIX) ? metric : metric + PrometheusConstants.METRIC_TYPE_COUNTER_SUFFIX;
    }

    private boolean notBusinessLabel(List<String> metricLabels) {
        return !metricLabels.contains(PrometheusConstants.LABEL_FIELD_TEAM_CODE) || !metricLabels.contains(PrometheusConstants.LABEL_FIELD_APPLICATION);
    }

}
