package com.iqiyi.vip.zeus.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import java.text.DecimalFormat;

/**
 * @author: guojing
 * @date: 2025/8/14 20:25
 */
@Slf4j
public class NumberConvertUtils {

    /**
     * 将数值对象转换为Double类型 支持Long、Integer、Float、Double、String等类型
     *
     * @param obj 数值对象
     * @return Double值，转换失败时返回
     */
    public static Double convertToDouble(Object obj) {
        if (obj == null) {
            return null;
        }
        if (obj instanceof Number) {
            return ((Number) obj).doubleValue();
        }
        if (obj instanceof String) {
            return NumberUtils.toDouble((String) obj, 0);
        }

        // 其他类型尝试通过字符串转换
        try {
            return NumberUtils.toDouble(obj.toString(), 0);
        } catch (Exception e) {
            return 0.0; // 或抛出自定义异常
        }
    }

    /**
     * 当小数部分为0时，仅输出整数部分；当小数部分超过2位时，仅保留2为输出
     * @param value
     */
    public static String formatDouble(double value) {
        DecimalFormat df = new DecimalFormat("#.##");
        return df.format(value);
    }

}
