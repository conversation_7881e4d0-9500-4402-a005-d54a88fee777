package com.iqiyi.vip.zeus.core.service.guard;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.mapper.guard.GuardDatasourceMapper;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.po.guard.GuardDatasourcePO;
import com.iqiyi.vip.zeus.core.req.guard.GuardDatasourceSearchParam;
import com.iqiyi.vip.zeus.core.resp.GuardDatasourceSearchResp;

/**
 * 数据源服务类
 * 提供数据源的增删改查功能
 * 
 * <AUTHOR>
 * @date 2025-09-08 10:00:00
 */
@Slf4j
@Service
public class GuardDatasourceService {

    @Resource
    private GuardDatasourceMapper guardDatasourceMapper;

    /**
     * 创建数据源
     * 
     * @param createParam 数据源信息
     * @return 影响行数
     */
    public Integer create(GuardDatasource createParam) {
        if (createParam == null) {
            return null;
        }

        GuardDatasourcePO po = createParam.toPO();
        try {
            int datasourceId = guardDatasourceMapper.insert(po);
            log.info("创建数据源成功，ID: {}, 名称: {}", po.getId(), po.getName());        
            return datasourceId;
        } catch (Exception e) {
            log.error("创建数据源失败，名称:{}", createParam.getName(), e);
            throw BizException.newSystemException("数据源创建失败，名称: " + createParam.getName());
        }
    }

    /**
     * 更新数据源
     * 
     * @param updateParam 数据源信息
     * @return 影响行数
     */
    public Boolean update(GuardDatasource updateParam) {
        if (updateParam == null) {
            return false;
        }

        GuardDatasourcePO po = updateParam.toPO();
        try {
            guardDatasourceMapper.updateById(po);
            log.info("更新数据源成功，ID: {}, 名称: {}", po.getId(), po.getName());
            return true;
        } catch (Exception e) {
            String errorMsg = "更新数据源失败，ID: " + updateParam.getId() + ", 名称: " + updateParam.getName();
            log.error(errorMsg, e);
            throw BizException.newSystemException(errorMsg);
        }
    }

    /**
     * 更新数据源状态
     * 
     * @param id     数据源ID
     * @param status 新状态
     */
    public boolean updateStatus(Integer id, Integer status) {
        try {
            guardDatasourceMapper.updateStatus(id, status);
            log.info("更新数据源状态成功，ID: {}, 状态: {}", id, status);
            return true;
        } catch (Exception e) {
            log.error("更新数据源状态失败，ID: {}", id, e);
            throw BizException.newSystemException("状态更新失败，ID: " + id);
        }
    }

    /**
     * 根据ID查询数据源
     * 
     * @param id 数据源ID
     * @return 数据源信息
     */
    public GuardDatasource getById(Integer id) {
        if (id == null) {
            return null;
        }
        GuardDatasourcePO selectById = guardDatasourceMapper.selectById(id);
        return GuardDatasource.fromPO(selectById);
    }

    /**
     * 根据名称查询数据源
     * 
     * @param name 数据源名称
     * @return 数据源信息
     */
    public GuardDatasource getByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        GuardDatasourcePO datasourcePO = guardDatasourceMapper.getByName(name);
        return GuardDatasource.fromPO(datasourcePO);
    }

    /**
     * 数据源名称是否可用
     * 
     * @param name 数据源名称
     * @return 数据源信息
     */
    public boolean nameAvailable(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        GuardDatasourcePO exists = guardDatasourceMapper.getByName(name);
        return exists == null;
    }

    /**
     * 查询数据源
     * 
     * @param searchParam 搜索参数
     * @return 数据源列表
     */
    public GuardDatasourceSearchResp search(GuardDatasourceSearchParam searchParam) {
        List<GuardDatasourcePO> searchResult = guardDatasourceMapper.search(searchParam);
        if (CollectionUtils.isEmpty(searchResult)) {
            return GuardDatasourceSearchResp.buildEmptyResp(searchParam.getPageNo(), searchParam.getPageSize());
        }

        int pageNo = searchParam.getPageNo() != null ? searchParam.getPageNo() : GuardDatasourceSearchParam.DEFAULT_PAGE_NO;
        int pageSize = searchParam.getPageSize() != null ? searchParam.getPageSize() : GuardDatasourceSearchParam.DEFAULT_PAGE_SIZE;
        int totalCount = searchResult.size();
        int fromIndex = Math.max((pageNo - 1) * pageSize, 0);
        int toIndex = Math.min(fromIndex + pageSize, totalCount);
        List<GuardDatasourcePO> pagedList = fromIndex < totalCount ? searchResult.subList(fromIndex, toIndex) : Collections.emptyList();
        return GuardDatasourceSearchResp.builder()
            .datasourceList(pagedList.stream().map(GuardDatasource::fromPO).collect(Collectors.toList()))
            .pageNo(pageNo)
            .pageSize(pageSize)
            .totalCount(totalCount)
            .totalPage(totalCount / pageSize)
            .build();
    }

}
