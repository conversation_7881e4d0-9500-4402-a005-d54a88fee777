package com.iqiyi.vip.zeus.core.service;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.mapper.zeus.MetricTemplateMapper;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.po.MetricTemplatePO;

/**
 * @author: guojing
 * @date: 2024/1/22 11:12
 */
@Profile("!sg")
@Service
public class MetricTemplateService {

    @Resource
    private MetricTemplateMapper metricTemplateMapper;

    public boolean delete(Integer id) {
        return metricTemplateMapper.deleteByPrimaryKey(id) > 0;
    }

    public Integer create(MetricTemplate metricTemplate) {
        MetricTemplatePO metricTemplatePO = metricTemplate.toMetricTemplatePO();
        metricTemplateMapper.insert(metricTemplatePO);
        return metricTemplatePO.getId();
    }

    public boolean update(MetricTemplate metricTemplate) {
        MetricTemplatePO metricTemplatePO = metricTemplate.toMetricTemplatePO();
        return metricTemplateMapper.updateByPrimaryKey(metricTemplatePO) > 0;
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "MetricTemplate_getById", cacheType= CacheType.LOCAL)
    public MetricTemplate getById(Integer id) {
        if (id == null) {
            return null;
        }
        MetricTemplatePO metricTemplatePO = metricTemplateMapper.selectByPrimaryKey(id);
        return MetricTemplate.buildFrom(metricTemplatePO);
    }

    public MetricTemplate getByName(String name) {
        MetricTemplatePO metricTemplatePO = metricTemplateMapper.selectByName(name);
        return MetricTemplate.buildFrom(metricTemplatePO);
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "MetricTemplate_listAll", cacheType= CacheType.LOCAL)
    public List<MetricTemplate> listAll() {
        List<MetricTemplatePO> metricTemplatePOList = metricTemplateMapper.selectAll();
        if (CollectionUtils.isEmpty(metricTemplatePOList)) {
            return Collections.emptyList();
        }
        return metricTemplatePOList.stream().map(MetricTemplate::buildFrom).collect(Collectors.toList());
    }

}
