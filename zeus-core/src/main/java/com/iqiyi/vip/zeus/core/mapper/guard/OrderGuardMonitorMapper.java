package com.iqiyi.vip.zeus.core.mapper.guard;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.OrderGuardMonitorPO;

public interface OrderGuardMonitorMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(OrderGuardMonitorPO record);

    int updateByPrimaryKey(OrderGuardMonitorPO record);

    OrderGuardMonitorPO selectByPrimaryKey(Integer id);

    List<OrderGuardMonitorPO> selectAll();

}