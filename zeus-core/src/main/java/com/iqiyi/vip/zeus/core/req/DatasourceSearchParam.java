package com.iqiyi.vip.zeus.core.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: guojing
 * @date: 2023/12/11 16:07
 */
@Data
@ApiModel("宙斯数据源搜索参数")
public class DatasourceSearchParam {

    /**
     * 团队code
     */
    @ApiModelProperty(value = "团队code")
    private String teamCode;
    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型")
    private String type;
    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String name;

}
