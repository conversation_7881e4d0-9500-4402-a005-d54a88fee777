package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;
import com.iqiyi.vip.zeus.core.po.zeus.AlertRulePO;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * @author: guojing
 * @date: 2023/12/2 16:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "告警规则")
public class ZeusAlertRule {

    /**
     * 告警规则id
     */
    @ApiModelProperty(value = "告警规则id")
    private Integer id;
    /**
     * 监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer monitorId;
    /**
     * 监控名称
     */
    @ApiModelProperty(value = "监控名称")
    private String monitorName;
    /**
     * 告警规则名称
     */
    @ApiModelProperty(value = "告警规则名称")
    private String name;
    /**
     * 团队code
     */
    @ApiModelProperty(value = "团队code")
    private String teamCode;
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    private String teamName;
    /**
     * 持续时间
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     * @see com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit
     */
    @ApiModelProperty(value = "持续时间，格式：\"(number)(unit)\", 比如：\"1m\", 或者：\"0\". 合法的单位: s, m, h, d, w")
    private String duration;
    /**
     * 检测频率
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     * @see com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit
     */
    @ApiModelProperty(value = "检测频率，格式：\"(number)(unit)\", 比如：\"1m\", 或者：\"0\". 合法的单位: s, m, h, d, w")
    private String checkFrequency;
    /**
     * 告警规则条件
     */
    @ApiModelProperty(value = "告警规则条件")
    private List<ZeusAlertRuleCondition> conditions;
    /**
     * 告警级别
     */
    @ApiModelProperty(value = "告警级别")
    private String level;
    /**
     * 告警接收人
     */
    @ApiModelProperty(value = "告警接收人")
    private String receivers;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;
    /**
     * 对应鹰眼上的告警规则uid
     */
    @ApiModelProperty(value = "对应鹰眼上的告警规则uid")
    private String eagleUid;
    /**
     * 智能告警规则名称
     */
    @ApiModelProperty(value = "智能告警规则名称")
    private String smartAlertRule;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;
    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
    private Map<String, Object> extraData;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    public AlertRulePO toAlertRulePO() {
        return AlertRulePO.builder()
            .id(id)
            .monitorId(monitorId)
            .name(name)
            .teamCode(teamCode)
            .duration(OffsetTimeUnit.toSeconds(duration))
            .checkFrequency(OffsetTimeUnit.toSeconds(checkFrequency))
            .conditions(JacksonUtils.toJsonString(conditions))
            .level(level)
            .receivers(receivers)
            .createUser(createUser)
            .updateUser(updateUser)
            .eagleUid(eagleUid)
            .smartAlertRule(smartAlertRule)
            .status(status)
            .extraData(JacksonUtils.toJsonString(extraData))
            .createTime(createTime)
            .updateTime(updateTime)
            .build();
    }

    public static ZeusAlertRule buildFrom(AlertRulePO alertRulePO) {
        if (alertRulePO == null) {
            return null;
        }
        return ZeusAlertRule.builder()
            .id(alertRulePO.getId())
            .monitorId(alertRulePO.getMonitorId())
            .name(alertRulePO.getName())
            .teamCode(alertRulePO.getTeamCode())
            .duration(OffsetTimeUnit.toOffsetTimeUnitName(alertRulePO.getDuration()))
            .checkFrequency(OffsetTimeUnit.toOffsetTimeUnitName(alertRulePO.getCheckFrequency()))
            .conditions(JacksonUtils.parseArray(alertRulePO.getConditions(), ZeusAlertRuleCondition.class))
            .level(alertRulePO.getLevel())
            .receivers(alertRulePO.getReceivers())
            .createUser(alertRulePO.getCreateUser())
            .updateUser(alertRulePO.getUpdateUser())
            .eagleUid(alertRulePO.getEagleUid())
            .smartAlertRule(alertRulePO.getSmartAlertRule())
            .status(alertRulePO.getStatus())
            .extraData(JacksonUtils.parseMap(alertRulePO.getExtraData()))
            .createTime(alertRulePO.getCreateTime())
            .updateTime(alertRulePO.getUpdateTime())
            .build();
    }

}
