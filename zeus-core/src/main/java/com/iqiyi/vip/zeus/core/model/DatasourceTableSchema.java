package com.iqiyi.vip.zeus.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2024/2/4 10:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasourceTableSchema {

    /**
     * 数据表名
     */
    private String table;
    /**
     * 数据表字段列表
     */
    private List<ColumnInfo> columnList;

}
