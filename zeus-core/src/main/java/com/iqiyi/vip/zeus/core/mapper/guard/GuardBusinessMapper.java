package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.GuardBusinessPO;

/**
 * 稽核项-业务关联Mapper接口
 */
public interface GuardBusinessMapper {
    
    /**
     * 根据ID查询关联关系
     */
    GuardBusinessPO selectById(@Param("id") Integer id);
    
    /**
     * 根据稽核项ID查询关联的业务列表
     */
    List<GuardBusinessPO> selectByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据业务ID查询关联的稽核项列表
     */
    List<GuardBusinessPO> selectByBusinessId(@Param("businessId") Integer businessId);
    
    /**
     * 根据稽核项ID和业务ID查询关联关系
     */
    GuardBusinessPO selectByGuardItemIdAndBusinessId(@Param("guardItemId") Integer guardItemId, 
                                                  @Param("businessId") Integer businessId);
    
    /**
     * 插入关联关系
     */
    int insert(GuardBusinessPO guardBusiness);
    
    /**
     * 批量插入关联关系
     */
    int batchInsert(@Param("list") List<GuardBusinessPO> guardBusinessList);
    
    /**
     * 更新关联关系
     */
    int updateById(GuardBusinessPO guardBusiness);
    
    /**
     * 根据ID删除关联关系（逻辑删除）
     */
    int deleteById(@Param("id") Integer id);
    
    /**
     * 根据稽核项ID删除关联关系
     */
    int deleteByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据业务ID删除关联关系
     */
    int deleteByBusinessId(@Param("businessId") Integer businessId);
    
    /**
     * 根据稽核项ID和业务ID删除关联关系
     */
    int deleteByGuardItemIdAndBusinessId(@Param("guardItemId") Integer guardItemId, 
                                        @Param("businessId") Integer businessId);
}