package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.GuardExecResultPO;

/**
 * 稽核执行结果Mapper接口
 */
public interface GuardExecResultMapper {
    
    /**
     * 根据ID查询执行结果
     */
    GuardExecResultPO selectById(@Param("id") Long id);
    
    /**
     * 根据日期和稽核项ID查询执行结果
     */
    GuardExecResultPO selectByDayAndGuardItemId(@Param("day") LocalDate day, 
                                             @Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据稽核项ID查询执行结果列表
     */
    List<GuardExecResultPO> selectByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 根据日期范围查询执行结果列表
     */
    List<GuardExecResultPO> selectByDateRange(@Param("startDate") LocalDate startDate, 
                                            @Param("endDate") LocalDate endDate,
                                            @Param("guardItemId") Integer guardItemId);
    
    /**
     * 分页查询执行结果
     */
    List<GuardExecResultPO> selectByPage(@Param("offset") Integer offset,
                                       @Param("limit") Integer limit,
                                       @Param("guardItemId") Integer guardItemId,
                                       @Param("startDate") LocalDate startDate,
                                       @Param("endDate") LocalDate endDate);
    
    /**
     * 统计执行结果总数
     */
    Long countByCondition(@Param("guardItemId") Integer guardItemId,
                         @Param("startDate") LocalDate startDate,
                         @Param("endDate") LocalDate endDate);
    
    /**
     * 插入执行结果
     */
    int insert(GuardExecResultPO execResult);
    
    /**
     * 批量插入执行结果
     */
    int batchInsert(@Param("list") List<GuardExecResultPO> execResultList);
    
    /**
     * 更新执行结果
     */
    int updateById(GuardExecResultPO execResult);
    
    /**
     * 根据日期和稽核项ID更新执行结果
     */
    int updateByDayAndGuardItemId(GuardExecResultPO execResult);
    
    /**
     * 根据ID删除执行结果
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据稽核项ID删除执行结果
     */
    int deleteByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 查询稽核项最新的执行结果
     */
    GuardExecResultPO selectLatestByGuardItemId(@Param("guardItemId") Integer guardItemId);
    
    /**
     * 统计稽核项执行结果数量
     */
    Long countByGuardItemId(@Param("guardItemId") Integer guardItemId);
}