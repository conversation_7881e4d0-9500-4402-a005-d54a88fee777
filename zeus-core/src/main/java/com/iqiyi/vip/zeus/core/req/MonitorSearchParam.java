package com.iqiyi.vip.zeus.core.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: guojing
 * @date: 2023/12/11 16:07
 */
@Data
@ApiModel("宙斯监控搜索参数")
public class MonitorSearchParam {

    /**
     * 团队code
     */
    @ApiModelProperty(value = "团队code")
    private String teamCode;
    /**
     * 监控种类
     */
    @ApiModelProperty(value = "监控种类")
    private Integer category;
    /**
     * 监控名称,模糊查询
     */
    @ApiModelProperty(value = "监控名称,模糊查询")
    private String name;
    /**
     * dashboard uid
     */
    @ApiModelProperty(value = "Dashboard uid")
    private String dashboardUid;
    /**
     * 监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer monitorId;

}
