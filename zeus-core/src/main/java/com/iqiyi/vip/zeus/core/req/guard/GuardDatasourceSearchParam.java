package com.iqiyi.vip.zeus.core.req.guard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据源搜索参数
 * 
 * <AUTHOR>
 * @date 2025-09-08 10:00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("业务稽核数据源搜索参数")
public class GuardDatasourceSearchParam {

    public static final int DEFAULT_PAGE_NO = 1;
    public static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer pageNo;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize;

    /**
     * 数据源id
     */
    @ApiModelProperty(value = "数据源id", example = "1")
    private Integer id;

    /**
     * 数据源名称(模糊匹配)
     */
    @ApiModelProperty(value = "数据源名称(模糊匹配)", example = "MySQL测试库")
    private String partName;

    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型", example = "MYSQL")
    private String type;

    /**
     * 数据源连接url(模糊匹配)
     */
    @ApiModelProperty(value = "数据源连接url(模糊匹配)")
    private String partConnUrl;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人", example = "zhangsan")
    private String operateUser;

    /**
     * 状态,0:无效;1:有效
     */
    @ApiModelProperty(value = "状态,0:无效;1:有效", example = "1", allowableValues = "0,1")
    private Integer status;

}
