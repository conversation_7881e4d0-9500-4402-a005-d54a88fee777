package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.BusinessPO;

/**
 * 业务信息Mapper接口
 */
public interface BusinessMapper {
    
    /**
     * 根据ID查询业务信息
     */
    BusinessPO selectById(@Param("id") Integer id);
    
    /**
     * 根据业务CODE查询业务信息
     */
    BusinessPO selectByCode(@Param("code") String code);
    
    /**
     * 根据业务名称查询业务信息
     */
    BusinessPO selectByName(@Param("name") String name);
    
    /**
     * 查询所有有效的业务信息
     */
    List<BusinessPO> selectAllValid();
    
    /**
     * 分页查询业务信息
     */
    List<BusinessPO> selectByPage(@Param("offset") Integer offset, 
                               @Param("limit") Integer limit,
                               @Param("keyword") String keyword);
    
    /**
     * 统计业务信息总数
     */
    Long countByCondition(@Param("keyword") String keyword);
    
    /**
     * 插入业务信息
     */
    int insert(BusinessPO business);
    
    /**
     * 更新业务信息
     */
    int updateById(BusinessPO business);
    
    /**
     * 根据ID删除业务信息（逻辑删除）
     */
    int deleteById(@Param("id") Integer id);
    
    /**
     * 批量插入业务信息
     */
    int batchInsert(@Param("list") List<BusinessPO> businessList);
}