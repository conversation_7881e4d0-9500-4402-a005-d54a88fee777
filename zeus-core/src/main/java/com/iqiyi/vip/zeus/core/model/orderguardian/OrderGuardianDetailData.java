package com.iqiyi.vip.zeus.core.model.orderguardian;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @author: guojing
 * @date: 2025/6/17 10:12
 */
@NoArgsConstructor
@Data
public class OrderGuardianDetailData {

    /**
     * 明细查询SQL
     */
    private String detailSql;

//    private List<String> columnNames;

//    private List<List<Object>> rowDataList;

    private List<LinkedHashMap<String, Object>> queryResult;

    public OrderGuardianDetailData(String detailSql, List<LinkedHashMap<String, Object>> queryResult) {
        this.detailSql = detailSql;
        this.queryResult = queryResult;
    }
}
