package com.iqiyi.vip.zeus.core.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;

@Data
@NoArgsConstructor
public class TimeIntervalPair {

    private static final String OFFSET_TIMEUNIT_NAMES = Arrays.stream(OffsetTimeUnit.values()).map(Enum::name).collect(Collectors.joining(""));

    private final static Pattern CHECK_FREQUENCY_STR_PATTERN = Pattern.compile("^[1-9]\\d*[" + OFFSET_TIMEUNIT_NAMES + "]$");

    private int timeValue;
    private OffsetTimeUnit timeUnit;

    public TimeIntervalPair(Integer timeValue, OffsetTimeUnit timeUnit) {
        this.timeValue = timeValue;
        this.timeUnit = timeUnit;
    }

    /**
     * @param timeIntervalStr 格式：1s、1m、1h、1d、1w
     */
    public static TimeIntervalPair of(String timeIntervalStr) {
        if (StringUtils.isBlank(timeIntervalStr)) {
            return null;
        }
        if (!CHECK_FREQUENCY_STR_PATTERN.matcher(timeIntervalStr).matches()) {
            return null;
        }
        int lastCharIndex = timeIntervalStr.length() - 1;
        int numberPart = Integer.parseInt(timeIntervalStr.substring(0, lastCharIndex));
        if (numberPart <= 0) {
            return null;
        }
        OffsetTimeUnit timeUnit = OffsetTimeUnit.valueOf(timeIntervalStr.substring(lastCharIndex));
        return new TimeIntervalPair(numberPart, timeUnit);
    }

    public boolean secondUnit() {
        return this.timeUnit == OffsetTimeUnit.s;
    }

    public boolean minuteUnit() {
        return this.timeUnit == OffsetTimeUnit.m;
    }

    public boolean hourUnit() {
        return this.timeUnit == OffsetTimeUnit.h;
    }

}
