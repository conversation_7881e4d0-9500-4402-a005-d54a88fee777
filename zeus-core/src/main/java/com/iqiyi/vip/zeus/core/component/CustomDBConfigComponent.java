package com.iqiyi.vip.zeus.core.component;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.config.AbstractDBConfig;
import com.iqiyi.vip.zeus.core.config.AbstractResourceConfig;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;

/**
 * @author: guojing
 * @date: 2024/2/4 14:35
 */
@Profile("!sg")
@Slf4j
@Component
public class CustomDBConfigComponent extends AbstractDBConfig {

    private static final String INFORMATION_SCHEMA_JDBC_URL_TEMPLATE = "jdbc:mysql://{0}/{1}?useUnicode=true&characterEncoding=UTF-8&characterSetResults=UTF-8&autoReconnect=true&useAffectedRows=true&zeroDateTimeBehavior=convertToNull";

    private static Resource[] MAPPER_RESOURCES;

    static {
        try {
            MAPPER_RESOURCES = new PathMatchingResourcePatternResolver().getResources("classpath:mapper/mysql/*.xml");
        } catch (IOException e) {
            log.error("load MysqlInformationSchemaMapper.xml error", e);
        }
    }

    private AbstractResourceConfig genResourceConfig(ZeusDatasource zeusDatasource, String databaseName) {
        if (StringUtils.isBlank(databaseName)) {
            databaseName = zeusDatasource.getDatabaseItem();
        }
        AbstractResourceConfig resourceConfig = new AbstractResourceConfig();
        String jdbcUrl = MessageFormat.format(INFORMATION_SCHEMA_JDBC_URL_TEMPLATE, zeusDatasource.getUrl(), databaseName);
        resourceConfig.setUrl(jdbcUrl);
        resourceConfig.setUsername(zeusDatasource.getUserItem());
        resourceConfig.setPassword(zeusDatasource.getPasswordItem());
        return resourceConfig;
    }

    private DataSource genDataSource(AbstractResourceConfig resourceConfig) {
        return super.dataSource(resourceConfig);
    }

    private SqlSessionFactory genSqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(MAPPER_RESOURCES);
        Objects.requireNonNull(sqlSessionFactoryBean.getObject()).getConfiguration().setMapUnderscoreToCamelCase(true);
        return sqlSessionFactoryBean.getObject();
    }

    public SqlSessionTemplate genSqlSessionTemplate(ZeusDatasource zeusDatasource) {
        return genSqlSessionTemplate(zeusDatasource, null);
    }

    public SqlSessionTemplate genSqlSessionTemplate(ZeusDatasource zeusDatasource, String databaseName) {
        try {
            log.info("start create SqlSessionTemplate, datasourceName: {}", zeusDatasource.getName());
            AbstractResourceConfig resourceConfig = genResourceConfig(zeusDatasource, databaseName);
            DataSource dataSource = genDataSource(resourceConfig);
            SqlSessionFactory sqlSessionFactory = genSqlSessionFactory(dataSource);
            SqlSessionTemplate sqlSessionTemplate = new SqlSessionTemplate(sqlSessionFactory);
            log.info("success created SqlSessionTemplate, datasourceName: {}", zeusDatasource.getName());
            return sqlSessionTemplate;
        } catch (Exception e) {
            log.error("create SqlSessionTemplate occurred exception, datasourceName: {}", zeusDatasource.getName(), e);
            return null;
        }
    }

}
