package com.iqiyi.vip.zeus.core.mapper.zeus;

import java.util.List;

import com.iqiyi.vip.zeus.core.po.MetricTemplatePO;

public interface MetricTemplateMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(MetricTemplatePO record);

    int updateByPrimaryKey(MetricTemplatePO record);

    MetricTemplatePO selectByPrimaryKey(Integer id);

    MetricTemplatePO selectByName(String name);

    List<MetricTemplatePO> selectAll();

}