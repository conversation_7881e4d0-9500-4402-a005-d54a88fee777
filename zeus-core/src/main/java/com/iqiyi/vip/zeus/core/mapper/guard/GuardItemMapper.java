package com.iqiyi.vip.zeus.core.mapper.guard;

import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

import com.iqiyi.vip.zeus.core.po.guard.GuardItemPO;

/**
 * 稽核项Mapper接口
 */
public interface GuardItemMapper {

    /**
     * 插入稽核项
     */
    int insert(GuardItemPO guardItem);
    
    /**
     * 更新稽核项
     */
    int updateById(GuardItemPO guardItem);
    
    /**
     * 根据ID删除稽核项（逻辑删除）
     */
    int deleteById(@Param("id") Integer id);
    
    /**
     * 根据ID查询稽核项
     */
    GuardItemPO selectById(@Param("id") Integer id);
    
    /**
     * 根据稽核项名称查询稽核项
     */
    GuardItemPO selectByName(@Param("name") String name);
    
    /**
     * 根据数据源ID查询稽核项列表
     */
    List<GuardItemPO> selectByDatasourceId(@Param("datasourceId") Integer datasourceId);

    /**
     * 根据数据源ID查询稽核项数量
     */
    int countByDatasourceId(@Param("datasourceId") Integer datasourceId);
    
    /**
     * 根据稽核维度查询稽核项列表
     */
    List<GuardItemPO> selectByDimension(@Param("dimension") String dimension);
    
    /**
     * 查询所有有效的稽核项
     */
    List<GuardItemPO> selectAllValid();
    
    /**
     * 分页查询稽核项
     */
    List<GuardItemPO> selectByPage(@Param("offset") Integer offset, 
                                 @Param("limit") Integer limit,
                                 @Param("keyword") String keyword,
                                 @Param("datasourceId") Integer datasourceId,
                                 @Param("dimension") String dimension);
    
    /**
     * 统计稽核项总数
     */
    Long countByCondition(@Param("keyword") String keyword,
                        @Param("datasourceId") Integer datasourceId,
                        @Param("dimension") String dimension);
    
    /**
     * 根据业务ID查询稽核项列表
     */
    List<GuardItemPO> selectByBusinessId(@Param("businessId") Integer businessId);
    
    /**
     * 根据用户订阅查询稽核项列表
     */
    List<GuardItemPO> selectByUserSubscription(@Param("oaName") String oaName);
    
    /**
     * 查询需要执行的稽核项列表
     */
    List<GuardItemPO> selectForExecution(@Param("execDate") LocalDate execDate);
}