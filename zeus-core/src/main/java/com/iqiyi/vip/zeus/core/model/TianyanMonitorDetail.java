package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * 监控实体类
 * @author: guojing
 * @date: 2023/12/2 16:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("天眼监控详情")
public class TianyanMonitorDetail {

    /**
     * 监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer id;
    /**
     * 监控名称
     */
    @ApiModelProperty(value = "监控名称")
    private String name;
    /**
     * 监控种类
     * @see com.iqiyi.vip.zeus.core.enums.MonitorCategory
     */
    @ApiModelProperty(value = "监控种类")
    private Integer category;
    /**
     * 宙斯数据源id
     */
    @ApiModelProperty(value = "监控数据源id")
    private Integer datasourceId;
    /**
     * 宙斯数据源类型
     */
    @ApiModelProperty(value = "监控数据源id")
    private String datasourceType;
    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称")
    private String datasourceName;
    /**
     * 监控查询配置信息
     */
    @ApiModelProperty(value = "监控查询配置信息")
    private List<ZeusMonitorQuery> query;
    /**
     * 团队code
     */
    @ApiModelProperty(value = "监控所属团队code")
    private String teamCode;
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    private String teamName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;
    /**
     * 鹰眼Dashboard uid
     */
    @ApiModelProperty(value = "监控DashboardUid")
    private String dashboardUid;
    @ApiModelProperty(value = "dashboard展示信息")
    private DashboardDisplayInfo dashboardDisplayInfo;
    /**
     * 鹰眼监控面板id，dashboard下唯一
     */
    @ApiModelProperty(value = "监控PanelID")
    private Integer panelId;
    @ApiModelProperty(value = "监控Panel在鹰眼的url")
    private String panelIdUrl;
    @ApiModelProperty(value = "监控状态，0:无效，1:有效")
    private Integer status;
    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "监控扩展信息")
    private Map<String, Object> extraData;
    @ApiModelProperty(value = "监控创建时间")
    private Timestamp createTime;
    @ApiModelProperty(value = "监控更新时间")
    private Timestamp updateTime;
    @ApiModelProperty(value = "智能告警规则信息")
    private SmartAlertRule smartAlertRule;

}
