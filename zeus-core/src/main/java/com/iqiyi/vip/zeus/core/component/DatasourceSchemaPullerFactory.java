package com.iqiyi.vip.zeus.core.component;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;

/**
 * @author: guojing
 * @date: 2024/2/2 13:53
 */
@Profile("!sg")
@Slf4j
@Lazy(value = false)
@Component
public class DatasourceSchemaPullerFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<DataSourceType, DatasourceSchemaPuller> datasourceSchemaPullerMap = Maps.newEnumMap(DataSourceType.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("DatasourceSchemaPullFactory-init start!");
        applicationContext.getBeansOfType(DatasourceSchemaPuller.class).values()
            .forEach(puller -> {
                log.info("DatasourceSchemaPullFactory-init:register handler {}  for {}",
                    puller.getClass().getSimpleName(), puller.getDatasourceType());
                datasourceSchemaPullerMap.put(puller.getDatasourceType(), puller);
            });
        log.info("DatasourceSchemaPullFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public DatasourceSchemaPuller getPuller(DataSourceType dataSourceType) {
        return datasourceSchemaPullerMap.get(dataSourceType);
    }

}
