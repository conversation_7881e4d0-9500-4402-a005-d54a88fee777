package com.iqiyi.vip.zeus.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * @author: guojing
 * @date: 2024/4/7 17:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorExtraData {

    /**
     * 查询对应的prometheus record
     */
    private Map<String, Object> promRecordMap;

    public static MonitorExtraData buildFrom(Map<String, Object> objectMap) {
        String jsonString = JacksonUtils.toJsonString(objectMap);
        return JacksonUtils.parseObject(jsonString, MonitorExtraData.class);
    }

}
