package com.iqiyi.vip.zeus.core.po.guard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 稽核项实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardItemPO {
    
    /**
     * 自增主键id
     */
    private Integer id;
    
    /**
     * 名称
     */
    private String name;
    
    /**
     * 数据源类型
     */
    private String datasourceType;
    
    /**
     * 数据源ID
     */
    private Integer datasourceId;
    
    /**
     * 校验规则SQL
     */
    private String checkSql;
    
    /**
     * 查看明细SQL
     */
    private String detailSql;
    
    /**
     * 稽核维度(准确性/一致性/时效性/合理性/完整性)
     */
    private String dimension;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建人
     */
    private String createUser;
    
    /**
     * 更新人
     */
    private String updateUser;
    
    /**
     * 可见范围 1：私有 2：公开
     */
    private Integer scope;
    
    /**
     * 状态 0：无效 1：有效
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
