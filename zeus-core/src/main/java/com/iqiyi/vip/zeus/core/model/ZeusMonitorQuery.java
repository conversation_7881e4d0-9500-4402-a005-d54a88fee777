package com.iqiyi.vip.zeus.core.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: guojing
 * @date: 2023/12/2 17:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "监控查询配置")
public class ZeusMonitorQuery {

    /**
     * 查询条件id
     */
    @ApiModelProperty(value = "查询条件id")
    @NotNull(message = "查询条件id不能为空")
    private Integer id;
    /**
     * 指标模版id
     */
    @ApiModelProperty(value = "指标模版id")
    @NotNull(message = "指标模版id不能为空")
    private Integer metricTmpId;
    /**
     * 查询来源
     * type为prometheus时，取值为metric
     * type为mysql时，取值为表名
     */
    @ApiModelProperty(value = "监控查询来源")
    @NotBlank(message = "监控查询来源不能为空")
    private String source;
    /**
     * 查询条件
     */
    @ApiModelProperty(value = "查询条件")
    @NotEmpty(message = "监控查询条件不能为空")
    private List<ZeusMonitorQueryCondition> conditions;
    /**
     * 时间范围字段, 数据源为MySQL时，不能为空
     */
    @ApiModelProperty(value = "时间范围字段")
    private String timeFilter;
    /**
     * 展示维度
     */
    @ApiModelProperty(value = "展示维度，分组字段")
    private String groupBy;
    /**
     * 展示说明, 数据源为Prometheus时，不能为空
     */
    @ApiModelProperty(value = "展示说明, 数据源为Prometheus时，不能为空")
    private String displayName;
    /**
     * 是否需要时间范围字段
     */
    @JsonIgnore
    @ApiModelProperty(value = "是否需要时间范围字段")
    private boolean needTimeFilter;
    /**
     * 是否支持分组
     */
    @JsonIgnore
    @ApiModelProperty(value = "是否支持分组")
    private boolean needGroupBy;
    /**
     * 是否需要展示说明
     */
    @JsonIgnore
    @ApiModelProperty(value = "是否需要展示说明")
    private boolean needDisplayName;
    /**
     * 智能告警指标名，智能告警Grafana页面展示
     */
    @ApiModelProperty(value = "智能告警指标名，智能告警Grafana页面展示")
    private String smartAlertMetricName;

}
