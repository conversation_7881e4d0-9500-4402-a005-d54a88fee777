package com.iqiyi.vip.zeus.core.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: guojing
 * @date: 2023/12/12 20:02
 */
@Data
@ApiModel("Dashboard搜索参数模型")
public class DashboardSearchParam {

    /**
     * 团队code
     */
    @ApiModelProperty(value = "团队code")
    private String teamCode;

    /**
     * dashboard名称
     */
    @ApiModelProperty(value = "dashboard名称")
    private String title;

}
