package com.iqiyi.vip.zeus.core.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;

import com.iqiyi.vip.zeus.core.constants.GrafanaSQLConstants;
import com.iqiyi.vip.zeus.core.utils.SQLParseUtils;

/**
 * 指标模版类型
 * @author: guojing
 * @date: 2024/1/24 21:05
 */

public enum MetricTemplateType {

    PROMETHEUS_TIME_SERIES(1, "timeseries", GrafanaSQLConstants.PANEL_FORMAT_TIME_SERIES),
    MYSQL_TIME_SERIES(2, "timeseries", GrafanaSQLConstants.PANEL_FORMAT_TIME_SERIES) {
        @Override
        public String panelJsonTmpResourcePath() {
            return "tmp/panel-mysql-time-series.json";
        }
    },
    MYSQL_STAT(3, "stat", GrafanaSQLConstants.PANEL_FORMAT_TABLE) {
        @Override
        public String panelJsonTmpResourcePath() {
            return "tmp/panel-mysql-table-stat.json";
        }
    },
    MYSQL_TABLE(4, "table", GrafanaSQLConstants.PANEL_FORMAT_TABLE) {
        @Override
        public String panelJsonTmpResourcePath() {
            return "tmp/panel-mysql-table.json";
        }
    },
    ;

    private Integer value;
    /**
     * 面板类型
     */
    private String panelType;
    /**
     * 面板查询类型
     */
    private String panelTargetType;


    MetricTemplateType(Integer value, String panelType, String panelTargetType) {
        this.value = value;
        this.panelType = panelType;
        this.panelTargetType = panelTargetType;
    }

    public Integer getValue() {
        return value;
    }

    public String getPanelType() {
        return panelType;
    }

    public String getPanelTargetType() {
        return panelTargetType;
    }

    /**
     * 不支持配置告警规则
     */
    public boolean nonsupportAlertRule() {
        return !GrafanaSQLConstants.PANEL_FORMAT_TIME_SERIES.equals(getPanelTargetType());
    }

    private static final HashMap<Integer, MetricTemplateType> map = new HashMap<>();

    static {
        for (MetricTemplateType enumType : MetricTemplateType.values()) {
            map.put(enumType.getValue(), enumType);
        }
    }

    public static MetricTemplateType parseValue(Integer value) {
        return map.getOrDefault(value, null);
    }

    public static MetricTemplateType parseFromContext(String datasourceType, String tmpContext) {
        if (StringUtils.isBlank(tmpContext)) {
            return null;
        }
        if (DataSourceType.Prometheus.getValue().equals(datasourceType)) {
            return MetricTemplateType.PROMETHEUS_TIME_SERIES;
        }
        if (DataSourceType.MySQL.getValue().equals(datasourceType)) {
            if (StringUtils.contains(tmpContext, GrafanaSQLConstants.MYSQL_MACRO_TIME_GROUP)) {
                return MetricTemplateType.MYSQL_TIME_SERIES;
            }
            String selectContent = SQLParseUtils.parseSelectSQL(tmpContext);
            if (StringUtils.isBlank(selectContent)) {
                return null;
            }
            if (selectContent.contains(GrafanaSQLConstants.COMMA)) {
                return MetricTemplateType.MYSQL_TABLE;
            } else {
                return MetricTemplateType.MYSQL_STAT;
            }
        }
        return null;
    }

    /**
     * 获取Panel模板的资源路径
     */
    public String panelJsonTmpResourcePath() {
        return "tmp/panel-prometheus.json";
    }

}
