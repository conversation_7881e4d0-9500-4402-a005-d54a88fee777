package com.iqiyi.vip.zeus.core.component;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.GuardDataQueryType;

/**
 * @author: guojing
 * @date: 2024/2/2 13:53
 */
@Profile("!sg")
@Slf4j
@Lazy(value = false)
@Component
public class GuardItemDataQueryFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<GuardDataQueryType, GuardItemDataQuery> dataQueryFactoryMap = Maps.newEnumMap(GuardDataQueryType.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("GuardItemDataQueryFactory-init start!");
        applicationContext.getBeansOfType(GuardItemDataQuery.class).values()
            .forEach(query -> {
                log.info("GuardItemDataQueryFactory-init:register handler {}  for {}",
                    query.getClass().getSimpleName(), query.getGuardDataQueryType());
                dataQueryFactoryMap.put(query.getGuardDataQueryType(), query);
            });
        log.info("GuardItemDataQueryFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public GuardItemDataQuery getQueryFactory(GuardDataQueryType queryType) {
        return dataQueryFactoryMap.get(queryType);
    }

}
