package com.iqiyi.vip.zeus.core.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.component.OrderGuardDataQuery;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.mapper.pilot.PilotComponent;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.core.po.PilotQueryResult;
import com.iqiyi.vip.zeus.core.utils.NumberConvertUtils;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

/**
 * @author: guojing
 * @date: 2025/7/22 17:22
 */
@Slf4j
@Component
public class PilotHiveService implements OrderGuardDataQuery {

    @Resource
    private PilotComponent pilotComponent;

    @Override
    public DataSourceType getDatasourceType() {
        return DataSourceType.Hive;
    }

    @Override
    public Map<String, OrderGuardianQueryData> queryData(String querySql, String startDay, String endDay) {
        Map<String, String> values = new HashMap<>();
        values.put("dt", CommonDateUtils.yesterdayStr());
        values.put("startTime", startDay + " 00:00:00");
        values.put("endTime", endDay + " 23:59:59");
        String finalSql = new StringSubstitutor(values).replace(querySql);
        PilotQueryResult queryResult = pilotComponent.queryNew(getDatasourceType(), finalSql);
        if (queryResult == null || CollectionUtils.isEmpty(queryResult.getRowDataList())) {
            return Collections.emptyMap();
        }

        Map<String, OrderGuardianQueryData> dataMap = new HashMap<>();
        for (List<Object> oneRowData : queryResult.getRowDataList()) {
            if (CollectionUtils.isEmpty(oneRowData) || oneRowData.size() < 2) {
                continue;
            }

            String day = (String) oneRowData.get(0);
            Object countObj = oneRowData.get(1);
            Double count = NumberConvertUtils.convertToDouble(countObj);
            OrderGuardianQueryData dayData = new OrderGuardianQueryData(day, count);
            if (oneRowData.size() >= 3) {
                Object secondCount = oneRowData.get(2);
                if (secondCount == null) {
                    continue;
                }
                if (secondCount instanceof String) {
                    dayData.setSecondCount((String) secondCount);
                } else {
                    dayData.setSecondCount(String.valueOf(secondCount));
                }
            }
            dataMap.put(day, dayData);
        }

        return dataMap;
    }

    @Override
    public OrderGuardianDetailData queryDetailData(String detailSql, String queryDay) {
        if (StringUtils.isBlank(detailSql) || Objects.equals(queryDay, CommonDateUtils.todayStr())) {
            return null;
        }
        Map<String, String> values = new HashMap<>();
        values.put("dt", CommonDateUtils.yesterdayStr());
        values.put("startTime", queryDay + " 00:00:00");
        values.put("endTime", queryDay + " 23:59:59");
        String finalSql = new StringSubstitutor(values).replace(detailSql);
        List<LinkedHashMap<String, Object>> queryResult = pilotComponent.queryDetail(getDatasourceType(), finalSql);
        return new OrderGuardianDetailData(finalSql, queryResult);
    }
}
