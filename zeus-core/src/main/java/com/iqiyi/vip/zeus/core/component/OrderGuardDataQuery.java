package com.iqiyi.vip.zeus.core.component;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianQueryData;

/**
 * @author: guojing
 * @date: 2025/5/26 18:39
 */
public interface OrderGuardDataQuery {

    DataSourceType getDatasourceType();

    /**
     *
     * @param querySql
     * @param startDay inclusive
     * @param endDay inclusive
     */
    Map<String, OrderGuardianQueryData> queryData(String querySql, String startDay, String endDay);

    OrderGuardianDetailData queryDetailData(String detailSql, String queryDay);

}
