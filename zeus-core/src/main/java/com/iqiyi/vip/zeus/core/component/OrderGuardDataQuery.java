package com.iqiyi.vip.zeus.core.component;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;

/**
 * @author: guojing
 * @date: 2025/5/26 18:39
 */
public interface OrderGuardDataQuery {

    GuardDatasourceType getDatasourceType();

    /**
     * 查询数据
     * @param querySql
     * @param startDay inclusive
     * @param endDay inclusive
     */
    Map<String, OrderGuardianQueryData> queryData(String querySql, String startDay, String endDay);

    /**
     * 查询明细数据
     * @param detailSql
     * @param queryDay
     */
    OrderGuardianDetailData queryDetailData(String detailSql, String queryDay);

}
