package com.iqiyi.vip.zeus.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderGuardMonitorPO {

    private Integer id;

    private String name;

    private String category;

    private String datasourceType;

    private String querySql;

    private String detailSql;

    private String createOpr;

    private String updateOpr;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

}