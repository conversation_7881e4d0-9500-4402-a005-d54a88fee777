package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


/**
 * @author: guojing
 * @date: 2023/12/20 20:27
 */
@Data
@ApiModel("告警规则条件模型")
public class ZeusAlertRuleCondition {

    /**
     * 监控查询条件id
     */
    @ApiModelProperty(value = "监控查询条件id")
    @NotNull(message = "监控查询条件id不能为空")
    private Integer monitorQueryId;
    /**
     * 操作符
     * @see com.iqiyi.vip.zeus.core.enums.AlertRuleOperator
     */
    @ApiModelProperty(value = "操作符")
    @NotNull(message = "操作符不能为空")
    private String operator;
    /**
     * 告警阈值
     */
    @ApiModelProperty(value = "左侧告警阈值")
    @NotEmpty(message = "左侧告警阈值不能为空")
    private Float leftThreshold;

    /**
     * 告警阈值
     */
    @ApiModelProperty(value = "右侧告警阈值")
    private Float rightThreshold;

}
