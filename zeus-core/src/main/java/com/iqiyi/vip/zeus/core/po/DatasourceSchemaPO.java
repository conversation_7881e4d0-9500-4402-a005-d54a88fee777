package com.iqiyi.vip.zeus.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasourceSchemaPO {

    private Long id;

    private Integer datasourceId;

    private String tableTeamCode;

    private String table;

    private String column;

    private String columnType;

    private Timestamp createTime;

    private Timestamp updateTime;

    public DatasourceSchemaPO(Integer datasourceId, String tableTeamCode, String table, String column, String columnType) {
        this.datasourceId = datasourceId;
        this.tableTeamCode = tableTeamCode;
        this.table = table;
        this.column = column;
        this.columnType = columnType;
    }

}