package com.iqiyi.vip.zeus.core.po.guard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 稽核项-业务关联实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardBusinessPO {
    
    /**
     * id
     */
    private Integer id;
    
    /**
     * 稽核项id
     */
    private Integer guardItemId;
    
    /**
     * 业务id
     */
    private Integer businessId;
    
    /**
     * 状态 0：无效 1：有效
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
