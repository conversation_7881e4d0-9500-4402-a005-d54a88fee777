package com.iqiyi.vip.zeus.core.model;

import lombok.Data;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/11/25 15:26
 */
@Data
public class MySQLDatasourceExtraField {

    /**
     * 数据库
     */
    private String database;
    /**
     * 用户名
     */
    private String username;
    /**
     * 数据库密码
     */
    private String password;

    public Map<String, Object> getExtraData() {
        Map<String, Object> extraData = new LinkedHashMap<>();
        extraData.put("database", database);
        extraData.put("username", username);
        return extraData;
    }

    public Map<String, Object> getSensitiveExtraData() {
        Map<String, Object> extraData = new LinkedHashMap<>();
        extraData.put("password", password);
        return extraData;
    }

    public static List<FieldConfigMeta> getExtraFieldMeta() {
        FieldConfigMeta databaseField = FieldConfigMeta.newStringField("数据库", "database", "请输入数据库名称", true);
        FieldConfigMeta usernameField = FieldConfigMeta.newStringField("用户名", "username", "请输入用户名", true);
        FieldConfigMeta passwordField = FieldConfigMeta.newStringSensitiveField("密码", "password", "请输入密码", true);
        return Arrays.asList(databaseField, usernameField, passwordField);
    }

}
