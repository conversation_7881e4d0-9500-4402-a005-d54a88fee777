package com.iqiyi.vip.zeus.core.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

/**
 * @author: guojing
 * @date: 2023/12/4 21:13
 */
@Slf4j
@Configuration
@Profile("!sg")
@MapperScan(basePackages = "com.iqiyi.vip.zeus.core.mapper", sqlSessionFactoryRef = "zeusSqlSessionFactory")
public class ZeusDBConfig extends AbstractDBConfig {

    protected final String beanPrefix = "zeus";

    @Resource
    private ZeusResourceConfig zeusResourceConfig;

    @Bean(name = beanPrefix + "DataSource")
    protected DataSource dataSource() {
        return super.dataSource(zeusResourceConfig);
    }

    @Bean(name = beanPrefix + "SqlSessionFactory")
    protected SqlSessionFactory sqlSessionFactory(@Qualifier(beanPrefix + "DataSource") DataSource dataSource) throws Exception {
        log.info("数据源启动完成:" + dataSource.getConnection());
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
        SqlSessionFactory sqlSessionFactory = sqlSessionFactoryBean.getObject();
        sqlSessionFactory.getConfiguration().getTypeHandlerRegistry().register("com.iqiyi.vip.zeus.core.mybatis.typehandler");
        sqlSessionFactory.getConfiguration().setMapUnderscoreToCamelCase(true);
        return sqlSessionFactory;
    }

    @Bean(name = beanPrefix + "SqlSessionTemplate")
    protected SqlSessionTemplate sqlSessionTemplate(@Qualifier(beanPrefix + "SqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    @Bean(name = beanPrefix + "TransactionManager")
    protected PlatformTransactionManager transactionManager(@Qualifier(beanPrefix + "DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
