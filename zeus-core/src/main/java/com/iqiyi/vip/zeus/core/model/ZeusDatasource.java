package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import javax.validation.constraints.NotBlank;
import java.sql.Timestamp;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.constants.DatasourceConstants;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.po.DatasourcePO;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDatasourceRequest;

/**
 * @author: guojing
 * @date: 2023/11/24 15:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("宙斯数据源模型")
public class ZeusDatasource {

    /**
     * 宙斯数据源ID
     */
    @ApiModelProperty(value = "数据源id")
    private Integer id;
    /**
     * 宙斯数据源名称
     * 具有唯一性
     */
    @ApiModelProperty(value = "数据源名称", required = true)
    @NotBlank(message = "数据源名称不能为空")
    private String name;
    /**
     * 宙斯数据源描述
     */
    @ApiModelProperty(value = "数据源描述")
    private String description;
    /**
     * 宙斯数据源类型
     * @see DataSourceType
     */
    @ApiModelProperty(value = "数据源类型", required = true)
    @NotBlank(message = "数据源类型不能为空")
    private String type;
    /**
     * 所属团队
     */
    @ApiModelProperty(value = "所属团队")
    @NotBlank(message = "团队不能为空")
    private String teamCode;
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    private String teamName;
    /**
     * 数据源url
     */
    @ApiModelProperty(value = "数据源url", required = true)
    @NotBlank(message = "数据源url不能为空")
    private String url;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createUser;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateUser;
    /**
     * 扩展字段配置
     */
    @ApiModelProperty(value = "数据源扩展信息")
    private Map<String, Object> extraData;
    /**
     * 敏感字段配置
     */
    @ApiModelProperty(value = "数据源敏感字段信息")
    private Map<String, Object> sensitiveExtraData;

    @ApiModelProperty(value = "是否有效，1：是；0：否")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;
    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    public boolean invalid() {
        return status != null && status == 0;
    }

    public ZeusDatasource addExtraItem(String key, Object value) {
        if (extraData == null) {
            extraData = new LinkedHashMap<>();
        }
        extraData.put(key, value);
        return this;
    }

    public Object getExtraItem(String key) {
        return MapUtils.getObject(extraData, key);
    }

    public ZeusDatasource setEagleDatasourceUid(String eagleDatasourceUid) {
        addExtraItem(DatasourceConstants.EXTRA_FIELD_EAGLE_DATASOURCE_UID, eagleDatasourceUid);
        return this;
    }

    public String getEagleDatasourceUid() {
        return MapUtils.getString(extraData, DatasourceConstants.EXTRA_FIELD_EAGLE_DATASOURCE_UID);
    }

    public String getUserItem() {
        return MapUtils.getString(extraData, DatasourceConstants.EXTRA_FIELD_USER);
    }

    public String getDatabaseItem() {
        return MapUtils.getString(extraData, DatasourceConstants.EXTRA_FIELD_DATABASE);
    }

    public String getPasswordItem() {
        return MapUtils.getString(sensitiveExtraData, DatasourceConstants.EXTRA_FIELD_PASSWORD);
    }

    public boolean prometheusType() {
        return DataSourceType.Prometheus.getValue().equals(type);
    }

    public DatasourcePO toDatasourcePO() {
        return DatasourcePO.builder()
            .id(id)
            .name(name)
            .description(description)
            .type(type)
            .teamCode(teamCode)
            .url(url)
            .createUser(createUser)
            .updateUser(updateUser)
            .extraData(JacksonUtils.toJsonString(extraData))
            .sensitiveExtraData(JacksonUtils.toJsonString(sensitiveExtraData))
            .createTime(createTime)
            .updateTime(updateTime)
            .build();
    }

    public static ZeusDatasource buildFrom(DatasourcePO datasourcePO) {
        if (datasourcePO == null) {
            return null;
        }
        return ZeusDatasource.builder()
            .id(datasourcePO.getId())
            .name(datasourcePO.getName())
            .description(datasourcePO.getDescription())
            .type(datasourcePO.getType())
            .teamCode(datasourcePO.getTeamCode())
            .url(datasourcePO.getUrl())
            .createUser(datasourcePO.getCreateUser())
            .updateUser(datasourcePO.getUpdateUser())
            .extraData(JacksonUtils.parseMap(datasourcePO.getExtraData()))
            .sensitiveExtraData(JacksonUtils.parseMap(datasourcePO.getSensitiveExtraData()))
            .status(datasourcePO.getStatus())
            .createTime(datasourcePO.getCreateTime())
            .updateTime(datasourcePO.getUpdateTime())
            .build();
    }

    public CreateDatasourceRequest toEagleDSRequest() {
        DataSourceType dataSourceType = DataSourceType.parseValue(this.type);
        List<FieldConfigMeta> dsFieldConfigMeta = dataSourceType.getDSFieldConfigMeta();
        Map<String, Object> jsonData = new LinkedHashMap<>();
        Map<String, Object> secureJsonData = new LinkedHashMap<>();
        dsFieldConfigMeta.forEach(fieldConfigMeta -> {
            String metaKey = fieldConfigMeta.getKey();
            if (BooleanUtils.isTrue(fieldConfigMeta.getSensitiveField())) {
                secureJsonData.put(metaKey, MapUtils.getObject(this.sensitiveExtraData, metaKey));
            } else {
                jsonData.put(metaKey, MapUtils.getObject(this.extraData, metaKey, fieldConfigMeta.getDefaultValue()));
            }
        });
        return CreateDatasourceRequest.builder()
            .uid(this.getEagleDatasourceUid())
            .name(this.name)
            .type(dataSourceType.getGrafanaDatasourceType())
            .access("proxy")
            .url(this.url)
            .database(MapUtils.getString(this.extraData, DatasourceConstants.EXTRA_FIELD_DATABASE))
            .user(MapUtils.getString(this.extraData, DatasourceConstants.EXTRA_FIELD_USER))
            .jsonData(jsonData)
            .secureJsonData(secureJsonData)
            .build();
    }

}
