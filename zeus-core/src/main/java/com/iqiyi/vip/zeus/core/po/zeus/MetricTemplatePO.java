package com.iqiyi.vip.zeus.core.po.zeus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MetricTemplatePO {

    private Integer id;

    private String name;

    private String datasourceType;

    private Integer type;
    /**
     * Prometheus指标类型
     */
    private String metricType;

    private String content;

    private String description;

    private String createUser;

    private String updateUser;

    private Integer status;

    private Timestamp createTime;

    private Timestamp updateTime;

}