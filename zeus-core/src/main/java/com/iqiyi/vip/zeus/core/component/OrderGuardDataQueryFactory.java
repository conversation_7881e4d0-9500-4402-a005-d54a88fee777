package com.iqiyi.vip.zeus.core.component;

import com.google.common.collect.Maps;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: guojing
 * @date: 2024/2/2 13:53
 */
@Profile("!sg")
@Slf4j
@Lazy(value = false)
@Component
public class OrderGuardDataQueryFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<DataSourceType, OrderGuardDataQuery> dataQueryFactoryMap = Maps.newEnumMap(DataSourceType.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("OrderGuardDataQueryFactory-init start!");
        applicationContext.getBeansOfType(OrderGuardDataQuery.class).values()
            .forEach(query -> {
                log.info("OrderGuardDataQueryFactory-init:register handler {}  for {}",
                    query.getClass().getSimpleName(), query.getDatasourceType());
                dataQueryFactoryMap.put(query.getDatasourceType(), query);
            });
        log.info("OrderGuardDataQueryFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public OrderGuardDataQuery getQueryFactory(DataSourceType dataSourceType) {
        return dataQueryFactoryMap.get(dataSourceType);
    }

}
