package com.iqiyi.vip.zeus.core.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.req.guard.GuardDatasourceSearchParam;

/**
 * 数据源搜索响应
 * 
 * <AUTHOR>
 * @date 2025-09-08 10:00:00
 */
@ApiModel("数据源搜索响应")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardDatasourceSearchResp {

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数", example = "100")
    private Integer totalCount;

    /**
     * 总页数
     */
    @ApiModelProperty(value = "总页数", example = "10")
    private Integer totalPage;

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer pageNo;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize;

    /**
     * 数据源列表
     */
    @ApiModelProperty(value = "数据源列表")
    private List<GuardDatasource> datasourceList;

    public static GuardDatasourceSearchResp buildEmptyResp(Integer pageNo, Integer pageSize) {
        return GuardDatasourceSearchResp.builder()
            .pageNo(pageNo == null ? GuardDatasourceSearchParam.DEFAULT_PAGE_NO : pageNo)
            .pageSize(pageSize == null ? GuardDatasourceSearchParam.DEFAULT_PAGE_SIZE : pageSize)
            .totalCount(0)
            .totalPage(0)
            .datasourceList(Collections.emptyList())
            .build();
    }

}
