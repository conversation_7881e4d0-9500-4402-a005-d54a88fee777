package com.iqiyi.vip.zeus.core.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: guojing
 * @date: 2023/12/13 17:47
 */
@Data
@ApiModel("PrometheusMetricLabelValueQueryParam参数模型")
public class PrometheusMetricLabelValueQueryParam {

    /**
     * 数据源id
     */
    @ApiModelProperty(value = "数据源id")
    @NotNull(message = "数据源id不能为空")
    private Integer datasourceId;
    /**
     * metric名称
     */
    @ApiModelProperty(value = "metric名称")
    @NotBlank(message = "metric不能为空")
    private String metric;
    /**
     * label名称
     */
    @ApiModelProperty(value = "label名称")
    @NotBlank(message = "label不能为空")
    private String label;

}
