package com.iqiyi.vip.zeus.core.po.zeus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRulePO {

    private Integer id;

    private Integer monitorId;

    private String name;

    private String teamCode;

    /**
     * 持续时间，单位秒
     */
    private Integer duration;

    private Integer checkFrequency;

    private String conditions;

    private String level;

    private String receivers;

    private String createUser;

    private String updateUser;

    private String eagleUid;

    private String smartAlertRule;

    private Integer status;

    private String extraData;

    private Timestamp createTime;

    private Timestamp updateTime;

}