package com.iqiyi.vip.zeus.core.mybatis.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * Created at: 2021-04-30
 *
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
public class CustomTypeHandler<T extends Object> extends BaseTypeHandler<T> {

    private Class<T> clazz;

    public CustomTypeHandler() {
    }

    public CustomTypeHandler(Class<T> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JacksonUtils.toJsonString(parameter));
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return JacksonUtils.parseObject(rs.getString(columnName), clazz);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return JacksonUtils.parseObject(rs.getNString(columnIndex), clazz);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return JacksonUtils.parseObject(cs.getNString(columnIndex), clazz);
    }

}
