package com.iqiyi.vip.zeus.core.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2024/4/11 20:46
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecordMonitorMapping {

    private String recordName;
    private List<MonitorQueryMapping> monitorQueryMappings;

}
