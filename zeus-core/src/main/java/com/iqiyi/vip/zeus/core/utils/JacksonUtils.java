package com.iqiyi.vip.zeus.core.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/11/14 15:05
 */
@Slf4j
public class JacksonUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
        try {
            return objectMapper.readValue(jsonString, javaType);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    /**
     * json str解析为map
     */
    public static Map<String, Object> parseMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {
            });
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static Map<String, String> parseStringMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.readValue(jsonString, new TypeReference<Map<String, String>>() {
            });
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> T loadFromJsonFile(String resourcePath, Class<T> clazz) {
        if (StringUtils.isBlank(resourcePath)) {
            return null;
        }
        try {
            ClassPathResource classPathResource = new ClassPathResource(resourcePath);
            return objectMapper.readValue(classPathResource.getInputStream(), clazz);
        } catch (IOException e) {
            log.error("loadFromJsonFile occurred exception, resourcePath: {}", resourcePath, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> String toJsonString(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.error("writeValueAsString occurred exception, objectValue: {}", object, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> byte[] toJsonBytes(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsBytes(object);
        } catch (IOException e) {
            log.error("writeValueAsBytes occurred exception, objectValue: {}", object, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> Map<String, Object> beanToMap(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(object, new TypeReference<Map<String, Object>>() {
        });
    }

    public static <T> Map<String, String> beanToStringMap(T object) {
        if (object == null) {
            return null;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(object, new TypeReference<Map<String, String>>() {
        });
    }

    public static boolean validJson(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return false;
        }
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            return jsonNode.isArray() || jsonNode.isObject();
        } catch (JsonProcessingException e) {
            return false;
        }
    }

}
