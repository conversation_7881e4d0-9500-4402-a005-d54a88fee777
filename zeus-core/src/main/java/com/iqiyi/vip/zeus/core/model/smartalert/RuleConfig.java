package com.iqiyi.vip.zeus.core.model.smartalert;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2024/11/21 14:26
 */
@Data
@NoArgsConstructor
public class RuleConfig {

    /**
     * 规则名称
     */
    private String name;
    /**
     * 项目
     */
    private String project;
    /**
     * 训练使用的历史数据时长
     */
    private String refer_duration;
    /**
     * 持续异常触发报警时间
     */
    private String keep_firing_for;
    /**
     * 时间前置，用于回放测试
     */
    private String time_offset;
    /**
     * 指标延时查询时间
     */
    private String time_shift;
    /**
     * 执行周期
     */
    private String interval;
    /**
     * 基线检测窗口，应于解决峰/谷值偏差问题
     */
    private Integer baseline_judge_wnd;
    /**
     * Hubble 告警分组
     */
    private String hubble_group;
    /**
     * Hubble告警接收人
     */
    private List<String> hubble_users;
    /**
     * 告警配置
     */
    private List<NoticeConfig> notice_configs;
    /**
     * 数据源配置
     */
    private List<TsdbConfig> tsdb_configs;
    /**
     * 指标配置
     */
    private List<MetricConfig> metric_configs;

}
