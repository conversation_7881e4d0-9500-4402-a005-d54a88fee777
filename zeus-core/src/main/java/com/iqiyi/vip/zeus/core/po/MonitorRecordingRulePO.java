package com.iqiyi.vip.zeus.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorRecordingRulePO {

    private Integer id;

    /**
     * 监控id
     */
    private Integer monitorId;
    /**
     * 监控下的查询id
     */
    private Integer queryId;
    /**
     * Record名
     */
    private String recordName;

    public MonitorRecordingRulePO(Integer monitorId, String recordName) {
        this.monitorId = monitorId;
        this.recordName = recordName;
    }

    public MonitorRecordingRulePO(Integer monitorId, Integer queryId, String recordName) {
        this.monitorId = monitorId;
        this.queryId = queryId;
        this.recordName = recordName;
    }
}