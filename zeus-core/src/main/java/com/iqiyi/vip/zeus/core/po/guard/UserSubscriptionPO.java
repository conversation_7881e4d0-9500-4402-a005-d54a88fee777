package com.iqiyi.vip.zeus.core.po.guard;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 稽核项订阅实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UserSubscriptionPO {
    
    /**
     * id
     */
    private Integer id;
    
    /**
     * 员工邮箱前缀
     */
    private String oaName;
    
    /**
     * 稽核项ID
     */
    private Integer guardItemId;
    
    /**
     * 状态 0：无效 1：有效
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
