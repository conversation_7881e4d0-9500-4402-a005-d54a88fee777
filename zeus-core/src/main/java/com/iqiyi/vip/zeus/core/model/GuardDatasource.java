package com.iqiyi.vip.zeus.core.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.po.guard.GuardDatasourcePO;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * 数据源模型
 * 
 * <AUTHOR>
 * @date 2025-01-08 19:30:00
 */
@ApiModel(description = "业务稽核数据源模型")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuardDatasource {
    
    /**
     * 自增主键id
     */
    @ApiModelProperty(value = "自增主键id", example = "1")
    private Integer id;
    
    /**
     * 数据源名称
     */
    @ApiModelProperty(value = "数据源名称", required = true, example = "MySQL测试库")
    @NotBlank(message = "数据源名称不能为空")
    private String name;
    
    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型", required = true, example = "MYSQL")
    @NotBlank(message = "数据源类型不能为空")
    private String type;
    
    /**
     * 数据源连接url
     */
    @ApiModelProperty(value = "数据源连接url", required = true, example = "********************************")
    @NotBlank(message = "数据源连接url不能为空")
    private String connUrl;
    
    /**
     * 连接配置(JSON格式)
     */
    @ApiModelProperty(value = "连接配置(JSON格式)", example = "{\"username\":\"root\",\"password\":\"123456\"}")
    private Map<String, Object> connConfig;
    
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", example = "测试环境MySQL数据库")
    private String description;
    
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", example = "zhangsan")
    private String createUser;
    
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", example = "lisi")
    private String updateUser;
    
    /**
     * 状态,0:无效;1:有效
     */
    @ApiModelProperty(value = "状态,0:无效;1:有效", example = "1", allowableValues = "0,1")
    private Integer status;
    
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 10:00:00")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", example = "2024-01-01 10:00:00")
    private Date updateTime;

    /**
     * 是否为Prometheus数据源
     */
    public boolean prometheusType() {
        return GuardDatasourceType.Prometheus.getValue().endsWith(this.type);
    }
    
    /**
     * 转换为GuardDatasourcePO对象
     * 
     * @return GuardDatasourcePO对象
     */
    public GuardDatasourcePO toPO() {
        return GuardDatasourcePO.builder()
                .id(this.id)
                .name(this.name)
                .type(this.type)
                .connUrl(this.connUrl)
                .connConfig(JacksonUtils.toJsonString(this.connConfig))
                .description(this.description)
                .createUser(this.createUser)
                .updateUser(this.updateUser)
                .status(this.status)
                .createTime(this.createTime)
                .updateTime(this.updateTime)
                .build();
    }
    
    /**
     * 从GuardDatasourcePO对象创建GuardDatasource
     * 
     * @param po GuardDatasourcePO对象
     * @return GuardDatasource对象
     */
    public static GuardDatasource fromPO(GuardDatasourcePO po) {
        if (po == null) {
            return null;
        }
        
        return GuardDatasource.builder()
                .id(po.getId())
                .name(po.getName())
                .type(po.getType())
                .connUrl(po.getConnUrl())
                .connConfig(JacksonUtils.parseMap(po.getConnConfig()))
                .description(po.getDescription())
                .createUser(po.getCreateUser())
                .updateUser(po.getUpdateUser())
                .status(po.getStatus())
                .createTime(po.getCreateTime())
                .updateTime(po.getUpdateTime())
                .build();
    }
}
