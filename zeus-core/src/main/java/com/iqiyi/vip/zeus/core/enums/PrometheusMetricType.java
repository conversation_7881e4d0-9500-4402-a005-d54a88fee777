package com.iqiyi.vip.zeus.core.enums;

/**
 * @author: guojing
 * @date: 2025/3/11 16:47
 */
public enum PrometheusMetricType {

    Counter("Counter", "计数器", "request_count"),
    Gauge("Gauge", "仪表盘", "request_rate"),
    ;

    private String value;
    private String desc;
    private String smartAlertMetricType;

    PrometheusMetricType(String value, String desc, String smartAlertMetricType) {
        this.value = value;
        this.desc = desc;
        this.smartAlertMetricType = smartAlertMetricType;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getSmartAlertMetricType() {
        return smartAlertMetricType;
    }

    public static PrometheusMetricType findByValue(String value) {
        for (PrometheusMetricType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

}
