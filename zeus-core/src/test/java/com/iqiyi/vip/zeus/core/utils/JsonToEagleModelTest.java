package com.iqiyi.vip.zeus.core.utils;

import org.junit.Assert;
import org.junit.Test;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;

/**
 * @author: guojing
 * @date: 2023/12/28 16:31
 */
public class JsonToEagleModelTest {

    @Test
    public void test() {
        Dashboard dashboard = JacksonUtils.loadFromJsonFile("tmp/dashboard.json", Dashboard.class);
        Assert.assertNotNull(dashboard);
    }
}
