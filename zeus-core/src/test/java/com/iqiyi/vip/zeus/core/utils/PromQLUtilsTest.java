package com.iqiyi.vip.zeus.core.utils;

import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MetricTemplateType;
import com.iqiyi.vip.zeus.core.enums.ValueType;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQueryCondition;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2023/12/22 12:14
 */
public class PromQLUtilsTest {

    @Test
    public void genPromQL() {
        MetricTemplate currentCountMetricTemplate = MetricTemplate.builder()
            .id(1)
            .name("当前数值")
            .datasourceType(DataSourceType.Prometheus.getValue())
            .type(MetricTemplateType.PROMETHEUS_TIME_SERIES.getValue())
            .content("sum(rate(${metric}{${labels}}[1m])))")
            .description("当前数值")
            .build();

        MetricTemplate groupByCountMetricTemplate = MetricTemplate.builder()
            .id(2)
            .name("当前数值-分组")
            .datasourceType(DataSourceType.Prometheus.getValue())
            .type(MetricTemplateType.PROMETHEUS_TIME_SERIES.getValue())
            .content("sum(rate(${metric}{${labels}}[1m])) by (${groupBy})")
            .needGroupBy(true)
            .description("当前数值-分组")
            .build();

        ZeusMonitorQueryCondition applicationCondition = new ZeusMonitorQueryCondition("application", "=", Arrays.asList("vip-xuanwu"));
        ZeusMonitorQueryCondition agreementTypeCondition = new ZeusMonitorQueryCondition("agreementType", "=", Arrays.asList("1"));
        ZeusMonitorQueryCondition operatorTypeCondition = new ZeusMonitorQueryCondition("operatorType", "=", Arrays.asList("1"));
        ZeusMonitorQuery currentCountQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(1)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量")
            .build();
        ZeusMonitorQuery groupByCountQuery = ZeusMonitorQuery.builder()
            .id(2)
            .metricTmpId(2)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .groupBy("vipType")
            .displayName("会员类型")
            .build();
        String currentCountPromQL = PromQLUtils.genPromQL(currentCountMetricTemplate, currentCountQuery);
        String groupByCountPromQL = PromQLUtils.genPromQL(groupByCountMetricTemplate, groupByCountQuery);
        System.out.println(currentCountPromQL);
        System.out.println(groupByCountPromQL);
    }

    @Test
    public void genMySQL() {
        MetricTemplate statMetricTemplate = MetricTemplate.builder()
            .id(1)
            .name("总数")
            .datasourceType(DataSourceType.MySQL.getValue())
            .type(MetricTemplateType.MYSQL_STAT.getValue())
            .content("SELECT count(1) as value\n"
                + "FROM ${table}\n"
                + "WHERE $__timeFilter(${timeColumn}) AND (${conditions})")
            .description("计算总数")
            .needTimeFilter(true)
            .build();
        MetricTemplate timeSeriesMetricTemplate = MetricTemplate.builder()
            .id(2)
            .name("数量趋势-分组维度")
            .datasourceType(DataSourceType.MySQL.getValue())
            .type(MetricTemplateType.MYSQL_TIME_SERIES.getValue())
            .content("SELECT $__timeGroupAlias(${timeColumn},'1s',0), \n"
                + "  ${groupBy} as metric, \n"
                + "  count(1) as value\n"
                + "FROM ${table}\n"
                + "WHERE $__timeFilter(${timeColumn}) AND (${conditions})\n"
                + "GROUP BY time,${groupBy}\n"
                + "ORDER BY time")
            .needTimeFilter(true)
            .needGroupBy(true)
            .description("数量趋势-分组维度")
            .build();

        ZeusMonitorQueryCondition statusCondition = new ZeusMonitorQueryCondition("status", "between_and", Arrays.asList("1", "2"), ValueType.STRING.getValue());
        ZeusMonitorQueryCondition refundSourceCondition = new ZeusMonitorQueryCondition("refund_source", "=", Arrays.asList("0"), ValueType.NUMBER.getValue());
        ZeusMonitorQueryCondition refundPersonCondition = new ZeusMonitorQueryCondition("refund_person", "=", Arrays.asList("appleiap"));
        ZeusMonitorQuery statMonitorQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(1)
            .source("boss_refund")
            .conditions(Arrays.asList(statusCondition, refundSourceCondition, refundPersonCondition))
            .timeFilter("update_time")
            .build();
        ZeusMonitorQuery timeSeriesMonitorQuery = ZeusMonitorQuery.builder()
            .id(2)
            .metricTmpId(2)
            .source("boss_refund")
            .conditions(Arrays.asList(statusCondition, refundSourceCondition))
            .timeFilter("update_time")
            .groupBy("refund_person")
            .build();

        String valuePromQL = PromQLUtils.genMySQL(statMetricTemplate, statMonitorQuery);
        String valueOffsetPromQL = PromQLUtils.genMySQL(timeSeriesMetricTemplate, timeSeriesMonitorQuery);
        System.out.println(valuePromQL);
        System.out.println(valueOffsetPromQL);
    }

}