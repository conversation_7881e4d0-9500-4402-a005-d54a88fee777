package com.iqiyi.vip.zeus.core.utils;

import org.junit.Test;

import com.iqiyi.vip.zeus.core.model.smartalert.RuleConfig;

/**
 * @author: guojing
 * @date: 2024/11/21 15:05
 */
public class YamlParseUtilsTest {

    @Test
    public void test() {
        String yamlStr = "# This config is for vip_auth\n"
            + "name: vip_auth\n"
            + "project: boss\n"
            + "core_metric: true\n"
            + "refer_duration: 15d\n"
            + "keep_firing_for: 3m\n"
            + "time_offset: 10m\n"
            + "time_shift: 2m\n"
            + "forecast_interval: 30m\n"
            + "notice_configs:\n"
            + "- name: 'feishu'\n"
            + "  users:\n"
            + "  - 'jisi'\n"
            + "  - 'wangkai02'\n"
            + "\n"
            + "tsdb_configs:\n"
            + "- name: boss\n"
            + "  url: http://mimir-api.qiyi.domain/prometheus\n"
            + "  headers:\n"
            + "      X-Scope-OrgID: boss\n"
            + "  type: prometheus\n"
            + "\n"
            + "metric_configs:\n"
            + "- name: http_request_rate\n"
            + "  db_source: boss\n"
            + "  expr: 'avg(vip_auth_count{uniq_key=\"h_b285462e\", httpUrl!=\"\"}) by (httpUrl)'\n"
            + "  metric_type: request_rate\n"
            + "\n"
            + "- name: http_request_latency\n"
            + "  db_source: boss\n"
            + "  expr: 'avg(vip_auth_cost_P95{uniq_key=\"h_b285462e\"}) by (httpUrl)'\n"
            + "  metric_type: latency";
        RuleConfig ruleConfig = YamlParseUtils.yamlToObject(yamlStr);
        System.out.println(ruleConfig);
        System.out.println("=========================");
        String result = YamlParseUtils.objectToYaml(ruleConfig);
        System.out.println(result);
    }

    @Test
    public void loadFromYamlFile() {
        RuleConfig ruleConfig = YamlParseUtils.loadFromYamlFile("tmp/smart-alert-rule-tmp.yml", RuleConfig.class);
        System.out.println(JacksonUtils.toJsonString(ruleConfig));
        String yaml = YamlParseUtils.objectToYaml(ruleConfig);
        System.out.println(yaml);
    }
}