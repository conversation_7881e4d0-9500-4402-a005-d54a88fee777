package com.iqiyi.vip.zeus.core.service;

import org.junit.Test;

import java.util.Arrays;
import java.util.List;

import com.iqiyi.vip.zeus.core.constants.DatasourceConstants;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.core.utils.MonitorToEagleTransfer;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelTarget;

/**
 * @author: guojing
 * @date: 2023/12/22 16:52
 */
public class MonitorToEagleTransferTest {

    @Test
    public void monitorQueryToPanelTarget() {
        ZeusDatasource prometheusDS = ZeusDatasource.builder()
            .type(DataSourceType.Prometheus.getValue())
            .name("Prometheus")
            .build();
        prometheusDS.setEagleDatasourceUid("123456");
        ZeusMonitorQuery valueQuery = ZeusMonitorQuery.builder()
            .id(1)
            .source("autorenew_renew_log_insert_total")
            .conditions(null)
            .displayName("芝麻GO代扣订单总量")
            .build();
        ZeusMonitorQuery valueOffsetQuery = ZeusMonitorQuery.builder()
            .id(2)
            .source("autorenew_set_log_insert_total")
            .conditions( null)
            .displayName("芝麻GO代扣订单总量-昨天")
            .build();
        List<ZeusMonitorQuery> prometheusQueries = Arrays.asList(valueQuery, valueOffsetQuery);
        List<PanelTarget> prometheusPanelTargets = MonitorToEagleTransfer.monitorQueryToPanelTarget(prometheusQueries, null, prometheusDS);
        System.out.println(JacksonUtils.toJsonString(prometheusPanelTargets));


        ZeusDatasource mysqlDS = ZeusDatasource.builder()
            .type(DataSourceType.MySQL.getValue())
            .name("MySQL-Refund")
            .build();
        mysqlDS.setEagleDatasourceUid("7654321");
        mysqlDS.addExtraItem(DatasourceConstants.EXTRA_FIELD_DATABASE, "viptrade_refund");
        ZeusMonitorQuery tableQuery = ZeusMonitorQuery.builder()
            .id(1)
            .source("boss_refund")
            .conditions(null)
            .build();
        List<ZeusMonitorQuery> mysqlQueries = Arrays.asList(tableQuery);
        List<PanelTarget> mysqlPanelTargets = MonitorToEagleTransfer.monitorQueryToPanelTarget(mysqlQueries, null, mysqlDS);
        System.out.println(JacksonUtils.toJsonString(mysqlPanelTargets));
    }
}