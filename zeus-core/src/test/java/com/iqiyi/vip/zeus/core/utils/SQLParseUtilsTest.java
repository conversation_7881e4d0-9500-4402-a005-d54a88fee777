package com.iqiyi.vip.zeus.core.utils;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2024/1/26 18:55
 */
public class SQLParseUtilsTest {

    @Test
    public void parseSelectSQL() {
        String sql = "SELECT $__timeGroupAlias({timeColumn},'1s',0), \n"
            + "  {groupBy} as metric, \n"
            + "  count(1) as value\n"
            + "FROM {table}\n"
            + "WHERE $__timeFilter({timeColumn}) AND ({conditions})\n"
            + "GROUP BY time,{groupBy}\n"
            + "ORDER BY time";
        System.out.println(SQLParseUtils.parseSelectSQL(sql));
    }
}