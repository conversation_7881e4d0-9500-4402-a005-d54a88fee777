package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.req.guard.GuardDatasourceSearchParam;
import com.iqiyi.vip.zeus.core.resp.GuardDatasourceSearchResp;
import com.iqiyi.vip.zeus.core.service.guard.GuardDatasourceService;
import com.iqiyi.vip.zeus.core.service.guard.GuardItemService;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.response.GuardDatasourceTypeResp;

/**
 * 数据源管理控制器
 * 提供数据源的REST API接口
 * 
 * <AUTHOR>
 * @date 2025-09-08 18:30:00
 */
@Profile("!sg")
@Slf4j
@Api(tags = "业务稽核数据源管理", description = "提供数据源的增删改查功能")
@Validated
@RestController
@RequestMapping("/guard/datasource")
public class GuardDatasourceController {

    @Resource
    private GuardDatasourceService guardDatasourceService;
    @Resource
    private GuardItemService guardItemService;

    @ApiOperation(value = "支持的数据源类型", notes = "获取支持的数据源类型")
    @GetMapping("/supportTypes")
    public BaseResponse<List<GuardDatasourceTypeResp>> supportTypes() {
        GuardDatasourceType[] supportTypes = GuardDatasourceType.values();
        List<GuardDatasourceTypeResp> responseList = Arrays.stream(supportTypes)
                .map(GuardDatasourceTypeResp::fromEnum)
                .collect(Collectors.toList());
        return BaseResponse.createSuccessList(responseList);
    }

    /**
     * 创建数据源
     * 
     * @param createParam 数据源信息
     * @return 创建结果
     */
    @ApiOperation(value = "创建数据源", notes = "创建新的数据源配置")
    @PostMapping(value = "/create")
    public BaseResponse<Integer> create(@Validated @RequestBody GuardDatasource createParam) {
        if (!createParam.prometheusType() && MapUtils.isEmpty(createParam.getConnConfig())) {
            return BaseResponse.createParamError("数据源连接配置不能为空");
        }
        createParam.setCreateUser("zhouguojing");
        createParam.setUpdateUser(createParam.getCreateUser());
        GuardDatasource existing = guardDatasourceService.getByName(createParam.getName());
        if (existing != null) {
            return BaseResponse.createParamError("数据源名称已存在: " + createParam.getName());
        }
        return BaseResponse.createSuccess(guardDatasourceService.create(createParam));
    }

    /**
     * 更新数据源
     * 
     * @param updateParam 数据源信息
     * @return 更新结果
     */
    @ApiOperation(value = "更新数据源", notes = "根据ID更新数据源配置")
    @PutMapping("/update")
    public BaseResponse<Boolean> update(@Validated @RequestBody GuardDatasource updateParam) {
        if (updateParam.getId() == null) {
            return BaseResponse.createParamError("数据源ID不能为空");
        }
        GuardDatasource existed = guardDatasourceService.getById(updateParam.getId());
        if (existed == null) {
            return BaseResponse.createParamError("数据源不存在，ID: " + updateParam.getId());
        }
        if (!updateParam.prometheusType() && MapUtils.isEmpty(updateParam.getConnConfig())) {
            return BaseResponse.createParamError("数据源连接配置不能为空");
        }
        if (Objects.equals(updateParam.getStatus(), 0)
                && guardItemService.countByDatasourceId(updateParam.getId()) > 0) {
            return BaseResponse.createParamError("数据源下有在用的稽核项，不能删除");
        }

        // 如果更新了名称，检查新名称是否已存在
        if (updateParam.getName() != null && !Objects.equals(updateParam.getName(), existed.getName())) {
            GuardDatasource nameExists = guardDatasourceService.getByName(updateParam.getName());
            if (nameExists != null && !Objects.equals(nameExists.getId(), updateParam.getId())) {
                return BaseResponse.createParamError("数据源名称已存在: " + updateParam.getName());
            }
        }
        return BaseResponse.createSuccess(guardDatasourceService.update(updateParam));
    }

    /**
     * 更新数据源状态
     *
     * @param id     数据源ID
     * @param status 新状态
     * @return 更新结果
     */
    @ApiOperation(value = "更新数据源状态", notes = "更新数据源的有效状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据源ID", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态,1:有效,0:无效", required = true, dataType = "int", paramType = "query")
    })
    @PostMapping("/updateStatus")
    public BaseResponse<Boolean> updateStatus(
            @NotNull(message = "数据源ID不能为空") Integer id,
            @NotNull(message = "状态不能为空") Integer status) {
        if (id == null || status == null) {
            return BaseResponse.createParamError("数据源ID和状态不能为空");
        }
        if (Objects.equals(status, 0) && guardItemService.countByDatasourceId(id) > 0) {
            return BaseResponse.createParamError("数据源下有在用的稽核项，不能删除");
        }
        boolean updateStatus = guardDatasourceService.updateStatus(id, status);
        return BaseResponse.createSuccess(updateStatus);
    }

    /**
     * 根据ID查询数据源
     * 
     * @param id 数据源ID
     * @return 数据源信息
     */
    @ApiOperation(value = "根据ID查询数据源", notes = "根据数据源ID获取详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "数据源ID", required = true, dataType = "int", paramType = "query", example = "1")
    })
    @GetMapping("/getById")
    public BaseResponse<GuardDatasource> getById(@NotNull(message = "数据源ID不能为空") Integer id) {
        GuardDatasource datasource = guardDatasourceService.getById(id);
        return BaseResponse.createSuccess(datasource);
    }

    /**
     * 根据名称查询数据源
     * 
     * @param name 数据源名称
     * @return 数据源信息
     */
    @ApiOperation(value = "根据名称查询数据源", notes = "根据数据源名称获取详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据源名称", required = true, dataType = "string", paramType = "query", example = "MySQL测试库")
    })
    @GetMapping("/getByName")
    public BaseResponse<GuardDatasource> getByName(@NotBlank(message = "数据源名称不能为空") String name) {
        return BaseResponse.createSuccess(guardDatasourceService.getByName(name));
    }

    /**
     * 数据源名称是否可用
     * 
     * @param name 数据源名称
     */
    @ApiOperation(value = "数据源名称是否可用", notes = "根据数据源名称判断是否可用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "数据源名称", required = true, dataType = "string", paramType = "query", example = "MySQL测试库")
    })
    @GetMapping("/nameAvailable")
    public BaseResponse<Boolean> nameAvailable(@NotBlank(message = "数据源名称不能为空") String name) {
        return BaseResponse.createSuccess(guardDatasourceService.nameAvailable(name));
    }

    /**
     * 搜索数据源
     */
    @ApiOperation(value = "搜索数据源", notes = "支持分页和关键词搜索的数据源查询")
    @GetMapping("/search")
    public BaseResponse<GuardDatasourceSearchResp> search(GuardDatasourceSearchParam searchParam) {
        GuardDatasourceSearchResp resp = guardDatasourceService.search(searchParam);
        return BaseResponse.createSuccess(resp);
    }

}
