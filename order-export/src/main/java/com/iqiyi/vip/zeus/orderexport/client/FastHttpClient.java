package com.iqiyi.vip.zeus.orderexport.client;

import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;


@Component
public class FastHttpClient {

    @Resource
    private RestTemplate fastRestTemplate;

    @Resource
    private HttpClient httpClient;

    public Map getRemoteResponse(String url, Map<String, Object> params) {
        return httpClient.getRemoteResponse(url, params, fastRestTemplate);
    }

}
