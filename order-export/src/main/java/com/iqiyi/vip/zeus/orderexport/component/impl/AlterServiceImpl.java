package com.iqiyi.vip.zeus.orderexport.component.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.zeus.core.enums.ExpiringDataEnum;
import com.iqiyi.vip.zeus.orderexport.client.FastHttpClient;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;

import static com.qiyi.ocm.common.utils.MD5Help.getMD5Str;

/**
 * 奇眼-告警服务使用手册wiki： http://wiki.qiyi.domain/pages/viewpage.action?pageId=62947995
 *
 * @Author: Lin Peihui
 * @Date: 2022/9/15
 */
@Component
public class AlterServiceImpl implements AlterService {

    private static final String system = "vip-job";
    private static final String token = "5356srfrtw3w4tgrwr2fdg3r2t3t";
    private static final String sys = "vip-xuanwu";
    private static final String signKey = "zMt&Igmmi8&9Wle{";
    @Resource
    private FastHttpClient fastHttpClient;
    @Resource
    private CloudConfig cloudConfig;
    @Value("${alertUrl}")
    private String url;

    @Override
    public boolean sendHotChat(String title, String content, String receiveUsers) {
        if (StringUtils.isNotBlank(receiveUsers) && receiveUsers.length() < 2) {
            return true;
        }
        if (StringUtils.isBlank(receiveUsers)) {
            receiveUsers = cloudConfig.getProperty("hot_chat_receiver", "linpeihui");
        }
        if (StringUtils.isBlank(receiveUsers)) {
            return true;
        }
        String reliaoUrl = "http://devops.vip.online.qiyi.qae/vip-devops/api/v2/alert/simpleNotice";
        Map<String, Object> params = new HashMap<>();
        params.put("noticeUsers", receiveUsers);
        params.put("title", title);
        params.put("content", content);
        params.put("triggerTimeStamp", System.currentTimeMillis());
        params.put("noticeChannel", "hotchat");
        params.put("sysCode", system);
        params.put("token", token);
        Map result = fastHttpClient.getRemoteResponse(reliaoUrl, params);

        return "A00000".equals(result.get("code"));
    }

    //type 0:规则 1:套餐
    @Override
    public boolean sendMailAndHotChat(Long code, Long userId, String receiveUsers, String content, String contentHotChat, Integer type) {
        Map<String, Object> param = new HashMap<>();
        param.put("sys", sys);
        Long timestamp = System.currentTimeMillis();
        param.put("timestamp", timestamp);
        String sign = getMD5Str(param, signKey);
        param.put("sign", sign);
        String idempotentKey = "" + timestamp;
        param.put("idempotentKey", idempotentKey);
        param.put("userId", userId);
        param.put("receiveUsers", receiveUsers);
        param.put("isSendLeader", 0);
        param.put("alertType", 0);
        param.put("sendTypes", "1,2");
        if (type.equals(ExpiringDataEnum.QIYUE_RULE.getValue())) {
            param.put("subject", "【重要提醒】价格规则即将到期");
            param.put("preface", "【即将到期】奇悦——交易管理-价格规则（非iOS）");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/trade/priceRuleEngineManageList");
            param.put("title", "价格规则（非iOS）即将到期");
        } else if (type.equals(ExpiringDataEnum.PACKAGE.getValue())) {
            param.put("subject", "【重要提醒】收银台套餐即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-收银台套餐");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/combo/list");
            param.put("title", "收银台套餐即将过期");
        } else if (type.equals(ExpiringDataEnum.STORE_CONFIG.getValue())) {
            param.put("subject", "【重要提醒】收银台路由即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-收银台路由");
            param.put("comfirmUrl", "https://admin-test.vip.qiyi.domain/ops/cashier/cashier-routes-v2/routes-config/list");
            param.put("title", "收银台路由即将过期");
        } else if (type.equals(ExpiringDataEnum.STORE_SWITCH.getValue())) {
            param.put("subject", "【重要提醒】收银台开关即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-收银台开关");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/counter-switch-manager/list");
            param.put("title", "收银台开关即将过期");
        } else if (type.equals(ExpiringDataEnum.PAY_TYPE.getValue())) {
            param.put("subject", "【重要提醒】收银台支付方式即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-收银台支付方式");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/pay-way/vip-type/list");
            param.put("title", "收银台支付方式即将过期");
        } else if (type.equals(ExpiringDataEnum.POINTS.getValue())) {
            param.put("subject", "【重要提醒】收银台积分即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-收银台积分");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/cashier-credit/list");
            param.put("title", "收银台积分即将过期");
        } else if(type.equals(ExpiringDataEnum.BUNDLE.getValue())){
            param.put("subject", "【重要提醒】收银台加价购即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-加价购/买赠");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/rise-goods-and-gift-manage/list");
            param.put("title", "加价购/买赠即将过期");
        } else if(type.equals(ExpiringDataEnum.SMART_STORE.getValue())){
            param.put("subject", "【重要提醒】通用/营销收银台即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-通用/营销收银台");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/generic-cashier/list");
            param.put("title", "通用/营销收银台策略即将过期");
        } else if(type.equals(ExpiringDataEnum.PAY_CHANNEL_MARKETING.getValue())){
            param.put("subject", "【重要提醒】支付渠道营销即将到期");
            param.put("preface", "【即将到期】奇悦——交易管理-支付渠道营销");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/trade/pay-channel-marketing/list");
            param.put("title", "支付渠道营销即将过期");
        } else if(type.equals(ExpiringDataEnum.PAY_TYPE_MARKETING.getValue())){
            param.put("subject", "【重要提醒】支付方式营销即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-支付方式营销");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/pay-way-marketing/list");
            param.put("title", "支付方式营销即将过期");
        } else if(type.equals(ExpiringDataEnum.GIFT.getValue())){
            param.put("subject", "【重要提醒】收银台置顶管理即将到期");
            param.put("preface", "【即将到期】奇悦——收银台管理-收银台置顶管理");
            param.put("comfirmUrl", "https://admin.vip.qiyi.domain/ops/cashier/top-gift/list");
            param.put("title", "收银台置顶管理即将过期");
        }
        param.put("mailContent", content);
        param.put("remark", "您负责的数据即将到期，详细数据请查看邮件正文。请及时处理避免产生线上问题");
        param.put("contentHotChat", contentHotChat);

        Map result = fastHttpClient.getRemoteResponse(url, param);
        return "A00000".equals(result.get("code"));
    }

    public static void main(String[] args) {
        new AlterServiceImpl().sendMailAndHotChat(null, null, "weihan", "content", "contentHotChat", 0);
    }

}
