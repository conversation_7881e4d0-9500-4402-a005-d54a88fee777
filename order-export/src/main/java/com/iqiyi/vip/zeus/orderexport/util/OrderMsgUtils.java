package com.iqiyi.vip.zeus.orderexport.util;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.orderexport.model.OrderDto;

/**
 * @author: linpeihui
 * @createTime: 2023/10/17
 */
@Slf4j
public class OrderMsgUtils {

    /**
     * true:压测模式 false:非压测模式
     */
    public static final String PRESSURE_TEST_MODE = "Pressure-Test-Mode";

    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    private static final String MODE_TYPE_PRESS_TEST = "pressureTest";


    /**
     *
     * @param messageExt 消息实体
     * @return true:是压测消息、不需要处理，false:非压测消息、需要处理
     */
    public static boolean isPressureOrderMsg(MessageExt messageExt) {
        String pressureTest = messageExt.getUserProperty(PRESSURE_TEST_MODE);
        return StringUtils.isNotBlank(pressureTest) && "true".equalsIgnoreCase(pressureTest);
    }

    /**
     * true 需要处理
     * false 不需要处理
     */
    public static boolean needDealWith(OrderDto order) {
        try {
            String refer = order.getRefer();
            if (StringUtils.isBlank(refer)) {
                return true;
            }
            Map<String, Object> map = GSON.fromJson(refer, new TypeToken<Map<String, Object>>() {
            }.getType());
            Object modeTypeObj = map.get("modeType");
            if (Objects.isNull(modeTypeObj)) {
                return true;
            }
            return !MODE_TYPE_PRESS_TEST.equalsIgnoreCase(String.valueOf(modeTypeObj));
        } catch (Exception e) {
            log.error("need deal with order error", e);
            return true;
        }
    }
}
