package com.iqiyi.vip.zeus.orderexport.config;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.constant.MQConstant;
import com.iqiyi.vip.zeus.orderexport.consumer.SignPaidMsgHandler;

/**
 * @Author: <PERSON> P<PERSON>hui
 * @Date: 2020/11/23
 */
@Profile({"!sg"})
@Configuration
public class MainLandRMQConfig {

    @Value("${trade.order.rmq.namesrvaddr}")
    private String tradeOrderRmqNamesrvAddr;

    @Value("${trade.order.rmq.sign.paid.consumer.token}")
    private String tradeOrderRmqSignPaidConsumerToken;

    @Resource
    private SignPaidMsgHandler signPaidMsgHandler;

    @Bean(name = "signPaidMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer signPaidMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.TRADE_ORDER_SIGN_PAID_CONSUMER_GROUP);
        consumer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        consumer.setToken(tradeOrderRmqSignPaidConsumerToken);
        consumer.subscribe(MQConstant.TRADE_ORDER_SIGN_PAID_TOPIC, "*");
        consumer.registerMessageListener(signPaidMsgHandler);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

}
