package com.iqiyi.vip.zeus.orderexport.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/24
 */
@Getter
@Setter
@ToString
public class OrderReferDto {

    private Boolean userAutoRenew;

    private String suitABTestId;

    private String actCode;

    private List<VipGiftInfo> giftInfos;

    /**
     * IAP自动续费苹果到期时间
     */
    private Timestamp expireTime;
    /**
     * 团购系统团单号
     */
    private String groupId;

    /**
     * 支付方式活动码
     */
    private String payTypeActCode;

    /**
     * 本次订单之前的到期时间
     */
    private Timestamp beforeDeadline;

    /**
     * 本次订单之前 - 用户是否付费
     */
    private Integer beforePaidSign;

    private String signOrderCode;

    private String phoneNum;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 扩展参数
     */
    private Map<String, Object> businessProperty;
}