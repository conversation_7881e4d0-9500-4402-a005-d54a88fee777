package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class OrderGuardianRowData {

    private String passportIdText;

    private List<List<String>> orderInfoList;

    public OrderGuardianRowData(String passportIdText, List<List<String>> orderInfoList) {
        this.passportIdText = passportIdText;
        this.orderInfoList = orderInfoList;
    }
}