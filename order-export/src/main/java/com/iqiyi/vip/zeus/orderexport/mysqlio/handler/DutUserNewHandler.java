package com.iqiyi.vip.zeus.orderexport.mysqlio.handler;

import com.iqiyi.vip.order.dal.OrderRepository;
import com.iqiyi.vip.order.dal.model.Order;
import com.iqiyi.vip.zeus.core.utils.BizDateUtil;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.component.impl.AgreementInfoService;
import com.iqiyi.vip.zeus.orderexport.constant.AgreementConstants;
import com.iqiyi.vip.zeus.orderexport.entity.AgreementNoInfo;
import com.iqiyi.vip.zeus.orderexport.entity.OrderInfo;
import com.iqiyi.vip.zeus.orderexport.exception.NetWorkException;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AgreementNoInfoDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;
import io.micrometer.core.instrument.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
@Profile("!sg")
@Component
public class DutUserNewHandler extends AbstractEventHandler {

    //订单上renewType和用户签约关系上的协议不一致
    private static final String ORDER_RENEW_TYPE_AND_USER_AGREEMENT_NOT_MATCH = "viptrade_order_renewType_and_userAgreement_not_match";
    private static final String ORDER_RENEW_TYPE_AND_USER_AGREEMENT_NOT_MATCH_KEY_PREFIX = "viptrade_order_renewType_and_userAgreement_not_match:";

    //签约关系上会员类型跟协议编号上的会员类型不一致
    private static final String VIP_TYPE_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH = "viptrade_vipType_between_userAgreement_and_agreementNo_not_match";
    private static final String VIP_TYPE_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH_KEY_PREFIX = "viptrade_vipType_between_userAgreement_and_agreementNo_not_match:";

    //签约关系上amount跟协议编号上的amount不一致
    private static final String AMOUNT_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH = "viptrade_amount_between_userAgreement_and_agreementNo_not_match";
    private static final String AMOUNT_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH_KEY_PREFIX = "viptrade_amount_between_userAgreement_and_agreementNo_not_match:";

    @Value("${order.renewType.and.userAgreement.not.match.threshold:10}")
    private Integer orderRenewTypeAndUserAgreementNotMatchThreshold;
    @Value("${vipType.between.userAgreement.and.agreementNo.not.match.threshold:10}")
    private Integer vipTypeBetweenUserAgreementAndAgreementNoNotMatchThreshold;
    @Value("${amount.between.userAgreement.and.agreementNo.not.match.threshold:10}")
    private Integer amountBetweenUserAgreementAndAgreementNoNotMatchThreshold;

    @Resource
    private AgreementInfoService agreementInfoService;
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource
    private AlterService alterService;
    @Resource
    private AgreementNoInfoDao agreementNoInfoDao;
    @Resource
    private AutoRenewDutTypeDao autoRenewDutTypeDao;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public boolean accept(MapCanalEvent event) {
        return super.accept(event);
    }

    @Override
    public void handCanalEvent(MapCanalEvent event) {
        boolean updateEvent = CanalEventUtil.isUpdate(event.getEventType());
        Map<String, Object> rowAfter = event.getRowAfter();
        Integer autoRenew = MapUtils.getInteger(rowAfter, "auto_renew");
        Integer signTime = MapUtils.getInteger(rowAfter, "sign_time");
        if (autoRenew == null || autoRenew == 0) {
            return;
        }

        Long userId = MapUtils.getLong(rowAfter, "user_id");
        Integer agreementNo = MapUtils.getInteger(rowAfter, "agreement_no");
        Integer dutType = MapUtils.getInteger(rowAfter, "type");
        Integer vipType = MapUtils.getInteger(rowAfter, "vip_type");
        Integer amount = MapUtils.getInteger(rowAfter, "amount");
        String orderCode = MapUtils.getString(rowAfter, "order_code");
        if (updateEvent) {
            Map<String, Object> rowBefore = event.getRowBefore();
            Integer beforeAutoRenew = MapUtils.getInteger(rowBefore, "auto_renew");
            Integer beforeAgreementNo = MapUtils.getInteger(rowAfter, "agreement_no");
            Integer beforeDutType = MapUtils.getInteger(rowAfter, "type");
            Integer beforeVipType = MapUtils.getInteger(rowBefore, "vip_type");
            Integer beforeAmount = MapUtils.getInteger(rowAfter, "amount");
            if (Objects.equals(beforeAutoRenew, autoRenew)
                && Objects.equals(beforeAgreementNo, agreementNo)
                && Objects.equals(beforeDutType, dutType)
                && Objects.equals(beforeVipType, vipType)
                && Objects.equals(beforeAmount, amount)) {
                return;
            }
        }

        try {
            checkOrderRenewTypeAndUserNewAgreement(userId, agreementNo, dutType, orderCode);
        } catch (Exception e) {
            log.error("checkOrderRenewTypeAndUserNewAgreement error, userId:{}, agreementNo:{}", userId, agreementNo, e);
            if (e instanceof NetWorkException) {
                throw e;
            }
        }

        try {
            checkVipTypeBetweenUserAgreementAndAgreementNo(userId, vipType, agreementNo, dutType);
        } catch (Exception e) {
            log.error("checkVipTypeBetweenUserAgreementAndAgreementNo error, userId:{}, vipType:{}, agreementNo:{}", userId, vipType, agreementNo, e);
        }

        try {
            checkAmountBetweenUserAgreementAndAgreementNo(userId, amount, agreementNo);
        } catch (Exception e) {
            log.error("checkAmountBetweenUserAgreementAndAgreementNo error, userId:{}, amount:{}, agreementNo:{}", userId, amount, agreementNo, e);
        }
    }

    @Override
    public String getTableName() {
        return "boss_dut_user_new";
    }

    private void checkAmountBetweenUserAgreementAndAgreementNo(Long userId, Integer amount, Integer agreementNo) {
        if (agreementNo == null) {
            return;
        }
        AgreementNoInfo agreementNoInfo = agreementNoInfoDao.getById(agreementNo);
        if (agreementNoInfo == null) {
            return;
        }
        if (Objects.equals(amount, agreementNoInfo.getAmount())) {
            return;
        }

        try {
            Metrics.counter(AMOUNT_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH).increment();
        } catch (Exception e) {
            log.error("metric:{} increment error", AMOUNT_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH, e);
        }

        String redisKey = AMOUNT_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH + BizDateUtil.yyyyMMddHHmmssString(new Date());
        Long notMatchCountLong = redisTemplate.opsForValue().increment(redisKey);
        if (Objects.isNull(notMatchCountLong)) {
            return;
        }
        int notMatchCount = notMatchCountLong.intValue();
        if (notMatchCount == 1) {
            redisTemplate.expire(redisKey, 3, TimeUnit.MINUTES);
        }

        String alterContext = String.format("userId:%s, 签约关系上的amount:%s, 签约协议对应的amount:%s, 签约协议号:%s", userId, amount, agreementNoInfo.getAmount(), agreementNo);
        if (notMatchCount < amountBetweenUserAgreementAndAgreementNoNotMatchThreshold) {
            alterService.sendHotChat("签约关系amount跟协议编号的amount不一致", alterContext, "");
        }
    }

    /**
     * 检查签约关系上会员类型跟协议编号上的会员类型是否一致
     * @param userId
     * @param agreementNo
     * @param dutType
     */
    private void checkVipTypeBetweenUserAgreementAndAgreementNo(Long userId, Integer vipType, Integer agreementNo, Integer dutType) {
        Integer vipTypeOfAgreementNo = null;
        if (agreementNo != null) {
            vipTypeOfAgreementNo = agreementNoInfoDao.getVipTypeById(agreementNo);
        } else {
            vipTypeOfAgreementNo = autoRenewDutTypeDao.getVipTypeByDutType(dutType);
        }
        if (Objects.equals(vipType, vipTypeOfAgreementNo)) {
            return;
        }

        try {
            Metrics.counter(VIP_TYPE_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH).increment();
        } catch (Exception e) {
            log.error("metric:{} increment error", VIP_TYPE_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH, e);
        }

        String redisKey = VIP_TYPE_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH_KEY_PREFIX + BizDateUtil.yyyyMMddHHmmssString(new Date());
        Long notMatchCountLong = redisTemplate.opsForValue().increment(redisKey);
        if (Objects.isNull(notMatchCountLong)) {
            return;
        }
        int notMatchCount = notMatchCountLong.intValue();
        if (notMatchCount == 1) {
            redisTemplate.expire(redisKey, 3, TimeUnit.MINUTES);
        }

        String alterContext = String.format("userId:%s, 签约关系上的会员类型:%s, 签约协议号所属的会员类型:%s, 签约协议号:%s", userId, vipType, vipTypeOfAgreementNo, agreementNo);
        if (notMatchCount < vipTypeBetweenUserAgreementAndAgreementNoNotMatchThreshold) {
            alterService.sendHotChat("签约关系会员类型跟协议编号的会员类型不一致", alterContext, "");
        }
    }

    /**
     * 检查订单上renewType跟签约关系上协议编号是否一致
     * @param agreementNo
     * @param dutType
     * @param orderCode
     */
    private void checkOrderRenewTypeAndUserNewAgreement(Long userId, Integer agreementNo, Integer dutType, String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return;
        }
        Order detailedOrder = orderRepository.findByOrderCode(orderCode);
        OrderInfo orderInfo = new OrderInfo(detailedOrder);
        if (orderInfo.getRenewType() == null) {
            return;
        }
        Integer orderRenewType = orderInfo.getRenewType();
        Integer userNewAgreementNo = orderRenewType > AgreementConstants.INTI_AGREEMENT_NO ? agreementNo : dutType;
        if (Objects.equals(userNewAgreementNo, orderRenewType)) {
            return;
        }

        try {
            Metrics.counter(ORDER_RENEW_TYPE_AND_USER_AGREEMENT_NOT_MATCH).increment();
        } catch (Exception e) {
            log.error("metric:{} increment error", ORDER_RENEW_TYPE_AND_USER_AGREEMENT_NOT_MATCH, e);
        }

        String redisKey = ORDER_RENEW_TYPE_AND_USER_AGREEMENT_NOT_MATCH_KEY_PREFIX + BizDateUtil.yyyyMMddHHmmssString(new Date());
        Long notMatchCountLong = redisTemplate.opsForValue().increment(redisKey);
        if (Objects.isNull(notMatchCountLong)) {
            return;
        }
        int notMatchCount = notMatchCountLong.intValue();
        if (notMatchCount == 1) {
            redisTemplate.expire(redisKey, 3, TimeUnit.MINUTES);
        }

        String alterContext = String.format("userId:%s, 签约的协议号:%s, 订单renewType:%s, 订单号:%s", userId, agreementNo, orderRenewType, orderCode);
        if (notMatchCount < orderRenewTypeAndUserAgreementNotMatchThreshold) {
            alterService.sendHotChat("订单renewType跟签约关系上协议编号不一致", alterContext, "");
        }
    }

    // 每分钟执行
    @Scheduled(cron = "0 0/1 * * * ?")
    public void orderRenewTypeAndUserAgreementNotMatchAggregateAlert() {
        Timestamp timestamp = BizDateUtil.plusMinutes(Timestamp.from(Instant.now()), -1);
        String redisKeySuffix = BizDateUtil.yyyyMMddHHmmssString(timestamp);
        String agreementNoNotMatchRedisKey = ORDER_RENEW_TYPE_AND_USER_AGREEMENT_NOT_MATCH_KEY_PREFIX + redisKeySuffix;
        // 获取当前计数并设置为0
        Integer agreementNoNotMatchCount = redisTemplate.opsForValue().getAndSet(agreementNoNotMatchRedisKey, 0);
        if (agreementNoNotMatchCount == null) {
            agreementNoNotMatchCount = 0;
        }
        // 如果计数超过阈值，则发送聚合告警
        if (agreementNoNotMatchCount > orderRenewTypeAndUserAgreementNotMatchThreshold) {
            String alterContext = String.format("%s: 不一致个数: %d", BizDateUtil.datetime2String(timestamp), agreementNoNotMatchCount);
            alterService.sendHotChat("订单renewType跟签约关系上协议编号不一致", alterContext, "");
        }


        String vipTypeNotMatchRedisKey = VIP_TYPE_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH_KEY_PREFIX + redisKeySuffix;
        // 获取当前计数并设置为0
        Integer vipTypeNotMatchCount = redisTemplate.opsForValue().getAndSet(vipTypeNotMatchRedisKey, 0);
        if (vipTypeNotMatchCount == null) {
            vipTypeNotMatchCount = 0;
        }
        // 如果计数超过阈值，则发送聚合告警
        if (vipTypeNotMatchCount > vipTypeBetweenUserAgreementAndAgreementNoNotMatchThreshold) {
            String alterContext = String.format("%s: 不一致个数: %d", BizDateUtil.datetime2String(timestamp), vipTypeNotMatchCount);
            alterService.sendHotChat("签约关系会员类型跟协议编号的会员类型不一致", alterContext, "");
        }


        String amountNotMatchRedisKey = AMOUNT_BETWEEN_USER_AGREEMENT_AND_AGREEMENT_NO_NOT_MATCH_KEY_PREFIX + redisKeySuffix;
        // 获取当前计数并设置为0
        Integer amountNotMatchCount = redisTemplate.opsForValue().getAndSet(amountNotMatchRedisKey, 0);
        if (amountNotMatchCount == null) {
            amountNotMatchCount = 0;
        }
        // 如果计数超过阈值，则发送聚合告警
        if (amountNotMatchCount > amountBetweenUserAgreementAndAgreementNoNotMatchThreshold) {
            String alterContext = String.format("%s: 不一致个数: %d", BizDateUtil.datetime2String(timestamp), amountNotMatchCount);
            alterService.sendHotChat("签约关系amount跟协议编号的amount不一致", alterContext, "");
        }
    }

}
