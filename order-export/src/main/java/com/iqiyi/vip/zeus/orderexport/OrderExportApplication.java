package com.iqiyi.vip.zeus.orderexport;

import com.alibaba.csp.sentinel.init.InitExecutor;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;

/**
 * <AUTHOR>
 */
@ComponentScan(basePackages = {"com.iqiyi.vip.order.dal","com.iqiyi.vip.zeus"})
@ImportResource({"classpath:applicationContext-orderDal.xml"})
@EnableScheduling
@EnableMethodCache(basePackages = {"com.iqiyi.vip.zeus"})
@SpringBootApplication
@EnableDiscoveryClient
@EnableCloudConfig
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableTransactionManagement(proxyTargetClass = true)
public class OrderExportApplication {

    public static void main(String[] args) {
        triggerSentinelInit();
        SpringApplication.run(OrderExportApplication.class, args);
    }

    private static void triggerSentinelInit() {
        new Thread(InitExecutor::doInit).start();
    }

}
