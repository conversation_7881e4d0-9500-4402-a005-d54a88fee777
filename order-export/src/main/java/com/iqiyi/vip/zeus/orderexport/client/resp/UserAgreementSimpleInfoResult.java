package com.iqiyi.vip.zeus.orderexport.client.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created at: 2021-07-07
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserAgreementSimpleInfoResult {

    /**
     * 用户
     */
    private Long uid;
    /**
     * 协议模板code
     */
    private String agreementCode;
    /**
     * 协议编号
     */
    private Integer agreementNo;
    /**
     * 协议模板类型
     */
    private Integer agreementType;
    /**
     * 平台code
     */
    private String platformCode;
    /**
     * 协议状态
     */
    private Integer agreementStatus;
    /**
     * 会员产品code
     */
    private String productCode;

    private Integer dutType;

    /**
     * 操作时间
     */
    private Timestamp operateTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;
    /**
     * 签约时长
     */
    private Integer amount;
}
