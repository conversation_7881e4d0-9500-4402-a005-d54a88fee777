package com.iqiyi.vip.zeus.orderexport.entity;

import com.google.common.base.Splitter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;


/**
 * @Author: <PERSON>
 * @Date: 2020/12/01
 */
@Data
public class PaymentType implements Serializable {

    public static final int CHARGEBACK_CAN = 1;
    public static final int CHARGEBACK_CANNOT = 0;
    public static final int CHARGEAUTO_CAN = 1;
    public static final int CHARGEAUTO_CANNOT = 0;
    public static final int BACKGROUND_CAN = 1;
    public static final int BACKGROUND_CANNOT = 0;
    private static ConcurrentMap<Long, Map<String, String>> propertiesCache = new ConcurrentHashMap<>();
    private static Map<String, String> emptyProperties = new HashMap<>();
    /**
     * id
     */
    protected Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否支持退单
     */
    private int isChargeback;
    /**
     * 是否支持自动退款
     */
    private int isChargeauto;
    /**
     * 是否支持后台方式退款
     */
    private int isBackground;
    private Boolean isSupportSign = false;
    private Long basicPayTypeId;
    private Long signPayTypeId;
    //是否支持免密支付签约
    private Boolean isSupportPasswordFreeSign = false;
    /**
     * 支付方式状态，0 ： 已下线 1：正常
     */
    private int status;
    /**
     * 支付方式对应的支付中心编码
     */
    private String payCenterCode;
    /**
     * 类别,1:在线购买,2:手机支付,3:OTT,4:其他
     */
    private Integer type;
    /**
     * 所对应的纯签约支付方式
     **/
    private Long pureSigningPayTypeId;
    private Integer payChannel;
    private String properties;

    public Boolean getIsSupportSign() {
        return isSupportSign;
    }

    /**
     * Not thread-safe
     */
    public Map<String, String> properties() {
        if (StringUtils.isEmpty(properties)) {
            return emptyProperties;
        }

        return propertiesCache.computeIfAbsent(id, (id) -> Splitter.on(",").withKeyValueSeparator("=").split(properties));
    }
}