package com.iqiyi.vip.zeus.orderexport.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.Set;

import com.iqiyi.vip.zeus.core.context.RequestContext;
import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.eagleclient.api.DevOpsApi;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;

/**
 * @author: guojing
 * @date: 2023/12/13 11:05
 */
@Slf4j
@Component
public class LoginUserInterceptor implements HandlerInterceptor {

    private static final Set<String> EXCLUDE_PATH = new HashSet<>();

    static {
        EXCLUDE_PATH.add("/zeus/orderGuardian/queryDetailData");
    }

    @Resource
    private DevOpsApi devOpsApi;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (EXCLUDE_PATH.contains(request.getRequestURI())){
            return true;
        }
        String oaAccount = request.getParameter("oaAccount");
        if (StringUtils.isBlank(oaAccount)) {
            throw BizException.newParamException("oaAccount参数不能为空");
        }
        AuthorityUser authorityUser = devOpsApi.oaAccountDetails(oaAccount);
        if (authorityUser == null) {
            throw BizException.newParamException("oaAccount账号不存在");
        }
        RequestContextHolder.setRequestContext(new RequestContext(authorityUser));
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        RequestContextHolder.cleanRequestContext();
    }

}
