package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.PayChannelMarketing;
import com.iqiyi.vip.zeus.orderexport.entity.PayTypeMarketing;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PayChannelMarketingDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PayTypeMarketingDao;

/**
 * <AUTHOR>
 * @date 2024/4/10 9:14
 */
@Repository
@Profile("!sg")
public class PayTypeMarketingDaoImpl implements PayTypeMarketingDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<PayTypeMarketing> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(PayTypeMarketingDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
