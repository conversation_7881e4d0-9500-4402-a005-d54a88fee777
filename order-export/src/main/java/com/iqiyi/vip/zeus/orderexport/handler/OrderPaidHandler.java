package com.iqiyi.vip.zeus.orderexport.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Maps;
import com.iqiyi.vip.order.dal.OrderRepository;
import com.iqiyi.vip.order.dal.model.Order;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.zeus.orderexport.client.CommodityClient;
import com.iqiyi.vip.zeus.orderexport.component.impl.PaymentInfoService;
import com.iqiyi.vip.zeus.orderexport.constant.MQConstant;
import com.iqiyi.vip.zeus.orderexport.entity.OrderInfo;
import com.iqiyi.vip.zeus.orderexport.entity.Product;
import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;
import com.iqiyi.vip.zeus.orderexport.enums.OrderTypeEnum;
import com.iqiyi.vip.zeus.orderexport.enums.TypeEnum;
import com.iqiyi.vip.zeus.orderexport.exception.CustomJsonException;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.ProductDao;
import com.iqiyi.vip.zeus.orderexport.util.CloudConfigUtil;
import com.iqiyi.vip.zeus.orderexport.util.ProductUtils;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: Lin Peihui
 * @Date: 2022/9/19
 */
@Slf4j
@Component
public class OrderPaidHandler implements ApplicationContextAware {
    private ApplicationContext context;

    private static final Integer DELAY_TIME_IN_FIXED_SECOND = 60;
    private static final Integer DELAY_TIME_IN_ONE_SECOND = 1;
    private static final ImmutableSet<Integer> FILTER_PACKAGE_SUB_TYPES = ImmutableSet.of(21, 22);

    @Resource
    private ProductDao productDao;
    @Resource
    private CommodityClient commodityClient;
    @Resource
    private PaymentInfoService paymentInfoService;
    @Resource
    private OrderRepository orderRepository;

    @Value("${intl.flag:false}")
    private Boolean isIntlEnv;

    public boolean notNeedHandle(OrderDto order) {
        if (order.getStatus() != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
            return true;
        }
        //过滤测试单
        if (order.getType() != null && order.getType() == -1) {
            return true;
        }
        if (notNeedNotifyType(order.getType())) {
            return true;
        }
        if (order.getOrderType() != null && OrderTypeEnum.TRADE.name().equals(order.getOrderType())) {
            return true;
        }

        if (StringUtils.isNotEmpty(order.getSkuId())) {
            Optional<QuerySkuResponse> querySkuResponseOptional = commodityClient.queryCommodity(order.getSkuId());
            if (querySkuResponseOptional.isPresent()) {
                boolean isPureSignProduct = ProductUtils.isPureSignProduct(querySkuResponseOptional.get());
                if (isPureSignProduct) {
                    return true;
                }
            }
        }

        Product product = productDao.getById(order.getProductId());
        if (!needNotifyProduct(order, product)) {
            log.info("Not need handle paid msg, orderCode:{}, productId:{}, productChargeType:{}",
                    order.getOrderCode(), product.getId(), product.getChargeType());
            return true;
        }

        if (product.getDeadline() != null && product.getDeadline().before(new Date())) {
            if (product.getPackageSubType() != null && FILTER_PACKAGE_SUB_TYPES.contains(product.getPackageSubType())) {
                PaymentTypeDTO paymentTypeDTO = paymentInfoService.getPaymentType(order.getPayType());
                if (paymentTypeDTO != null && paymentTypeDTO.getPayChannel() == 9) {
                    log.warn("苹果加更礼订单关联的商品已过期。orderCode: {}, productCode: {}", order.getOrderCode(), product.getCode());
                    return true;
                }
            }
        }

        Order detailedOrder = orderRepository.findByOrderCode(order.getOrderCode());
        OrderInfo latestOrderInfo = new OrderInfo(detailedOrder);

        return StringUtils.isNotBlank(latestOrderInfo.getNotifyResult())
            && ("A00000".equals(order.getNotifyResult()) || "A00001".equals(order.getNotifyResult()));
    }

    public void sendDelayMsg(String orderCode, Integer currentCount) {
        Integer maxRetryCount = CloudConfigUtil.getRightMonitorRetryCount();
        if (currentCount > maxRetryCount) {
            log.info("Order {} delay {} count, reach max retry count {}", orderCode, currentCount - 1, maxRetryCount);
            return;
        }
        Map<String, String> msgMap = Maps.newHashMap();
        msgMap.put("orderCode", orderCode);
        msgMap.put("retriedCount", String.valueOf(currentCount));
        String jsonString = JSON.toJSONString(msgMap);
        try {
            byte[] msgBytes = jsonString.getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message msg = new Message(MQConstant.TRADE_ORDER_MONITOR_DELAY_TOPIC, msgBytes);
            msg.setKeys(orderCode);
            msg.setDelayTimeInSeconds(DELAY_TIME_IN_FIXED_SECOND);
            DefaultMQProducer orderMonitorDelayProducer = context.getBean("orderMonitorDelayProducer", DefaultMQProducer.class);
            SendResult sendResult = orderMonitorDelayProducer.send(msg);
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.info("Send delay msg failed, orderCode:{}, sendResult:{}", orderCode, sendResult);
                throw new CustomJsonException("send retry open vip msg failed");
            }
            log.info("Send order delay msg msg success, orderCode:{}, count:{}", orderCode, currentCount);
        } catch (Exception e) {
            log.error("Some error occurred during process msg, orderCode:{}, count:{}", orderCode, currentCount, e);
            throw new CustomJsonException("process msg failed");
        }

    }

    public void sendDelayMsgOnce(String orderCode) {
        Map<String, String> msgMap = Maps.newHashMap();
        msgMap.put("orderCode", orderCode);
        String jsonString = JSON.toJSONString(msgMap);
        try {
            byte[] msgBytes = jsonString.getBytes(RemotingHelper.DEFAULT_CHARSET);
            Message msg = new Message(MQConstant.TRADE_ORDER_MONITOR_DELAY_TOPIC, msgBytes);
            msg.setKeys(orderCode);
            msg.setDelayTimeInSeconds(DELAY_TIME_IN_ONE_SECOND);
            DefaultMQProducer orderMonitorDelayProducer = context.getBean("orderMonitorDelayProducer", DefaultMQProducer.class);
            SendResult sendResult = orderMonitorDelayProducer.send(msg);
            if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
                log.info("Send delay msg failed, orderCode:{}, sendResult:{}", orderCode, sendResult);
                throw new CustomJsonException("send retry open vip msg failed");
            }
            log.info("Send order delay msg msg success, orderCode:{}", orderCode);
        } catch (Exception e) {
            log.error("Some error occurred during process msg, orderCode:{}", orderCode, e);
            throw new CustomJsonException("process msg failed");
        }

    }


    /**
     * 无需开通权益的type
     */
    private boolean notNeedNotifyType(Integer type) {
        return type != null && (TypeEnum.SETTLE.getCode() == type || TypeEnum.WECHAT_PAY_SCORE_COMPLETE.getCode() == type);
    }

    /**
     * 无需开通权益的商品
     */
    private boolean needNotifyProduct(OrderDto order, Product product) {
        if (order.getOrderType() == null) {
            return true;
        }
        return product.getChargeType() != null && product.getChargeType() != 3;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}
