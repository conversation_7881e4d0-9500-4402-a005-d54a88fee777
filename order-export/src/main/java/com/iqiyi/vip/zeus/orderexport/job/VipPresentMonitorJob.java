package com.iqiyi.vip.zeus.orderexport.job;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.zeus.orderexport.entity.PresentOrder;
import com.iqiyi.vip.zeus.orderexport.entity.PresentRecord;
import com.iqiyi.vip.zeus.orderexport.handler.PresentMetricHandler;
import com.iqiyi.vip.zeus.orderexport.model.PresentCount;
import com.iqiyi.vip.zeus.orderexport.persist.dao.present.VipPresentDao;
import com.iqiyi.vip.zeus.orderexport.util.DateUtils;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created at: 2021-02-05
 * <p>
 * 买赠服务订单赠送和领取监控Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VipPresentMonitorJob extends IJobHandler {

    /**
     * job默认执行间隔，单位：分
     */
    private static final int EXEC_TIME_INTERVAL_MINUTES = 3;
    /**
     * 上次执行时间
     */
    private AtomicReference<String> previousTime = new AtomicReference<>();

    @Value("${present.order.table.count}")
    int presentOrderTableCount;
    @Resource
    VipPresentDao vipPresentDao;

    private static final int PRESENT_MONITOR_THREAD_CORE_NUM = 30;
    private static ThreadPoolExecutor threadPoolExecutor;

    static {
        ThreadFactory presentMonitorThreadFactory = new ThreadFactoryBuilder().setNameFormat("presentMonitor-thread-pool-%d").build();
        threadPoolExecutor = new ThreadPoolExecutor(PRESENT_MONITOR_THREAD_CORE_NUM,
                PRESENT_MONITOR_THREAD_CORE_NUM,
                Integer.MAX_VALUE,
                TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(96),
                presentMonitorThreadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    @Job("vipPresentMonitorJob")
    public void execute() throws Exception {
        String param = JobHelper.getJobParam();
        Long jobId = JobHelper.getJobId();
        String execIntervalMinutesStr = param;
        int execIntervalMinutes = NumberUtils.toInt(execIntervalMinutesStr, EXEC_TIME_INTERVAL_MINUTES);

        log.info("---------- Start execute VipPresentMonitorJob, jobId:{} ----------", jobId);
        JobHelper.log("---------- Start execute VipPresentMonitorJob, jobId:{0} ----------", jobId);
        StopWatch stopWatch = StopWatch.createStarted();

        Timestamp now = DateUtils.currentTimestamp();
        Timestamp nMinutesAgo = DateUtils.minusMinutes(now, execIntervalMinutes);
        String nMinutesAgoStr = DateUtils.formatTimestamp(nMinutesAgo, DateUtils.DATE_MINUTE_PATTERN);
        String currentMinute = DateUtils.formatTimestamp(now, DateUtils.DATE_MINUTE_PATTERN);
        if (previousTime.get() != null && nMinutesAgoStr.compareTo(previousTime.get()) < 0) {
            String previousEndPayTime = previousTime.get();
            log.info("---------- VipPresentMonitorJob already executed, don't repeat, jobId:{}, previous endPayTime:{}. ----------", jobId, previousEndPayTime);
            JobHelper.log("---------- VipPresentMonitorJob already executed, don't repeat, jobId:{0}, previous endPayTime:{1}. ----------", jobId, previousEndPayTime);
            JobHelper.handleSuccess();
        }

        List<Future<PresentCount>> resultFutures = new ArrayList<>();
        for (int i = 0; i < presentOrderTableCount; i++) {
            String tableNo = String.format("%02d", i);
            Future<PresentCount> resultFuture = threadPoolExecutor.submit(new PresentOrderMonitorTask(tableNo, nMinutesAgoStr, currentMinute));
            resultFutures.add(resultFuture);
        }
        List<PresentCount> presentCounts = resultFutures.stream().map(future -> {
            PresentCount countInfo = null;
            try {
                countInfo = future.get();
            } catch (Exception e) {
                log.error("get result future failed", e);
            }
            return countInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        PresentCount totalPresentCount = calcTotalPresentCount(presentCounts);
        String presentOrderCountInfo = JacksonUtils.toJsonString(totalPresentCount.getPresentOrderCountMap());
        String presentRecordCountInfo = JacksonUtils.toJsonString(totalPresentCount.getPresentRecordCountMap());

        previousTime.set(currentMinute);
        log.info("---------- VipPresentMonitorJob finished, jobId:{}, presentOrderCount:{}, presentRecordCount:{}, cost:{}ms. ----------", jobId, presentOrderCountInfo, presentRecordCountInfo, stopWatch.getTime());
        JobHelper.log("---------- VipPresentMonitorJob finished, jobId:{0}, presentOrderCount:{1}, presentRecordCount:{2}, cost:{3}ms. ----------", jobId, presentOrderCountInfo, presentRecordCountInfo, stopWatch.getTime());
        JobHelper.handleSuccess();
    }

    private PresentCount calcTotalPresentCount(List<PresentCount> presentCounts) {
        if (CollectionUtils.isEmpty(presentCounts)) {
            return new PresentCount(Collections.emptyMap(), Collections.emptyMap());
        }
        Map<String, Integer> totalPresentOrderCountMap = new HashMap<>();
        Map<String, Integer> totalPresentRecordCountMap = new HashMap<>();
        for (PresentCount presentCount : presentCounts) {
            Map<String, Integer> presentOrderCountMap = presentCount.getPresentOrderCountMap();
            for (Map.Entry<String, Integer> entry : presentOrderCountMap.entrySet()) {
                String key = entry.getKey();
                if (!totalPresentOrderCountMap.containsKey(key)) {
                    totalPresentOrderCountMap.put(key, 0);
                }
                totalPresentOrderCountMap.put(key, totalPresentOrderCountMap.get(key) + entry.getValue());
            }
            Map<String, Integer> presentRecordCountMap = presentCount.getPresentRecordCountMap();
            for (Map.Entry<String, Integer> entry : presentRecordCountMap.entrySet()) {
                String key = entry.getKey();
                if (!totalPresentRecordCountMap.containsKey(key)) {
                    totalPresentRecordCountMap.put(key, 0);
                }
                totalPresentRecordCountMap.put(key, totalPresentRecordCountMap.get(key) + entry.getValue());
            }
        }
        return new PresentCount(totalPresentOrderCountMap, totalPresentRecordCountMap);
    }

    class PresentOrderMonitorTask implements Callable<PresentCount> {

        private String tableNo;
        private String startPayTime;
        private String endPayTime;

        public PresentOrderMonitorTask(String tableNo, String startPayTime, String endPayTime) {
            this.tableNo = tableNo;
            this.startPayTime = startPayTime;
            this.endPayTime = endPayTime;
        }

        @Override
        public PresentCount call() {
            List<PresentOrder> presentOrders = vipPresentDao.selectRecentPresentOrders(tableNo, startPayTime, endPayTime);
            List<PresentRecord> presentRecords = vipPresentDao.selectRecentPresentRecords(tableNo, startPayTime, endPayTime);
            Map<String, Integer> presentOrderCountMap = PresentMetricHandler.reportPresentOrderData(presentOrders);
            Map<String, Integer> presentRecordCountMap = PresentMetricHandler.reportPresentRecordData(presentRecords);
            return new PresentCount(presentOrderCountMap, presentRecordCountMap);
        }
    }

}
