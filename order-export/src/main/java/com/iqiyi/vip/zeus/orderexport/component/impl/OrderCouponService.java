package com.iqiyi.vip.zeus.orderexport.component.impl;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.persist.dao.coupon.CouponUsedDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.TidbOrderDao;

/**
 * @Author: Lin Peihui
 * @Date: 2022/2/23
 */
@Component
@Profile("!sg")
public class OrderCouponService {

    @Resource
    private CouponUsedDao couponUsedDao;
    @Resource
    private TidbOrderDao tidbOrderDao;

    public boolean isCouponReused(OrderDto order, boolean isTest) {
        if (order.getCouponFee() == null || order.getCouponFee() <= 0) {
            return false;
        }
        //过滤非支付的订单
        if (order.getStatus() != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()
            && order.getStatus() != OrderStatusEnum.PRE_PAID_NO_RIGHTS.getStatus()) {
            return false;
        }
        //过滤权益开通的订单消息
        if (StringUtils.isNotBlank(order.getNotifyResult())) {
            return false;
        }

        String couponCode = couponUsedDao.getCouponCodeByOrderCode(order.getOrderCode());
        if (StringUtils.isBlank(couponCode)) {
            return false;
        }

        List<String> orderCodeList = couponUsedDao.getOrderCodeByCouponCode(couponCode);
        List<OrderDto> orderList = tidbOrderDao.getByOrderCodeList(orderCodeList);
        if (isTest) {
            orderList = new ArrayList<>();
            OrderDto orderDto1 = OrderDto.builder().status(1).build();
            OrderDto orderDto2 = OrderDto.builder().status(1).build();
            orderList.add(orderDto1);
            orderList.add(orderDto2);
        }
        if (CollectionUtils.isEmpty(orderList)) {
            return false;
        }
        int paidCount = 0;
        for (OrderDto o : orderList) {
            if (o.getStatus() == OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()
                || o.getStatus() == OrderStatusEnum.PRE_PAID_NO_RIGHTS.getStatus()) {
                paidCount++;
                if (paidCount >= 2) {
                    return true;
                }
            }
        }
        return false;
    }
}
