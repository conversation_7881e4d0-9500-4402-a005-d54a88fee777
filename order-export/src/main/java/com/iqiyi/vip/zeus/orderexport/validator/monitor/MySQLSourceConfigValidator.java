package com.iqiyi.vip.zeus.orderexport.validator.monitor;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MetricTemplateType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.service.DatasourceSchemaService;
import com.iqiyi.vip.zeus.orderexport.param.AlertRuleCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.param.MonitorCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.param.TianyanMonitorSaveParam;

/**
 * @author: guojing
 * @date: 2023/12/18 14:45
 */
@Profile("!sg")
@Component
public class MySQLSourceConfigValidator extends AbstractMonitorConfigValidator {

    @Resource
    private DatasourceSchemaService datasourceSchemaService;

    @Override
    protected DataSourceType getDataSourceType() {
        return DataSourceType.MySQL;
    }

    @Override
    public void checkConfig(MonitorCreateOrUpdateParam createOrUpdateParam) {
        List<ZeusMonitorQuery> monitorQueryList = createOrUpdateParam.getQuery();
        if (monitorQueryList.size() != 1) {
            throw BizException.newParamException("MySQL数据源类型的监控只支持配置1个指标");
        }
        ZeusMonitorQuery monitorQuery = monitorQueryList.get(0);
        MetricTemplate metricTemplate = checkSingleQuery(monitorQuery);
        if (metricTemplate.isNeedTimeFilter()) {
            boolean datetimeColumn = datasourceSchemaService.datetimeColumn(createOrUpdateParam.getDatasourceId(),
                monitorQuery.getSource(), monitorQuery.getTimeFilter());
            if (!datetimeColumn) {
                throw BizException.newParamException("当前时间范围字段不是日期类型，请重新选择");
            }
        }
        MetricTemplateType metricTemplateType = MetricTemplateType.parseValue(metricTemplate.getType());
        AlertRuleCreateOrUpdateParam alertRuleCreateOrUpdateParam = createOrUpdateParam.getAlertRuleParam();
        if (metricTemplateType.nonsupportAlertRule() && alertRuleCreateOrUpdateParam != null) {
            throw BizException.newParamException("当前指标模版不支持配置告警规则");
        }
        if (alertRuleCreateOrUpdateParam != null && BooleanUtils.isTrue(alertRuleCreateOrUpdateParam.getEnableSmartAlert())) {
            throw BizException.newParamException("当前数据源指不支持配置智能告警");
        }
    }

    @Override
    public void checkConfig(TianyanMonitorSaveParam createOrUpdateParam) {

    }
}
