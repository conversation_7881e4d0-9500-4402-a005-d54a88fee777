package com.iqiyi.vip.zeus.orderexport.exception;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/22 11:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderServiceException extends RuntimeException {
    private String errorCode;
    protected String errorMsg;
    protected String name;

    public OrderServiceException(String errorCode, String errorMsg, String name) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.name = name;
    }

    @Override
    public String toString() {
        return "OrderServiceException{" +
                "errorCode='" + errorCode + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}