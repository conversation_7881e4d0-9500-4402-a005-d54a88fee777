package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Bundle;
import com.iqiyi.vip.zeus.orderexport.entity.PayChannelMarketing;

/**
 * <AUTHOR>
 * @date 2024/4/10 9:09
 */
public interface PayChannelMarketingDao {
    @Select("select * from qiyue_pay_channel_marketing_activity "
        + "where valid_start_time<now() "
        + "and valid_end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<PayChannelMarketing> queryExpiringData(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

}
