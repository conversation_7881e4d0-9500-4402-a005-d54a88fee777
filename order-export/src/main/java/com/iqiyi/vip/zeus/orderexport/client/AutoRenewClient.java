package com.iqiyi.vip.zeus.orderexport.client;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.kit.http.client.util.HttpClients;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.client.resp.UserAgreementSimpleInfoResult;
import com.iqiyi.vip.zeus.orderexport.util.SignUtil;

/**
 * @author: guojing
 * @date: 2025/3/25 19:21
 */
@Slf4j
@Component
public class AutoRenewClient {

    //自动续费开通状态
    private static final int AUTO_RENEW_STATUS_OF_OPEN = 1;

    private static final String USER_AGREEMENT_LIST_URL = "/services/autorenew/agreement/list";

    @Value("${autorenew.server.domain:http://autorenew-api-online}")
    private String autoRenewServerDomain;
    @Value("${autorenew.server.signKey:PjuWj4RzHyrAac}")
    private String autoRenewServerSignKey;

    @Resource
    private RestTemplate autoRenewRestTemplate;

    public List<UserAgreementSimpleInfoResult> getUserAutoRenewAgreementList(Long uid) {
        String requestUrl = autoRenewServerDomain + USER_AGREEMENT_LIST_URL;
        Map<String, Object> params = new HashMap<>();
        params.put("uid", uid);
        params.put("agreementStatusList", AUTO_RENEW_STATUS_OF_OPEN);
        params.put("_channel", "vip-xuanwu");
        String sign = SignUtil.generateSignWithObjValue(params, autoRenewServerSignKey);
        params.put("sign", sign);
        String urlWithParam = HttpClients.buildQueryUrl(requestUrl, params);
        ResponseEntity<BaseResponse<List<UserAgreementSimpleInfoResult>>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            responseEntity = autoRenewRestTemplate.exchange(urlWithParam, HttpMethod.GET, null,
                new ParameterizedTypeReference<BaseResponse<List<UserAgreementSimpleInfoResult>>>() {});
        } catch (RestClientException e) {
            log.error("请求自动续费查询用户协议列表接口失败，url: {}, cost: {}", urlWithParam, stopWatch.getTime(), e);
            return null;
        }
        BaseResponse<List<UserAgreementSimpleInfoResult>> responseBody = responseEntity.getBody();
        log.info("请求自动续费查询用户协议列表接口成功，url: {}, cost: {}, result: {}", urlWithParam, stopWatch.getTime(), JacksonUtils.toJsonString(responseBody));
        return responseBody != null && responseBody.success() ? responseBody.getData() : null;
    }


}
