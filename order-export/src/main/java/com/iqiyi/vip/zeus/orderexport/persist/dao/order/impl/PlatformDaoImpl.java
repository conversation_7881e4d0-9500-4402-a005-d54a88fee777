package com.iqiyi.vip.zeus.orderexport.persist.dao.order.impl;

import com.iqiyi.vip.zeus.orderexport.entity.Platform;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.PlatformDao;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * Created at: 2021-09-13
 *
 * <AUTHOR>
 */
@Repository
public class PlatformDaoImpl implements PlatformDao {

    @Resource
    private SqlSessionTemplate orderSqlSessionTemplate;

    @Cacheable(value = "Platform", key = "'getById_' + #id")
    @Override
    public Platform getById(Long id) {
        return orderSqlSessionTemplate.getMapper(PlatformDao.class).getById(id);
    }

    @Cacheable(value = "Platform", key = "'getByCode_' + #code")
    @Override
    public Platform getByCode(String code) {
        return orderSqlSessionTemplate.getMapper(PlatformDao.class).getByCode(code);
    }

}
