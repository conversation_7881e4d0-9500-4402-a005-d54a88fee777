package com.iqiyi.vip.zeus.orderexport.client.resp;

import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
public class AccountBindInfo {

    private Long uid;
    /**
     * 绑定状态 0：新建 1；已绑定 2：已解绑
     */
    private Integer status;
    /**
     * 绑定的第三方类型
     */
    private Integer type;
    /**
     * 签约协议号，对应支付网关接口里的扩展参数subParam_contractNo
     */
    private String signCode;
    /**
     * 第三方用户id
     */
    private String thirdUserId;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 微信补充签约关系标识
     */
    private String signFlag;

    public boolean isBind() {
        return status != null && 1 == status;
    }

    public boolean isNotBind() {
        return !isBind();
    }

}
