package com.iqiyi.vip.zeus.orderexport.persist.dao.present;

import com.iqiyi.vip.zeus.orderexport.entity.PresentOrder;
import com.iqiyi.vip.zeus.orderexport.entity.PresentRecord;

import java.util.List;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
public interface VipPresentDao {

    /**
     * 查询支付时间在payTime之后的记录
     * @param tableNo
     * @param startPayTime 格式：yyyy-MM-dd HH:mm，包含
     * @param endPayTime 格式：yyyy-MM-dd HH:mm 不包含
     */
    List<PresentOrder> selectRecentPresentOrders(String tableNo, String startPayTime, String endPayTime);

    /**
     * 查询指定时间范围内的赠送订单领取量
     * @param tableNo
     * @param startTime 格式：yyyy-MM-dd HH:mm，包含
     * @param endTime 格式：yyyy-MM-dd HH:mm，不包含
     */
    List<PresentRecord> selectRecentPresentRecords(String tableNo, String startTime, String endTime);

}
