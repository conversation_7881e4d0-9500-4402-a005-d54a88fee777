package com.iqiyi.vip.zeus.orderexport.constant;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/23
 */
public class MQConstant {

    /**
     * 延迟消息
     */
    public static String TRADE_ORDER_MONITOR_DELAY_PRODUCER_GROUP = "PG-vip_trade_order_monitor_delay";
    public static String TRADE_ORDER_MONITOR_DELAY_CONSUMER_GROUP = "CG-vip_trade_order_monitor_delay";
    public static String TRADE_ORDER_MONITOR_DELAY_TOPIC = "vip_trade_order_monitor_delay";

    /**
     * mysql order binlog 订单binlog变更消息
     */
    public static String MYSQL_ORDER_BINLOG_CONSUMER_GROUP = "CG-vip_xuanwu_order_monitor";
    public static String MYSQL_ORDER_BINLOG_TOPIC = "vip_trade_mysql_order_binlog";

    /**
     * 订单已支付消息
     */
    public static String TRADE_ORDER_PAID_CONSUMER_GROUP = "CG-vip_xuanwu_order_paid";
    public static String TRADE_ORDER_PAID_TOPIC = "vip_trade_msg_order_paid";

    /**
     * 订单签约和代扣消息
     */
    public static String TRADE_ORDER_SIGN_PAID_CONSUMER_GROUP = "CG-vip_xuanwu_order_sign_paid";
    public static String TRADE_ORDER_SIGN_PAID_TOPIC = "vip_trade_msg_order_sign_paid";

    /**
     * 订单完成消息
     */
    public static String TRADE_ORDER_FINISHED_CONSUMER_GROUP = "CG-vip_xuanwu_order_finished";
    public static String TRADE_ORDER_FINISHED_TOPIC = "vip_trade_msg_order_finished";

    /**
     * 自动续费异步任务binlog消息
     */
    public static String AUTORENEW_ASYNC_TASK_BINLOG_TOPIC = "viptrade_autorenew_asynctask_binlog";
    public static String AUTORENEW_ASYNC_TASK_BINLOG_CONSUMER_GROUP = "CG-xuanwu_autorenew_asynctask_binlog";
    /**
     * 自动续费延迟任务消息
     */
    public static String AUTORENEW_ASYNC_TASK_TOPIC = "viptrade_autorenew_asynctask";
    public static String AUTORENEW_ASYNC_TASK_CONSUMER_GROUP = "CG-xuanwu_autorenew_asynctask";
    /**
     * 自动续费binlog消息
     */
    public static String AUTORENEW_BINLOG_TOPIC = "viptrade_autorenew_binlog";
    public static String AUTORENEW_BINLOG_CONSUMER_GROUP = "CG-xuanwu_autorenew_binlog";

}
