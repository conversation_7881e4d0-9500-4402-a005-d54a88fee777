package com.iqiyi.vip.zeus.orderexport.component;

import io.micrometer.core.instrument.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.iqiyi.vip.zeus.core.utils.BizDateUtil;
import com.iqiyi.vip.zeus.orderexport.component.impl.AgreementInfoService;
import com.iqiyi.vip.zeus.orderexport.constant.AgreementConstants;

/**
 * @author: guojing
 * @date: 2024/11/28 21:15
 */
@Slf4j
@Component
public class OrderVipTypeNotMatchComponent {

    public static final String ORDER_VIP_TYPE_NOT_MATCH_METRIC = "viptrade_order_vipType_not_match";

    public static final String ORDER_VIP_TYPE_NOT_MATCH_KEY_PREFIX = ORDER_VIP_TYPE_NOT_MATCH_METRIC + ":";

    @Value("${order.vipType.not.match.threshold:20}")
    private Integer orderVipTypeNotMatchThreshold;

    @Resource
    private AgreementInfoService agreementInfoService;
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource
    private AlterService alterService;

    public void check(Map<String, String> orderMap) {
        Integer autoRenew = MapUtils.getInteger(orderMap, "autoRenew");
        Integer renewType = MapUtils.getInteger(orderMap, "renewType");
        Integer payChannel = MapUtils.getInteger(orderMap, "payChannel");
        Long vipType = MapUtils.getLong(orderMap, "vipType");
        String orderName = MapUtils.getString(orderMap, "name");
        if (autoRenew == null || (autoRenew != 1 && autoRenew != 3) || vipType == null || renewType == null) {
            return;
        }

        Long vipTypeOfAgreement = renewType > AgreementConstants.INTI_AGREEMENT_NO
            ? agreementInfoService.getVipTypeByAgreementNo(renewType)
            : agreementInfoService.getVipTypeByDutType(renewType);
        if (Objects.equals(vipTypeOfAgreement, vipType)) {
            return;
        }
        try {
            Metrics.counter(ORDER_VIP_TYPE_NOT_MATCH_METRIC).increment();
        } catch (Exception e) {
            log.error("metric:{} increment error", ORDER_VIP_TYPE_NOT_MATCH_METRIC, e);
        }
        String orderCode = MapUtils.getString(orderMap, "orderCode");
        String alterContext = String.format("订单名称:%s, 订单号:%s, 支付渠道:%s, 产品所属会员类型:%s, 协议所属会员类型:%s, 订单renewType:%s", orderName, orderCode, payChannel, vipType, vipTypeOfAgreement, renewType);
        log.info("{}, {}", ORDER_VIP_TYPE_NOT_MATCH_METRIC, alterContext);

        String redisKey = ORDER_VIP_TYPE_NOT_MATCH_KEY_PREFIX + BizDateUtil.yyyyMMddHHmmssString(new Date());
        Long notMatchCountLong = redisTemplate.opsForValue().increment(redisKey);
        if (Objects.isNull(notMatchCountLong)) {
            return;
        }
        int notMatchCount = notMatchCountLong.intValue();
        if (notMatchCount == 1) {
            redisTemplate.expire(redisKey, 3, TimeUnit.MINUTES);
        }
        if (notMatchCount < orderVipTypeNotMatchThreshold) {
            alterService.sendHotChat("订单上的商品会员类型跟协议所属会员类型不一致", alterContext, "");
        }
    }

    // 每分钟执行
    @Scheduled(cron = "0 0/1 * * * ?")
    public void aggregateAlerts() {
        Timestamp timestamp = BizDateUtil.plusMinutes(Timestamp.from(Instant.now()), -1);
        String redisKeySuffix = BizDateUtil.yyyyMMddHHmmssString(timestamp);
        String redisKey = ORDER_VIP_TYPE_NOT_MATCH_KEY_PREFIX + redisKeySuffix;
        // 获取当前计数并设置为0
        Integer count = redisTemplate.opsForValue().getAndSet(redisKey, 0);
        if (count == null) {
            count = 0;
        }

        // 如果计数超过阈值，则发送聚合告警
        if (count > orderVipTypeNotMatchThreshold) {
            String alterContext = String.format("%s: 不一致个数: %d", BizDateUtil.datetime2String(timestamp), count);
            alterService.sendHotChat("订单上的商品会员类型跟协议所属会员类型不一致", alterContext, "");
        }
    }

}
