package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/4/8 21:56
 */
@Data
public class Payment {
    private Integer id;
    private String name;
    private Timestamp validStartTime;
    private Timestamp validEndTime;
    private String storeCode;
    private String skuId;
    private String version;
    private Integer createChannel;
    private Integer levelId;
    private Integer priority;
    private Integer payType;
    private Integer sort;
    private Integer recommend;
    private Integer supportSignFree;
    private Timestamp updateTime;
    private String updator;
}
