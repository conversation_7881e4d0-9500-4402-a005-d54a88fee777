package com.iqiyi.vip.zeus.orderexport.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/24
 */
@Getter
@Setter
@ToString
public class VipGiftInfo {
    /**
     * 活动ID
     */
    private String actId;
    /**
     * 赠送时长
     */
    private Integer amount;
    /**
     * 赠送商品code
     */
    private String productCode;

    /**
     * 权益开始时间
     */
    private String startTime;
    /**
     * 权益结束时间
     */
    private String endTime;
    /**
     * 自动续费标志
     */
    private Integer renewalsFlag;

    /**
     * 对应的赠品订单号
     */
    private String orderCode;
}