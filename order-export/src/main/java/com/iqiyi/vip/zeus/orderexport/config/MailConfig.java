package com.iqiyi.vip.zeus.orderexport.config;

import com.iqiyi.vip.zeus.orderexport.util.mail.MailComponent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/8/19 11:42
 */
@Configuration
public class MailConfig {

    @Value("${mail.user.name}")
    private String userName;

    @Value("${mail.user.address}")
    private String from;

    @Value("${mail.user.token}")
    private String token;

    @Bean(name = "mailComponent")
    public MailComponent mailComponent(){
        MailComponent mailComponent = new MailComponent();
        mailComponent.setUserName(userName);
        mailComponent.setFrom(from);
        mailComponent.setToken(token);
        return mailComponent;
    }

}
