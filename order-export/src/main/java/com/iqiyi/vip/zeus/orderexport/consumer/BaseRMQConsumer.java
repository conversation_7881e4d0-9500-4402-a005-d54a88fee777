package com.iqiyi.vip.zeus.orderexport.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;

import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.util.OrderMsgUtils;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/23
 */
@Slf4j
public abstract class BaseRMQConsumer implements MessageListenerConcurrently {

    /**
     * 子类实现具体的消息处理逻辑
     */
    protected abstract ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext);

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        String monitorName = getMonitorName();
        long start = System.currentTimeMillis();
        try {
            //过滤压测订单
            List<MessageExt> filteredMsgList = new ArrayList<>();
            for (MessageExt msg : list) {
                if (!OrderMsgUtils.isPressureOrderMsg(msg)) {
                    filteredMsgList.add(msg);
                }
            }
            if (CollectionUtils.isEmpty(filteredMsgList)) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            return doConsume(filteredMsgList, consumeConcurrentlyContext);
        } finally {
            long cost = System.currentTimeMillis() - start;
            log.info("Consumer {} cost time:{}", monitorName, cost);
        }
    }

    /**
     * 获取监控项名称
     *
     * @return String the metrics name
     */
    private String getMonitorName() {
        return this.getClass().getSimpleName();
    }
}
