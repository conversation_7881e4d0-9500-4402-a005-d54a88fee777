package com.iqiyi.vip.zeus.orderexport.validator.alertrule;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MetricTemplateType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.service.EagleDashboardService;
import com.iqiyi.vip.zeus.core.service.MetricTemplateService;
import com.iqiyi.vip.zeus.core.service.ZeusAlertRuleService;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorService;
import com.iqiyi.vip.zeus.core.utils.TimeOffsetValidator;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.orderexport.param.AlertRuleCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.validator.result.AlertRuleCreateParamCheckResult;
import com.iqiyi.vip.zeus.orderexport.validator.result.AlertRuleUpdateParamCheckResult;

/**
 * @author: guojing
 * @date: 2023/12/27 20:05
 */
@Profile("!sg")
@Component
public class ZeusAlertRuleValidator {

    @Resource
    private ZeusMonitorService zeusMonitorService;
    @Resource
    private ZeusAlertRuleService zeusAlertRuleService;
    @Resource
    private EagleDashboardService eagleDashboardService;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;
    @Resource
    private MetricTemplateService metricTemplateService;

    public void checkAlertRuleParamWhenCreateMonitor(AlertRuleCreateOrUpdateParam createParam) {
        if (createParam.getId() != null) {
            throw BizException.newParamException("无需设置告警规则id");
        }
        if (StringUtils.isNotBlank(createParam.getDuration()) && TimeOffsetValidator.offsetInvalid(createParam.getDuration())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        if (StringUtils.isNotBlank(createParam.getCheckFrequency()) && TimeOffsetValidator.offsetInvalid(createParam.getCheckFrequency())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
    }

    public void checkAlertRuleParamWhenUpdateMonitor(AlertRuleCreateOrUpdateParam updateParam, AuthorityTeamBasic currentUserTeam) {
        if (StringUtils.isNotBlank(updateParam.getDuration()) && TimeOffsetValidator.offsetInvalid(updateParam.getDuration())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        if (StringUtils.isNotBlank(updateParam.getCheckFrequency()) && TimeOffsetValidator.offsetInvalid(updateParam.getCheckFrequency())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        ZeusAlertRule zeusAlertRuleFromDB = zeusAlertRuleService.getById(updateParam.getId());
        if (zeusAlertRuleFromDB == null) {
            return;
        }
        if (!zeusAlertRuleFromDB.getEagleUid().equals(updateParam.getEagleUid())) {
            throw BizException.newParamException("告警规则对应的鹰眼告警规则UID不能修改");
        }
        if (!Objects.equals(currentUserTeam.getTeamCode(), zeusAlertRuleFromDB.getTeamCode())) {
            throw BizException.newParamException("仅可更新自己所在团队下的告警规则");
        }
    }

    public AlertRuleCreateParamCheckResult checkCreateParam(AlertRuleCreateOrUpdateParam createParam) {
        if (createParam.getId() != null) {
            throw BizException.newParamException("无需设置告警规则id");
        }
        if (StringUtils.isNotBlank(createParam.getDuration()) && TimeOffsetValidator.offsetInvalid(createParam.getDuration())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        if (StringUtils.isNotBlank(createParam.getCheckFrequency()) && TimeOffsetValidator.offsetInvalid(createParam.getCheckFrequency())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        ZeusMonitor monitorFromDB = zeusMonitorService.getById(createParam.getMonitorId());
        if (monitorFromDB == null) {
            throw BizException.newParamException("监控不存在");
        }
        ZeusDatasource zeusDatasource = zeusDatasourceService.getById(monitorFromDB.getDatasourceId());
        DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
        if (dataSourceType == DataSourceType.MySQL) {
            ZeusMonitorQuery monitorQuery = monitorFromDB.getQuery().get(0);
            MetricTemplate metricTemplate = metricTemplateService.getById(monitorQuery.getMetricTmpId());
            MetricTemplateType metricTemplateType = MetricTemplateType.parseValue(metricTemplate.getType());
            if (metricTemplateType.nonsupportAlertRule()) {
                throw BizException.newParamException("该指标模版不支持配置告警规则");
            }
        }
        DashboardWithMeta dashboardWithMeta = null;
        if (StringUtils.isNotBlank(monitorFromDB.getDashboardUid())) {
            dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(monitorFromDB.getDashboardUid());
            if (dashboardWithMeta == null) {
                throw BizException.newParamException("监控关联的Dashboard不存在");
            }
        }
        return new AlertRuleCreateParamCheckResult(monitorFromDB, dashboardWithMeta);
    }

    public AlertRuleUpdateParamCheckResult checkUpdateParam(AlertRuleCreateOrUpdateParam updateParam, AuthorityTeamBasic currentUserTeam) {
        if (updateParam.getId() == null) {
            throw BizException.newParamException("告警规则id不能为空");
        }
        if (StringUtils.isNotBlank(updateParam.getDuration()) && TimeOffsetValidator.offsetInvalid(updateParam.getDuration())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        if (StringUtils.isNotBlank(updateParam.getCheckFrequency()) && TimeOffsetValidator.offsetInvalid(updateParam.getCheckFrequency())) {
            throw BizException.newParamException("持续时间取值不合法");
        }
        ZeusMonitor zeusMonitor = zeusMonitorService.getById(updateParam.getMonitorId());
        if (zeusMonitor == null) {
            throw BizException.newParamException("监控不存在");
        }
        ZeusAlertRule zeusAlertRuleFromDB = zeusAlertRuleService.getById(updateParam.getId());
        if (!zeusAlertRuleFromDB.getEagleUid().equals(updateParam.getEagleUid())) {
            throw BizException.newParamException("告警规则对应的鹰眼告警规则不能修改");
        }
        if (!Objects.equals(currentUserTeam.getTeamCode(), zeusAlertRuleFromDB.getTeamCode())) {
            throw BizException.newParamException("仅可更新自己所在团队下的告警规则");
        }
        ZeusDatasource zeusDatasource = zeusDatasourceService.getById(zeusMonitor.getDatasourceId());
        DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
        if (dataSourceType == DataSourceType.MySQL) {
            ZeusMonitorQuery monitorQuery = zeusMonitor.getQuery().get(0);
            MetricTemplate metricTemplate = metricTemplateService.getById(monitorQuery.getMetricTmpId());
            MetricTemplateType metricTemplateType = MetricTemplateType.parseValue(metricTemplate.getType());
            if (metricTemplateType.nonsupportAlertRule()) {
                throw BizException.newParamException("该指标模版不支持配置告警规则");
            }
        }
        DashboardWithMeta dashboardWithMeta = null;
        if (StringUtils.isNotBlank(zeusMonitor.getDashboardUid())) {
            dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(zeusMonitor.getDashboardUid());
            if (dashboardWithMeta == null) {
                throw BizException.newParamException("监控关联的Dashboard不存在");
            }
        }
        return new AlertRuleUpdateParamCheckResult(zeusMonitor, dashboardWithMeta);
    }

}
