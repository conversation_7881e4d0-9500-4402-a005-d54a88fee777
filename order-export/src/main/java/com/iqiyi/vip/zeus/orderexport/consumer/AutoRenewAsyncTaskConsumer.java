package com.iqiyi.vip.zeus.orderexport.consumer;

import com.iqiyi.vip.zeus.orderexport.handler.AutoRenewMetricHandler;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 自动续费延迟任务执行中的监控
 * Created at: 2021-09-13
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AutoRenewAsyncTaskConsumer extends BaseRMQConsumer {

    @Resource
    private AutoRenewMetricHandler autoRenewMetricHandler;

    @Override
    protected ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        StopWatch stopWatch = StopWatch.createStarted();
        for (MessageExt messageExt : list) {
            stopWatch.suspend();
            stopWatch.resume();
            String msgBody = null;
            try {
                msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                Map<String, Object> msgMap = JacksonUtils.parseMap(msgBody);
                autoRenewMetricHandler.reportAsyncTaskData("autorenew_async_task_running_total", msgMap);
            } catch (Exception e) {
                log.error("[AutoRenewAsyncTaskConsumer] process message occurred exception. topic:{}, msgId:{}, msgBody:{}", messageExt.getTopic(), messageExt.getMsgId(), msgBody, e);
                continue;
            }
            log.info("[AutoRenewAsyncTaskConsumer] process one message finished. topic:{}, msgId: {}, costTime:{}ms", messageExt.getTopic(), messageExt.getMsgId(), stopWatch.getTime());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
