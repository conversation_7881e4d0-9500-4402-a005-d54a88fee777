package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
@Data
public class PresentOrder {

    private Long uid;

    private String msgId;

    private String orderCode;

    private String presentOrderCode;

    private String presentTradeCode;

    private String refundOrderCode;

    private String buyType;

    private String presentType;

    private Integer status;

    private Timestamp payTime;

    private Timestamp receiveTime;

}
