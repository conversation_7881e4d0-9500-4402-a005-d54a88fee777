package com.iqiyi.vip.zeus.orderexport.persist.dao.promotion;


import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FavorRecordDao {

    List<Long> queryIdsByFavorIdAndFavorDataAndType(@Param("tableName") String tableName, @Param("favorId") Long favorId,
        @Param("favorData") String favorData, @Param("indexDataType") Integer indexDataType,
        @Param("excludedOrderCode") String excludedOrderCode);
}