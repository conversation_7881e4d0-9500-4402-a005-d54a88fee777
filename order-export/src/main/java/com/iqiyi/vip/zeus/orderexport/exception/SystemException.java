package com.iqiyi.vip.zeus.orderexport.exception;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/22 13:57
 */
@AllArgsConstructor
public class SystemException extends RuntimeException {
    protected final String errorCode;
    protected final String errorMsg;

    public static final String SYSTEM_EXCEPTION = "SYSTEM_EXCEPTION";

    @Override
    public String toString() {
        return "SystemException{" +
                "errorCode='" + errorCode + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}