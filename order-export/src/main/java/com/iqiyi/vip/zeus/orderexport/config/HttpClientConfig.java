package com.iqiyi.vip.zeus.orderexport.config;

import com.qiyi.ocm.common.http.HttpClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @Title:
 * @author: pangzihua
 * @date: 2018/10/29 16:40
 */
@Configuration
public class HttpClientConfig {

    @Resource
    @Qualifier("restTemplate")
    RestTemplate restTemplate;

    @Resource
    @Qualifier("lbRestTemplate")
    RestTemplate lbRestTemplate;

    @Bean(name = "ocmHttpClient")
    public HttpClient httpClient() {
        HttpClient httpClient = new HttpClient();
        httpClient.setRestTemplate(restTemplate);
        httpClient.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return httpClient;
    }

    //按form表单提交
    @Bean
    public HttpClient httpFormClient() {
        HttpClient httpClient = new HttpClient();
        httpClient.setRestTemplate(restTemplate);
        httpClient.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return httpClient;
    }

    @LoadBalanced
    @Bean(name = "lbHttpClient")
    public HttpClient lbHttpClient() {
        HttpClient httpClient = new HttpClient();
        httpClient.setRestTemplate(lbRestTemplate);
        httpClient.setContentType(MediaType.APPLICATION_JSON_UTF8);
        return httpClient;
    }

    @Bean(name = "lbHttpFormClient")
    public HttpClient lbHttpFormClient() {
        HttpClient httpClient = new HttpClient();
        httpClient.setRestTemplate(lbRestTemplate);
        httpClient.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return httpClient;
    }

}
