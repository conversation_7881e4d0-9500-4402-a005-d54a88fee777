package com.iqiyi.vip.zeus.orderexport.util.mail;

import lombok.Data;
import org.springframework.core.io.InputStreamSource;

/**
 * 邮件头
 * <AUTHOR>
 * Date: 2018/5/28 Time: 14:22
 */
@Data
public class MailHeader {

    /** 表格默认宽度*/
    public final static int DEFAULT_WIDTH = 900;

    public final static String TITLE_PREFIX = "【重要监控】";

    /**
     * 收件人,to不为空以此参数为准
     */
    private String to;

    /**
     * 收件人列表,to为空取该参数
     */
    private String[] tos;

    /**
     * 邮件标题,不为空
     */
    private String title;


    /**
     * 附件,文件路径,可为空
     */
    private String[] attachments;

    /**
     * 附件文件名
     */
    private String attachmentFileName;
    /**
     * 附件文件流
     */
    private InputStreamSource attachmentSource;

    /**
     * 是否加默认标题前缀：【重要监控】
     */
    private boolean needTitlePrefix = true;

    /**
     * 表格宽度
     */
    private Integer tableWidth;
}

