package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Points;
import com.iqiyi.vip.zeus.orderexport.entity.SmartStore;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PointsDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.SmartStoreDao;

/**
 * <AUTHOR>
 * @date 2024/4/9 21:33
 */
@Repository
@Profile("!sg")
public class SmartStoreDaoImpl implements SmartStoreDao {

    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;
    @Override
    public List<SmartStore> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(SmartStoreDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
