package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.constants.DatasourceConstants;
import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.req.DatasourceSearchParam;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.model.ModelConfigMeta;
import com.iqiyi.vip.zeus.orderexport.param.DatasourceCreateOrUpdateParam;

/**
 * @author: guojing
 * @date: 2023/11/25 16:48
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/datasource")
@Api(tags = "宙斯数据源相关操作接口")
public class DatasourceController {

    @Resource
    private ZeusDatasourceService zeusDatasourceService;
    @Resource
    private ZeusMonitorService zeusMonitorService;

    @ApiOperation(value = "获取所有数据源类型的配置元数据")
    @GetMapping(value = "/configFieldMeta")
    public BaseResponse<List<ModelConfigMeta>> configFieldMeta() {
        List<ModelConfigMeta> result = new ArrayList<>();
        for (DataSourceType type : DataSourceType.values()) {
            result.add(new ModelConfigMeta(type.getValue(), type.getDSFieldConfigMeta()));
        }
        return BaseResponse.createSuccessList(result);
    }

    /**
     * 创建数据源
     * @param request
     * @param createParam
     * @return 数据源id
     */
    @ApiOperation(value = "创建数据源")
    @PostMapping(value = "/create")
    public BaseResponse<Integer> create(HttpServletRequest request, @Validated @RequestBody DatasourceCreateOrUpdateParam createParam) {
        if (createParam.getId() != null) {
            throw BizException.newParamException("无需指定数据源id");
        }
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        if (currentUser.notUnderThisTeam(createParam.getTeamCode())) {
            throw BizException.newParamException("无权在此在团队下创建数据源");
        }
        if (zeusDatasourceService.exist(createParam.getName())) {
            throw BizException.newParamException("数据源已存在，无需重复创建");
        }
        ZeusDatasource createDatasourceData = createParam.toZeusDatasource();
        createDatasourceData.setCreateUser(currentUser.getOaAccount());
        createDatasourceData.setUpdateUser(currentUser.getOaAccount());
        Integer datasourceId = zeusDatasourceService.create(createDatasourceData);
        return BaseResponse.createSuccess(datasourceId);
    }

    /**
     * 更新数据源
     * @param request
     * @param updateParam
     */
    @ApiOperation(value = "更新数据源")
    @PostMapping(value = "/update")
    public BaseResponse<Boolean> update(HttpServletRequest request, @Validated @RequestBody DatasourceCreateOrUpdateParam updateParam) {
        if (updateParam.getId() == null) {
            throw BizException.newParamException("数据源id不能为空");
        }
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        ZeusDatasource zeusDatasourceFromDB = zeusDatasourceService.getById(updateParam.getId());
        if (zeusDatasourceFromDB == null) {
            throw BizException.newParamException("数据源不存在");
        }
        if (Objects.equals(zeusDatasourceFromDB.getName(), DatasourceConstants.DATASOURCE_PROMETHEUS)) {
            throw BizException.newParamException("此数据源不能修改");
        }
        if (!Objects.equals(updateParam.getTeamCode(), zeusDatasourceFromDB.getTeamCode())) {
            throw BizException.newParamException("数据源所属团队不能修改");
        }
        if (!Objects.equals(updateParam.getType(), zeusDatasourceFromDB.getType())) {
            throw BizException.newParamException("数据源类型不能修改");
        }
        ZeusDatasource updateDatasourceData = updateParam.toZeusDatasource();
        updateDatasourceData.setUpdateUser(currentUser.getOaAccount());
        return BaseResponse.createSuccess(zeusDatasourceService.update(updateDatasourceData));
    }

    @ApiOperation(value = "删除数据源")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "数据源id", dataType = "Integer", required = true, paramType = "query")})
    @PostMapping(value = "/delete")
    public BaseResponse<Boolean> delete(HttpServletRequest request, @NotNull(message = "数据源id不能为空") Integer id) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        ZeusDatasource zeusDatasource = zeusDatasourceService.getById(id);
        if (zeusDatasource == null) {
            throw BizException.newParamException("数据源不存在");
        }
        if (Objects.equals(zeusDatasource.getName(), DatasourceConstants.DATASOURCE_PROMETHEUS)) {
            throw BizException.newParamException("此数据源不能删除");
        }
        if (!Objects.equals(zeusDatasource.getTeamCode(), realTeam.getTeamCode())) {
            throw BizException.newParamException("无权操作此数据源");
        }
        if (zeusMonitorService.databaseInUse(id)) {
            throw BizException.newParamException("此数据源被监控使用中，请先删除监控");
        }
        return BaseResponse.createSuccess(zeusDatasourceService.delete(zeusDatasource));
    }

    @ApiOperation(value = "根据id查询数据源详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "数据源id", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getById")
    public BaseResponse<ZeusDatasource> getById(@NotNull(message = "数据源ID不能为空") Integer id) {
        return BaseResponse.createSuccess(zeusDatasourceService.getById(id));
    }

    /**
     * 在当前用户所在团队下搜索数据源
     * @param searchParam
     */
    @ApiOperation(value = "搜索数据源")
    @GetMapping(value = "/search")
    public BaseResponse<List<ZeusDatasource>> search(@Validated DatasourceSearchParam searchParam) {
        return BaseResponse.createSuccessList(zeusDatasourceService.search(searchParam));
    }

}
