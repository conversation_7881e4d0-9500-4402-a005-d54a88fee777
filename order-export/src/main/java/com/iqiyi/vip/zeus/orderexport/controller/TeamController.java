package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.core.service.DevOpsComponent;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: guojing
 * @date: 2023/12/13 17:06
 */
@Profile("!sg")
@Slf4j
@RestController
@RequestMapping("/zeus/team")
@Api(tags = "团队相关接口")
public class TeamController {

    @Resource
    private DevOpsComponent devOpsComponent;

    /**
     * 返回团队信息
     */
    @ApiOperation(value = "返回团队列表")
    @GetMapping(value = "/list")
    public BaseResponse<List<Team>> list() {
        return BaseResponse.createSuccessList(devOpsComponent.getVipRdTeams());
    }

}
