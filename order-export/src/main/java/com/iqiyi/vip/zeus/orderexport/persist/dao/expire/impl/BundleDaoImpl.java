package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Bundle;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.BundleDao;

/**
 * <AUTHOR>
 * @date 2024/4/8 22:12
 */
@Repository
@Profile("!sg")
public class BundleDaoImpl implements BundleDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<Bundle> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(BundleDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
