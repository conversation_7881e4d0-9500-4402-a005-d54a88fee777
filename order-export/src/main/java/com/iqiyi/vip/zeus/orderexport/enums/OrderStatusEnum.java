package com.iqiyi.vip.zeus.orderexport.enums;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/24
 */
public enum OrderStatusEnum {
    ORDER_STATUS_NEW(0, "创建"),
    ORDER_STATUS_DELIVERED(1, "已支付"),
    ORDER_STATUS_REFUND(2, "已退单"),
    ORDER_STATUS_CANCLE(3, "取消"),
    ORDER_STATUS_CLOSE(4, "已关闭"),
    ORDER_STATUS_NEGATIVE(5, "负单"),
    ORDER_STATUS_NEGATIVE_PAID(6, "负单已退款"),
    ORDER_STATUS_PROCESSING(7, "未支付"),
    ORDER_STATUS_CANCEL_WITH_COUPONUNFREEZE(8, "取消订单并且解锁关联的代金券"),
    ORDER_STATUS_APPLE_CANCELSUB(9, "苹果续费取消"),
    PRE_PAID_NO_RIGHTS(10, "已经付款完成，权益未开通"),
    PRE_PAID_NEGATIVE(11, "预付费负单退款中"),
    PRE_PAID_NEGATIVE_PAID(12, "预付费负单已支付");

    private int status;
    private String desc;

    private OrderStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return this.status;
    }

    public String getDesc() {
        return this.desc;
    }
}
