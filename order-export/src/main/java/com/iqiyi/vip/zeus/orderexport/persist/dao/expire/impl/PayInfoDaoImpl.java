package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PayInfoDao;

/**
 * <AUTHOR>
 * @date 2024/4/8 20:51
 */
@Repository
@Profile("!sg")
public class PayInfoDaoImpl implements PayInfoDao {
    @Resource
    private SqlSessionTemplate payinfoSqlSessionTemplate;

    @Override
    public String queryPayTypeName(Integer id) {
        return payinfoSqlSessionTemplate.getMapper(PayInfoDao.class).queryPayTypeName(id);
    }
}
