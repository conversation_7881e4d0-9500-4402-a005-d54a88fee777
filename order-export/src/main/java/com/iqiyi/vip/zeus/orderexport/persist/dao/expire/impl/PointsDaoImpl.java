package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Points;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PointsDao;

/**
 * <AUTHOR>
 * @date 2024/4/8 21:22
 */
@Repository
@Profile("!sg")
public class PointsDaoImpl implements PointsDao {

    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;
    @Override
    public List<Points> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(PointsDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
