package com.iqiyi.vip.zeus.orderexport.persist.dao.order.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringData;
import com.iqiyi.vip.zeus.orderexport.model.ExpiringDataReq;
import com.iqiyi.vip.zeus.orderexport.persist.dao.ExpiringDataDao;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/12
 */
@Repository
@Profile("!sg")
public class OrderExpiringDataDaoImpl implements ExpiringDataDao {

    @Resource
    SqlSessionTemplate orderSqlSessionTemplate;

    @Override
    public List<ExpiringData> queryExpiringData(ExpiringDataReq expiringDataReq) {
        return orderSqlSessionTemplate.getMapper(ExpiringDataDao.class)
            .queryExpiringData(expiringDataReq);
    }
}
