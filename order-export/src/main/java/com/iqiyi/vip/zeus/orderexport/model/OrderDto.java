package com.iqiyi.vip.zeus.orderexport.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.iqiyi.vip.zeus.orderexport.constant.Constant;
import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/24
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderDto {

    public static final Set<Integer> ORDER_FINISH_STATUS = Sets.newHashSet(1, 3, 6, 12);

    public static final Set<Integer> ORDER_PRE_PAID_STATUS = Sets.newHashSet(10);

    public static final Set<Integer> GIFT_ORDER_PAY_TYPE_LIST = Sets.newHashSet(311);

    public static final Set<Integer> SUPPORT_PAY_ORDER_REMIND_PRODUCT_TYPE = Sets.newHashSet(1, 5, 6, 7, 10);

    public static final int ORDER_AUTO_RENEW_FIRST = 1;
    public static final int ORDER_AUTO_RENEW_DUT = 2;
    public static final int ORDER_AUTO_RENEW_DUT_MONTHS = 3;

    public static final int ORDER_CHARGE_TYPE_PAID = 1;
    public static final int ORDER_CHARGE_TYPE_FREE = 2;
    public static final String FROM_CASHER_FLAG = "FromCasher=1";

    public static final String MAIN = "main_";

    private String orderCode;

    private String tradeCode;

    private Integer fee;

    private Integer status;

    private Timestamp createTime;

    private Timestamp modifyTime;

    private Timestamp payTime;

    private Long userId;

    private String accountId;

    private Integer payType;

    private Long platform;

    private Long channel;

    private Long pushChannel;

    private Long gateway;

    private String tradeNo;

    private Timestamp beginTime;

    private Timestamp tradeCreate;

    private Timestamp tradePayment;

    private String refer;

    private String cartCode;

    private String centerCode;

    private Integer renewType;

    private Timestamp updateTime;

    private Integer type;

    private Timestamp validTime;

    private Long serviceId;

    private String serviceOrderNo;

    private String returnUrl;

    private String notifyUrl;

    private String notifyResult;

    private String userIp;

    private Integer couponFee;

    private String fv;

    private Long productId;

    private Integer realFee;

    private Integer productFee;

    private Long contentId;

    private Integer productType;

    private String contentUrl;

    private String pictureUrl;

    private Timestamp deadline;

    private Integer renewalsFlag;

    private Integer amount;

    private Timestamp startTime;

    private String name;

    private Integer originalPrice;

    private String partner;

    private String businessValues;

    private Integer autoRenew;

    private String frVersion;

    private String sendMsgId;

    private Integer couponSettlementFee;

    private Integer settlementFee;

    private String centerPayType;

    private Integer centerPayService;

    private Integer chargeType;

    private String orders;

    private String orderType;

    private String currencyUnit;

    private String skuId;

    private Integer skuAmount;

    /**
     * 获取 refer 字段对应的DTO
     *
     * @return OrderReferDto {@link OrderReferDto}
     */
    public Optional<OrderReferDto> extractReferDto() {
        try {
            return Optional.of(JSON.parseObject(refer, OrderReferDto.class));
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    public Integer getRefundWay() {
        Optional<OrderReferDto> orderReferDto = this.extractReferDto();
        if (orderReferDto.isPresent() && orderReferDto.get().getBusinessProperty() != null) {
            return (Integer) orderReferDto.get().getBusinessProperty().getOrDefault("refundWay", null);
        }
        return null;
    }

    public String getRefundPurpose() {
        Optional<OrderReferDto> orderReferDto = this.extractReferDto();
        if (orderReferDto.isPresent() && orderReferDto.get().getBusinessProperty() != null) {
            return (String) orderReferDto.get().getBusinessProperty().getOrDefault("refundPurpose", "");
        }
        return null;
    }

    public String getActCode() {
        //解析该订单里保存的首次优惠信息，fs值
        Optional<OrderReferDto> orderReferDto = this.extractReferDto();
        String actCode = null;
        if (orderReferDto.isPresent() && StringUtils.isNotBlank(orderReferDto.get().getActCode())) {
            actCode = orderReferDto.get().getActCode();
        }
        return actCode;
    }

    public String getPhoneNum() {
        //解析该订单里保存的首次优惠信息，fs值
        Optional<OrderReferDto> orderReferDto = this.extractReferDto();
        String phoneNUm = null;
        if (orderReferDto.isPresent() && StringUtils.isNotBlank(orderReferDto.get().getPhoneNum())) {
            phoneNUm = orderReferDto.get().getPhoneNum();
        }
        return phoneNUm;
    }

    public String getPayTypeActCode() {
        //解析该订单里保存的首次优惠信息，fs值
        Optional<OrderReferDto> orderReferDto = this.extractReferDto();
        String payTypeActCode = null;
        if (orderReferDto.isPresent() && StringUtils.isNotBlank(orderReferDto.get().getPayTypeActCode())) {
            payTypeActCode = orderReferDto.get().getPayTypeActCode();
        }
        return payTypeActCode;
    }

    public Timestamp getExpireTime() {
        //解析订单里保存的IAP自动续费苹果到期时间
        Optional<OrderReferDto> orderReferDto = this.extractReferDto();
        Timestamp expireTime = null;
        if (orderReferDto.isPresent() && null != orderReferDto.get().getExpireTime()) {
            expireTime = orderReferDto.get().getExpireTime();
        }
        return expireTime;
    }

    private static Set<Integer> getFinishStatus() {
        return ORDER_FINISH_STATUS;
    }

    private static Set<Integer> getPrePaidStatus() {
        return ORDER_PRE_PAID_STATUS;
    }

    /**
     * 判断参数指定的状态是否是支付状态(支付/退款)
     *
     * @param status Integer The order status
     * @return Boolean return ture if the order already paid
     */
    public static boolean isPaidStatus(Integer status) {
        Preconditions.checkNotNull(status, "orderStatus can not be null");
        return getOrderPaidStatus().contains(status);
    }

    public static boolean isPrePaidStatus(Integer status) {
        return getPrePaidStatus().contains(status);
    }

    public static boolean isFinishedStatus(Integer status) {
        return getFinishStatus().contains(status);
    }

    private static List<Integer> getOrderPaidStatus() {
        return Lists.newArrayList(OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus(), OrderStatusEnum.ORDER_STATUS_NEGATIVE_PAID.getStatus(), OrderStatusEnum.PRE_PAID_NEGATIVE_PAID.getStatus());
    }

    public boolean isPaid() {
        return getOrderPaidStatus().contains(status);
    }

    public boolean isGiftOrder() {
        return GIFT_ORDER_PAY_TYPE_LIST.contains(payType);
    }

    public boolean isSupportPayOrderRemindProductType() {
        return SUPPORT_PAY_ORDER_REMIND_PRODUCT_TYPE.contains(productType);
    }

    public boolean isNotGiftOrder() {
        return !isGiftOrder();
    }

    public boolean canSendOrderPaidMsg() {
        return isPaid() && !Constant.COMMON_SUC.equals(notifyResult);
    }

    public boolean orderCanceled() {
        return null != status && status == OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus();
    }

    public boolean canSendPayOrderRemind() {
        return (autoRenew == null || autoRenew != ORDER_AUTO_RENEW_DUT)
                && (chargeType == null || chargeType != ORDER_CHARGE_TYPE_FREE)
                && (type == null || type != -1)
                && StringUtils.isEmpty(partner)
                && isSupportPayOrderRemindProductType()
                && fromCasher();
    }

    public boolean fromCasher() {
        return frVersion != null && frVersion.contains(FROM_CASHER_FLAG);
    }

    /**
     * A00000表示已通知
     * A00001表示订单非法
     * 订单状态非已支付、已取消、负单则不需要通知业务方
     *
     * @return boolean 是否已经成功通知相关业务系统 (eg.开通权益)
     */
    public boolean isNotified() {
        final Integer status = getStatus();
        return "A00000".equals(getNotifyResult())
                || "A00001".equals(getNotifyResult())
                || (status != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()
                && status != OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus()
                && status != OrderStatusEnum.ORDER_STATUS_NEGATIVE.getStatus()
                && status != OrderStatusEnum.ORDER_STATUS_NEGATIVE_PAID.getStatus());
    }

    @JSONField(name = "order_code")
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode;
    }

    @JSONField(name = "trade_code")
    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public void setFee(Integer fee) {
        this.fee = fee;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @JSONField(name = "create_time")
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @JSONField(name = "modify_time")
    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    @JSONField(name = "pay_time")
    public void setPayTime(Timestamp payTime) {
        this.payTime = payTime;
    }

    @JSONField(name = "user_id")
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @JSONField(name = "account_id")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @JSONField(name = "pay_type")
    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    @JSONField(name = "platform")
    public void setPlatform(Long platform) {
        this.platform = platform;
    }

    @JSONField(name = "channel")
    public void setChannel(Long channel) {
        this.channel = channel;
    }

    @JSONField(name = "push_channel")
    public void setPushChannel(Long pushChannel) {
        this.pushChannel = pushChannel;
    }

    @JSONField(name = "gateway")
    public void setGateway(Long gateway) {
        this.gateway = gateway;
    }

    @JSONField(name = "trade_no")
    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    @JSONField(name = "begin_time")
    public void setBeginTime(Timestamp beginTime) {
        this.beginTime = beginTime;
    }

    @JSONField(name = "trade_create")
    public void setTradeCreate(Timestamp tradeCreate) {
        this.tradeCreate = tradeCreate;
    }

    @JSONField(name = "trade_payment")
    public void setTradePayment(Timestamp tradePayment) {
        this.tradePayment = tradePayment;
    }

    @JSONField(name = "refer")
    public void setRefer(String refer) {
        this.refer = refer;
    }

    @JSONField(name = "cart_code")
    public void setCartCode(String cartCode) {
        this.cartCode = cartCode;
    }

    @JSONField(name = "center_code")
    public void setCenterCode(String centerCode) {
        this.centerCode = centerCode;
    }

    @JSONField(name = "renew_type")
    public void setRenewType(Integer renewType) {
        this.renewType = renewType;
    }

    @JSONField(name = "update_time")
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    @JSONField(name = "type")
    public void setType(Integer type) {
        this.type = type;
    }

    @JSONField(name = "valid_time")
    public void setValidTime(Timestamp validTime) {
        this.validTime = validTime;
    }

    @JSONField(name = "service_id")
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    @JSONField(name = "service_order_no")
    public void setServiceOrderNo(String serviceOrderNo) {
        this.serviceOrderNo = serviceOrderNo;
    }

    @JSONField(name = "return_url")
    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    @JSONField(name = "notify_url")
    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    @JSONField(name = "notify_result")
    public void setNotifyResult(String notifyResult) {
        this.notifyResult = notifyResult;
    }

    @JSONField(name = "user_ip")
    public void setUserIp(String userIp) {
        this.userIp = userIp;
    }

    @JSONField(name = "coupon_fee")
    public void setCouponFee(Integer couponFee) {
        this.couponFee = couponFee;
    }

    @JSONField(name = "fv")
    public void setFv(String fv) {
        this.fv = fv;
    }

    @JSONField(name = "product_id")

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    @JSONField(name = "real_fee")
    public void setRealFee(Integer realFee) {
        this.realFee = realFee;
    }

    @JSONField(name = "product_fee")
    public void setProductFee(Integer productFee) {
        this.productFee = productFee;
    }

    @JSONField(name = "content_id")
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    @JSONField(name = "product_type")
    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    @JSONField(name = "content_url")
    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }

    @JSONField(name = "picture_url")
    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    @JSONField(name = "deadline")
    public void setDeadline(Timestamp deadline) {
        this.deadline = deadline;
    }

    @JSONField(name = "renewals_flag")
    public void setRenewalsFlag(Integer renewalsFlag) {
        this.renewalsFlag = renewalsFlag;
    }

    @JSONField(name = "amount")
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    @JSONField(name = "start_time")
    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    @JSONField(name = "name")
    public void setName(String name) {
        this.name = name;
    }

    @JSONField(name = "original_price")
    public void setOriginalPrice(Integer originalPrice) {
        this.originalPrice = originalPrice;
    }

    @JSONField(name = "partner")
    public void setPartner(String partner) {
        this.partner = partner;
    }

    @JSONField(name = "business_values")
    public void setBusinessValues(String businessValues) {
        this.businessValues = businessValues;
    }

    @JSONField(name = "auto_renew")
    public void setAutoRenew(Integer autoRenew) {
        this.autoRenew = autoRenew;
    }

    @JSONField(name = "fr_version")
    public void setFrVersion(String frVersion) {
        this.frVersion = frVersion;
    }

    @JSONField(name = "send_msg_id")
    public void setSendMsgId(String sendMsgId) {
        this.sendMsgId = sendMsgId;
    }

    @JSONField(name = "coupon_settlement_fee")
    public void setCouponSettlementFee(Integer couponSettlementFee) {
        this.couponSettlementFee = couponSettlementFee;
    }

    @JSONField(name = "settlement_fee")
    public void setSettlementFee(Integer settlementFee) {
        this.settlementFee = settlementFee;
    }

    @JSONField(name = "center_pay_type")
    public void setCenterPayType(String centerPayType) {
        this.centerPayType = centerPayType;
    }

    @JSONField(name = "center_pay_service")
    public void setCenterPayService(Integer centerPayService) {
        this.centerPayService = centerPayService;
    }

    @JSONField(name = "charge_type")
    public void setChargeType(Integer chargeType) {
        this.chargeType = chargeType;
    }

    @JSONField(name = "orders")
    public void setOrders(String orders) {
        this.orders = orders;
    }

    @JSONField(name = "order_type")
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    @JSONField(name = "currency_unit")
    public void setCurrencyUnit(String currencyUnit) {
        this.currencyUnit = currencyUnit;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this);
    }

    public String getMainActCode() {
        String orderActCode = this.getActCode();
        if (StringUtils.isBlank(orderActCode)) {
            return "";
        }
        List<String> actCodeList = Splitter.on(",").trimResults().splitToList(orderActCode);
        for (String actCode : actCodeList) {
            if (!actCode.startsWith(MAIN)) {
                continue;
            }
            return actCode.substring(MAIN.length());
        }
        return "";
    }
}