package com.iqiyi.vip.zeus.orderexport.client.resp;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;


/**
 * Created by IntelliJ IDEA.
 *
 * @author: liuwanqiang
 * Date: 2019-1-10
 * Time: 14:44
 */
@Data
public class AccountResponse {

	private List<AccountBindInfo> data;

	private String code;

	private String message;

	public AccountBindInfo getTypeBindInfo(Integer dutType) {
		if (CollectionUtils.isEmpty(data) ) {
			return null;
		}
		return data.stream()
				.filter(bindInfo -> bindInfo.isBind() && bindInfo.getType().equals(dutType))
				.findFirst()
				.orElse(null);

	}

    public boolean alreadyBind(Integer dutType) {
        if (CollectionUtils.isEmpty(data) ) {
            return false;
        }

        return data.stream()
            .anyMatch(accountBindInfo -> accountBindInfo.isBind() && accountBindInfo.getType().equals(dutType));
    }

}
