package com.iqiyi.vip.zeus.orderexport.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created at: 2021-02-20
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class PresentCount {

    private Map<String, Integer> presentOrderCountMap;
    private Map<String, Integer> presentRecordCountMap;

}
