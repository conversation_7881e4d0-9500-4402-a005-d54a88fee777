package com.iqiyi.vip.zeus.orderexport.mysqlio.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.zeus.orderexport.handler.AutoRenewMetricHandler;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AsyncTaskBinlogHandler extends AbstractEventHandler {

    @Resource
    AutoRenewMetricHandler autoRenewMetricHandler;

    @Override
    public boolean accept(MapCanalEvent event) {
        boolean superAccept = super.accept(event);
        if (!superAccept) {
            return false;
        }
        return CanalEventUtil.isInsert(event.getEventType()) || CanalEventUtil.isUpdate(event.getEventType());
    }

    @Override
    public void handCanalEvent(MapCanalEvent event) {
        if (CanalEventUtil.isInsert(event.getEventType())) {
            autoRenewMetricHandler.reportAsyncTaskData("autorenew_async_task_created_total", event.getRowAfter());
        } else {
            Map<String, Object> rowBefore = event.getRowBefore();
            Map<String, Object> rowAfter = event.getRowAfter();
            String beforeInQueue = MapUtils.getString(rowBefore, "inqueue", "0");
            String afterInQueue = MapUtils.getString(rowAfter, "inqueue", "0");
            if (beforeInQueue.equals("0") && afterInQueue.equals("1")) {
                autoRenewMetricHandler.reportAsyncTaskData("autorenew_async_task_running_new_total", event.getRowAfter());
            }
        }
    }

    @Override
    public String getTableName() {
        return "boss_async_task";
    }
}
