package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

import com.iqiyi.vip.zeus.core.enums.MetricTemplateType;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;

/**
 * @author: guojing
 * @date: 2024/1/22 20:44
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("指标模版创建和更新参数类")
public class MetricTemplateCreateOrUpdateParam {

    /**
     * 指标模版id
     */
    @ApiModelProperty(value = "指标模版")
    private Integer id;
    /**
     * 指标模版名称
     */
    @ApiModelProperty(value = "指标模版名称")
    @NotEmpty(message = "指标模版名称不能为空")
    private String name;
    /**
     * 数据源类型
     */
    @ApiModelProperty(value = "数据源类型")
    @NotEmpty(message = "数据源类型不能为空")
    private String datasourceType;
    /**
     * Prometheus指标类型
     */
    @ApiModelProperty(value = "Prometheus指标类型, 取值：Counter、Gauge")
    private String metricType;
    /**
     * 模版内容
     */
    @ApiModelProperty(value = "模版内容")
    @NotEmpty(message = "模版内容不能为空")
    private String content;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    private String description;

    public MetricTemplate toMetricTemplate() {
        MetricTemplateType metricTemplateType = MetricTemplateType.parseFromContext(datasourceType, content);
        return MetricTemplate.builder()
            .id(id)
            .name(name)
            .datasourceType(datasourceType)
            .type(metricTemplateType != null ? metricTemplateType.getValue() : null)
            .metricType(metricType)
            .content(content)
            .description(description)
            .build();
    }

}
