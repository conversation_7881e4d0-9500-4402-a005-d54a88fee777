package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;

/**
 * @author: guojing
 * @date: 2023/12/20 11:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("天眼业务指标监控创建或更新参数模型")
public class TianyanMonitorSaveParam {

    /**
     * 宙斯监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer id;
    @ApiModelProperty(value = "天眼主题名称")
    @NotBlank(message = "天眼主题名称不能为空")
    private String themeTypeName;
    @ApiModelProperty(value = "天眼指标名称")
    @NotBlank(message = "天眼指标名称不能为空")
    private String targetName;
    /**
     * 监控名称
     */
    @ApiModelProperty(value = "监控名称")
    @NotBlank(message = "监控名称不能为空")
    private String name;
    /**
     * 监控查询配置信息
     */
    @Valid
    @ApiModelProperty(value = "监控查询配置信息")
    @NotEmpty(message = "监控查询配置信息不能为空")
    private List<ZeusMonitorQuery> query;
    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
    private Map<String, Object> extraData;

    @ApiModelProperty(value = "智能告警配置")
    private TianyanSmartAlertSaveParam smartAlertParam;

    public ZeusMonitor toZeusMonitor() {
        return ZeusMonitor.builder()
            .id(id)
            .name(name)
            .query(query)
            .extraData(extraData)
            .build();
    }

}
