package com.iqiyi.vip.zeus.orderexport.persist.dao.order.impl;

import com.iqiyi.vip.zeus.orderexport.entity.Product;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.ProductDao;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @Author: <PERSON>
 * @Date: 2020/12/01
 */
@Repository
public class ProductDaoImpl implements ProductDao {
    @Resource
    private SqlSessionTemplate orderSqlSessionTemplate;

    @Override
    public Product getById(Long id) {
        return orderSqlSessionTemplate.getMapper(ProductDao.class).getById(id);
    }

    @Cacheable(value = "ProductNew", key = "'getByCode_' + #code")
    @Override
    public Product getByCode(String code) {
        return orderSqlSessionTemplate.getMapper(ProductDao.class).getByCode(code);
    }
}
