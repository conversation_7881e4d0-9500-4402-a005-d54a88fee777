package com.iqiyi.vip.zeus.orderexport.util;

import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;

/**
 * <AUTHOR>
 * @date 2025/1/17 19:53
 */
public class ProductUtils {
    public static boolean isPureSignProduct(QuerySkuResponse querySkuResponse) {
        if (querySkuResponse == null) {
            return false;
        }

        Integer skuIdentifier = null;
        Integer timeLength = null;

        Map<String, Object> specAttributes = querySkuResponse.getSpecAttributes();
        Object skuIdentifierObj = specAttributes.get("skuIdentifier");
        if (Objects.nonNull(skuIdentifierObj) && !"".equals(skuIdentifierObj)) {
            skuIdentifier =  Integer.parseInt(skuIdentifierObj.toString());
        }
        Object timeLengthObj = specAttributes.get("timeLength");
        if (Objects.nonNull(timeLengthObj) && !"".equals(timeLengthObj)) {
            timeLength =  Integer.parseInt(timeLengthObj.toString());
        }

        return Objects.equals(skuIdentifier, 5) && Objects.equals(timeLength, 0);
    }
}
