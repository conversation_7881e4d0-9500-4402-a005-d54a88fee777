package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.PayTypeMarketing;

/**
 * <AUTHOR>
 * @date 2024/4/10 9:10
 */
public interface PayTypeMarketingDao {
    @Select("select * from qiyue_pay_type_marketing "
        + "where valid_start_time<now() "
        + "and valid_end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<PayTypeMarketing> queryExpiringData(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

}
