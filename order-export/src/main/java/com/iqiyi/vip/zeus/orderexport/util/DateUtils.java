package com.iqiyi.vip.zeus.orderexport.util;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
public class DateUtils {

    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_MINUTE_PATTERN = "yyyy-MM-dd HH:mm";

    public static final String DATE_PATTERN = "yyyy-MM-dd";

    public static Timestamp currentTimestamp() {
        return Timestamp.valueOf(LocalDateTime.now());
    }

    public static Timestamp stringToTimestamp(String datetime) {
        return Timestamp.valueOf(datetime);
    }

    public static Timestamp minusHours(Timestamp timestamp, int hours) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.minusHours(hours);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Timestamp minusMinutes(Timestamp timestamp, int minutes) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.minusMinutes(minutes);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Timestamp minusSeconds(Timestamp timestamp, int seconds) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.minusSeconds(seconds);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String formatNow() {
        return DateFormatUtils.format(new Date(), DATE_TIME_PATTERN);
    }

    public static String formatTimestamp(Timestamp timestamp) {
        return DateFormatUtils.format(timestamp, DATE_TIME_PATTERN);
    }

    public static String formatTimestamp(Timestamp timestamp, String pattern) {
        return DateFormatUtils.format(timestamp, pattern);
    }

    public static Timestamp plusDays(Timestamp timestamp, int days) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(timestamp.toInstant(), ZoneId.systemDefault());
        LocalDateTime newLocalDateTime = localDateTime.plusDays(days);
        return Timestamp.from(newLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static List<String> getDateList(int nDays, String endDate) {
        LocalDate end = LocalDate.parse(endDate);
        return IntStream.range(0, nDays)
            .mapToObj(i -> end.minusDays(i).format(DateTimeFormatter.ISO_LOCAL_DATE))
            .collect(Collectors.toList());
    }

}
