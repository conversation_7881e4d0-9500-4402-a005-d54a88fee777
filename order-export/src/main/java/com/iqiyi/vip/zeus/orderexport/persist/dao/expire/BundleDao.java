package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Bundle;

/**
 * <AUTHOR>
 * @date 2024/4/8 22:11
 */
public interface BundleDao {
    @Select("select * from qiyue_product_bundle "
        + "where valid_start_time<now() "
        + "and valid_end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<Bundle> queryExpiringData(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

}
