package com.iqiyi.vip.zeus.orderexport.mysqlio;


import com.alibaba.fastjson.JSON;

/**
 * @Author: Lin Peihui
 * @Date: 2020/11/24
 */
public class CanalEvent<T> {

    private String schemaName;
    private String tableName;
    private String eventType;
    private String timestamp;
    private T rowBefore;
    private T rowAfter;

    public CanalEvent() {
    }

    public CanalEvent(String schemaName, String tableName, String eventType, String timestamp, T rowBefore, T rowAfter) {
        this.schemaName = schemaName;
        this.tableName = tableName;
        this.eventType = eventType;
        this.timestamp = timestamp;
        this.rowBefore = rowBefore;
        this.rowAfter = rowAfter;
    }

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public T getRowBefore() {
        return rowBefore;
    }

    public void setRowBefore(T rowBefore) {
        this.rowBefore = rowBefore;
    }

    public T getRowAfter() {
        return rowAfter;
    }

    public void setRowAfter(T rowAfter) {
        this.rowAfter = rowAfter;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
