package com.iqiyi.vip.zeus.orderexport.handler;

import com.google.common.collect.Lists;
import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.order.dal.OrderRepository;
import com.iqiyi.vip.order.dal.model.Order;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.zeus.orderexport.client.CommodityClient;
import com.iqiyi.vip.zeus.orderexport.client.VipInfoClient;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.component.OrderVipTypeNotMatchComponent;
import com.iqiyi.vip.zeus.orderexport.constant.Constant;
import com.iqiyi.vip.zeus.orderexport.entity.OrderInfo;
import com.iqiyi.vip.zeus.orderexport.entity.Product;
import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;
import com.iqiyi.vip.zeus.orderexport.enums.ProductChargeTypeEnum;
import com.iqiyi.vip.zeus.orderexport.enums.SkuIdentifierEnum;
import com.iqiyi.vip.zeus.orderexport.enums.TypeEnum;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.ProductDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.refund.RefundDao;
import com.iqiyi.vip.zeus.orderexport.util.DateUtils;
import com.qiyi.vip.commons.component.dto.VipInfo;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * @Author: Lin Peihui
 * @Date: 2022/8/25
 */
@Slf4j
@Component
public class OrderFinishedHandler {

    public static final int PRODUCT_PERIOD_UNIT_DAY = 1;
    public static final int PRODUCT_PERIOD_UNIT_MONTH = 2;
    public static final int PRODUCT_PERIOD_UNIT_HOUR = 3;
    public static final int PRODUCT_PERIOD_UNIT_MINUTE = 4;
    public static final int PRODUCT_PERIOD_UNIT_SECOND = 5;
    public static final int PRODUCT_PERIOD_UNIT_WEEK = 6;
    public static final int YEAR_UNIT = 7;

    @Resource
    private ProductDao productDao;
    @Resource
    private AlterService alterService;
    @Resource
    private CloudConfig cloudConfig;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderVipTypeNotMatchComponent orderVipTypeNotMatchComponent;
    @Resource
    private CommodityClient commodityClient;
    @Resource
    private RefundDao refundDao;
    @Resource
    private VipInfoClient vipInfoClient;

    @Value("${intl.flag:false}")
    private Boolean isIntlEnv;
    @Value("${sourceDeadline.before.targetDeadline.limit.inHours:24}")
    private Integer sourceDeadlineBeforeTargetDeadlineLimitInHours;

    public void handle(Map<String, String> orderMap) {
        try {
            countVipRightTooLong(orderMap);
        } catch (Exception e) {
            log.error("countVipRightTooLong error", e);
        }
        try {
            orderVipTypeNotMatchComponent.check(orderMap);
        } catch (Exception e) {
            log.error("orderVipTypeNotMatchComponent error", e);
        }
        try {
            countFreeUpgradeRightWrong(orderMap);
        } catch (Exception e) {
            log.error("countFreeUpgradeRightWrong error", e);
        }
        try {
            countUpgradeOrderSourceDeadlineBeforeTargetDeadline(orderMap);
        } catch (Exception e) {
            log.error("countSourceVipTypeLessThanTargetVipType error", e);
        }

        try {
            countRealFeeGreaterThanFee(orderMap);
        } catch (Exception e) {
            log.error("countRealFeeGreaterThanFee error", e);
        }

//        countRefundGap(orderMap);
        //TODO 处理国际站的误报
        //countIllegalVipRight(orderMap);
    }

    private void countFreeUpgradeRightWrong(Map<String, String> orderMap) {
        String orderCode = orderMap.get("orderCode");
        String skuId = orderMap.get("skuId");
        int status = Integer.parseInt(orderMap.get("status"));
        if (status != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
            return;
        }
        if (StringUtils.isBlank(skuId)) {
            return;
        }
        Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(skuId);
        if (!skuResponseOptional.isPresent()) {
            log.error("Query commodity by sku is not present. skuId:{}, orderCode:{}", skuId, orderCode);
            return;
        }
        Integer skuIdentifier = null;
        Integer exchangeRateSourceNum = null;  //兑换比例源数量（例如兑换比例2:1，源数量为2）
        Integer exchangeRateTargetNum = null;  //兑换比例目标数量（例如兑换比例2:1，目标数量为1）
        QuerySkuResponse skuResponse = skuResponseOptional.get();
        Map<String, Object> specAttributes = skuResponse.getSpecAttributes();
        if (Objects.nonNull(specAttributes.get("skuIdentifier")) && !"".equals(specAttributes.get("skuIdentifier"))) {
            skuIdentifier = MapUtils.getInteger(specAttributes, "skuIdentifier");
            if (Objects.nonNull(specAttributes.get("exchangeRateSourceNum")) && !"".equals(specAttributes.get("exchangeRateSourceNum"))) {
                exchangeRateSourceNum = MapUtils.getInteger(specAttributes,"exchangeRateSourceNum");
            }
            if (Objects.nonNull(specAttributes.get("exchangeRateTargetNum")) && !"".equals(specAttributes.get("exchangeRateTargetNum"))) {
                exchangeRateTargetNum = MapUtils.getInteger(specAttributes,"exchangeRateTargetNum");
            }
        }
        if (skuIdentifier == null || exchangeRateSourceNum == null || exchangeRateTargetNum == null) {
            log.warn("SkuIdentifier or exchangeRateSourceNum or exchangeRateTargetNum in sku is null. orderCode:{}, skuId:{}", orderCode, skuId);
            return;
        }
        if (!SkuIdentifierEnum.FREE_EXCHANGE_UPGRADE.getValue().equals(skuIdentifier)) {
            return;
        }
        List<String> refundOrderCodeList = refundDao.getRefundOrderCodeByIdempotentCode(orderCode);
        if (CollectionUtils.isEmpty(refundOrderCodeList)) {
            log.error("Refund order code list for free exchange upgrade is null. orderCode:{}, skuId:{}", orderCode, skuId);
            return;
        }

        List<Order> orderList = new ArrayList<>();
        for (String code : refundOrderCodeList) {
            Order order = orderRepository.findByOrderCode(code);
            if (order != null) {
                orderList.add(order);
            }
        }
        if (CollectionUtils.isEmpty(orderList)) {
            log.error("Refund order list for free exchange upgrade is null. orderCode:{}, skuId:{}", orderCode, skuId);
            return;
        }
        long refundDays = calculateTotalDays(orderList);
        long openDays = Long.parseLong(orderMap.get("skuAmount"));
        long needRefundDays = openDays / exchangeRateTargetNum * ((exchangeRateSourceNum - exchangeRateTargetNum));
        Long diff = Math.abs(refundDays + needRefundDays);
        Integer maxDiffDays = cloudConfig.getIntProperty("freeUpgradeRightMaxDiffDays", 1);
        if (diff < maxDiffDays) {
            return;
        }

        Tag orderTag = new ImmutableTag("order", "1");
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(orderTag);
        Metrics.counter("huiyuan_order_free_upgrade_right_wrong", orderTotal).increment();
        log.info("Free upgrade right wrong. orderCode:{}, openDays:{}, refundDays:{}, needRefundDays:{}, diff:{}, sourceNum:{}, targetNum:{}",
            orderCode, openDays, refundDays, needRefundDays, diff, exchangeRateSourceNum, exchangeRateTargetNum);

        Boolean needNotify = cloudConfig.getBooleanProperty("notifyFreeUpgradeRight", false);
        if (needNotify) {
            String alterContext = String.format("orderCode:%s, openDays:%d, refundDays:%d, needRefundDays:%d, diff:%d", orderCode, openDays, refundDays, needRefundDays, diff);
            alterService.sendHotChat("玄武-兑换升级天数与回收天数不一致", alterContext, "");
        }
    }

    private void countUpgradeOrderSourceDeadlineBeforeTargetDeadline(Map<String, String> orderMap) {
        String orderCode = MapUtils.getString(orderMap, "orderCode");
        Long uid = MapUtils.getLong(orderMap, "uid");
        Integer status = MapUtils.getInteger(orderMap, "status");
        Long sourceVipType = MapUtils.getLong(orderMap, "sourceType");
        Long vipType = MapUtils.getLong(orderMap, "productSubtype");
        if (status == null || status != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()
            || sourceVipType == null || vipType == null
            || uid == null) {
            return;
        }

        Map<Integer, VipInfo> userMultiVipInfo = vipInfoClient.getUserMultiVipInfo(uid, Arrays.asList(sourceVipType, vipType));
        if (MapUtils.isEmpty(userMultiVipInfo) || userMultiVipInfo.size() != 2) {
            return;
        }
        VipInfo sourceVipInfo = userMultiVipInfo.get(sourceVipType.intValue());
        VipInfo vipInfo = userMultiVipInfo.get(vipType.intValue());
        if (sourceVipInfo == null || vipInfo == null) {
            return;
        }

        Timestamp sourceDeadline = sourceVipInfo.getDeadlineTS();
        Timestamp targetDeadline = vipInfo.getDeadlineTS();
        long diffInHours = Duration.between(sourceDeadline.toInstant(), targetDeadline.toInstant()).toHours();
        if (diffInHours <= sourceDeadlineBeforeTargetDeadlineLimitInHours) {
            return;
        }
        Tag sourceVipTypeTag = new ImmutableTag("sourceVipType", sourceVipType.toString());
        Tag vipTypeTag = new ImmutableTag("vipType", vipType.toString());
        List<Tag> tags = Arrays.asList(sourceVipTypeTag, vipTypeTag);
        Metrics.counter("huiyuan_upgrade_order_sourceDeadline_before_target", tags).increment();
        String alterContext = String.format("orderCode:%s, userId:%d, sourceVipType:%d, sourceDeadline:%s, targetVipType:%d, targetDeadline:%s",
            orderCode, uid, sourceVipType, DateUtils.formatTimestamp(sourceDeadline), vipType, DateUtils.formatTimestamp(targetDeadline));
//        alterService.sendHotChat("玄武-升级单低等级权益到期时间小于高等级权益到期时间", alterContext, "");
        log.info("upgrade order source deadline before target deadline. {}", alterContext);
    }

    private void countVipRightTooLong(Map<String, String> orderMap) {
        String statusStr = orderMap.get("status");
        Integer status = Integer.valueOf(statusStr);
        String typeStr = orderMap.get("type");
        Integer type = null;
        if (typeStr != null && !"null".equals(typeStr)) {
            type = Integer.valueOf(typeStr);
        }
        if (type != null && (type == TypeEnum.SETTLE.getCode() || type == TypeEnum.WECHAT_PAY_SCORE_COMPLETE.getCode())) {
            return;
        }
        String orderType = orderMap.get("orderType");
        if ("TRADE".equals(orderType)) {
            return;
        }
        Long productId = Long.valueOf(orderMap.get("productId"));
        Product product = productDao.getById(productId);
        if (product.getChargeType() == ProductChargeTypeEnum.PRODUCT_CHARGE_TYP_MARKETING.getValue()) {
            return;
        }
        if (product.isVod()) {
            return;
        }
        String orderCode = orderMap.get("orderCode");
        String startTime = orderMap.get("startTime");
        String deadline = orderMap.get("endTime");
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(deadline)) {
            return;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startLocalDateTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime deadlineLocalDateTime = LocalDateTime.parse(deadline, formatter);

        Integer maxDiffYears = cloudConfig.getIntProperty("timeLength_max_diff_years", 50);
        // 给开始时间增加n年
        LocalDateTime startPlusFiftyYears = startLocalDateTime.plusYears(maxDiffYears);

        if (deadlineLocalDateTime.isAfter(startPlusFiftyYears)) {
            Tag orderTag = new ImmutableTag("order", "1");
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(orderTag);
            Metrics.counter("huiyuan_order_vipRight_too_long", orderTotal).increment();
            String alterContext = String.format("orderCode:%s, startTime:%s, deadline:%s", orderCode, startTime, deadline);
            alterService.sendHotChat("玄武-权益时间过长", alterContext, "");
            log.info("Vip right too long. orderCode:{}, startTime:{}, deadline:{}", orderCode, startTime, deadline);
        }
    }

    private void countRefundGap(Map<String, String> orderMap) {
        String statusStr = orderMap.get("status");
        Integer status = Integer.valueOf(statusStr);
        if (status != OrderStatusEnum.ORDER_STATUS_NEGATIVE_PAID.getStatus()) {
            return;
        }
        String orderCode = orderMap.get("orderCode");
        Order detailedOrder = orderRepository.findByOrderCode(orderCode);
        OrderInfo originOrder = new OrderInfo(detailedOrder);
        if (originOrder.getStartTime() == null || originOrder.getDeadline() == null) {
            return;
        }
        Date now = new Date(System.currentTimeMillis());
        if (originOrder.getStartTime().after(now)) {
            return;
        }
        String startTime = orderMap.get("startTime");
        String deadline = orderMap.get("endTime");
        if (StringUtils.isBlank(startTime) || StringUtils.isBlank(deadline)) {
            return;
        }
        Timestamp orderStartTime = Timestamp.valueOf(startTime);
        Timestamp orderDeadline = Timestamp.valueOf(deadline);
        if (orderStartTime.compareTo(orderDeadline) <= 0) {
            return;
        }
        Long refundDay = getTimeDiff(orderDeadline, orderStartTime);
        if (refundDay == 0) {
            return;
        }
        Long originDay = getTimeDiff(originOrder.getStartTime(), originOrder.getDeadline());
        BigDecimal totalFee = new BigDecimal(originOrder.getRealFee());
        BigDecimal tmpFee = totalFee.divide(BigDecimal.valueOf(originDay), Constant.REFUND_CALCULATE_SCALE, BigDecimal.ROUND_HALF_EVEN);
        BigDecimal refundFee = tmpFee.multiply(BigDecimal.valueOf(refundDay)).setScale(1, BigDecimal.ROUND_HALF_UP);
        Long refundMoney = refundFee.longValue() / 100;

        List<Tag> refundGapCnTotal = Lists.newArrayList();
        Metrics.counter("huiyuan_order_refund_gap_cn", refundGapCnTotal).increment();

        List<Tag> refundGapDayTotal = Lists.newArrayList();
        Metrics.counter("huiyuan_order_refund_gap_day", refundGapDayTotal).increment(refundDay);

        List<Tag> refundGapRealFeeTotal = Lists.newArrayList();
        Metrics.counter("huiyuan_order_refund_gap_realFee", refundGapRealFeeTotal).increment(refundMoney);

        log.info("Refund gap. orderCode:{}", orderCode);
    }

    private void countIllegalVipRight(Map<String, String> orderMap) {
        String statusStr = orderMap.get("status");
        Integer status = Integer.valueOf(statusStr);
        String typeStr = orderMap.get("type");
        Integer type = null;
        if (typeStr != null && !"null".equals(typeStr)) {
            type = Integer.valueOf(typeStr);
        }
        if (type != null && (type == TypeEnum.SETTLE.getCode() || type == TypeEnum.WECHAT_PAY_SCORE_COMPLETE.getCode())) {
            return;
        }
        String orderType = orderMap.get("orderType");
        if ("TRADE".equals(orderType)) {
            return;
        }
        Long productId = Long.valueOf(orderMap.get("productId"));
        Product product = productDao.getById(productId);
        if (product.getChargeType() == ProductChargeTypeEnum.PRODUCT_CHARGE_TYP_MARKETING.getValue()) {
            return;
        }
        String startTime = orderMap.get("startTime");
        String deadline = orderMap.get("endTime");
        Timestamp orderStartTime = Timestamp.valueOf(startTime);
        Timestamp orderDeadline = Timestamp.valueOf(deadline);
        Integer amount = Integer.valueOf(orderMap.get("amount"));
        String orderCode = orderMap.get("orderCode");
        if (product.getPeriod() != null && product.getPeriodUnit() != null) {
            Integer period = product.getPeriod();
            Integer periodUnit = product.getPeriodUnit();
            boolean isVipRightDiff = false;
            if (status == OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
                isVipRightDiff = isStartTimeOrDeadlineIllegal(orderStartTime, orderDeadline, amount, period, periodUnit);
            }
            boolean isIllegalVipRight = isIllegalVipRight(startTime, deadline, status, amount);
            if (isVipRightDiff || isIllegalVipRight) {
                Tag orderTag = new ImmutableTag("order", "1");
                List<Tag> orderTotal = Lists.newArrayList();
                orderTotal.add(orderTag);
                Metrics.counter("huiyuan_order_illegal_vipRight", orderTotal).increment();
                String alterContext = String.format("orderCode:%s, startTime:%s, deadline:%s", orderCode, startTime, deadline);
                alterService.sendHotChat("玄武-权益时间有误", alterContext, "");
                log.info("Illegal start or end time. orderCode:{}", orderCode);
            }
        }
    }

    /**
     * 国际站订单realFee大于Fee监控。
     *
     * @param orderMap
     */
    private void countRealFeeGreaterThanFee(Map<String, String> orderMap) {
        if (!isIntlEnv) {
            return;
        }
        String orderCode = orderMap.get("orderCode");
        Integer status = MapUtils.getInteger(orderMap, "status");
        if (status != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
            return;
        }
        Integer autoRenew = MapUtils.getInteger(orderMap, "autoRenew");
        if (autoRenew != null && autoRenew == 2) {
            return;
        }
        Integer realFee = MapUtils.getInteger(orderMap, "orderRealFee");
        Integer fee = MapUtils.getInteger(orderMap, "orderFee");
        String payType = orderMap.get("payType");
        if (realFee == null || fee == null || StringUtils.isBlank(payType)) {
            log.error("order info exist empty, orderCode:{}, realFee:{}, Fee:{}, payType:{}", orderCode, realFee, fee, payType);
            return;
        }
        if (realFee <= fee) {
            return;
        }

        Tag payTypeTag = new ImmutableTag("payType", payType);
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payTypeTag);
        Metrics.counter("huiyuan_order_realFee_greater_fee", orderTotal).increment();
        log.info("order realFee greater than fee, orderCode:{}, realFee:{}, Fee:{}, payType:{}", orderCode, realFee, fee, payType);
        Boolean needNotify = cloudConfig.getBooleanProperty("realFeeGreaterThanFee_notify", false);
        if (!needNotify) {
            return;
        }
        if (hitPayTypeBlackList(payType)) {
            return;
        }
        String notifyUsers = cloudConfig.getProperty("realFeeGreaterThanFee_notifyUsers", "wangrongkun");
        String alterContext = String.format("orderCode:%s, realFee:%s, fee:%s, payType:%s",
                orderCode, realFee, fee, payType);
        alterService.sendHotChat("玄武-国际站-订单realFee大于fee", alterContext, notifyUsers);
    }

    private boolean hitPayTypeBlackList(String payType) {
        String payTypeBlackList = cloudConfig.getProperty("realFeeGreaterThanFee_payTypeBlackList", "");
        if (StringUtils.isNotBlank(payTypeBlackList)) {
            String[] payTypeList = payTypeBlackList.split(",");
            for (String payTypeItem : payTypeList) {
                if (StringUtils.equals(payType, payTypeItem)) {
                    log.info("order realFee greater than fee, payType {} not need notify", payType);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 权益时间有误
     */
    private boolean isIllegalVipRight(String startTime, String deadline, Integer status, Integer amount) {
        if (startTime == null || deadline == null) {
            return true;
        }
        if (status == OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
            if (startTime.compareTo(deadline) > 0) {
                return true;
            }
        }
        if (status == OrderStatusEnum.ORDER_STATUS_NEGATIVE_PAID.getStatus()) {
            if (amount < 0 && startTime.compareTo(deadline) < 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断订单的startTime,deadline是否通知有误
     */
    private boolean isStartTimeOrDeadlineIllegal(Timestamp startTime, Timestamp deadline, Integer amount, Integer period, Integer periodUnit) {
        if (startTime == null || deadline == null || deadline.before(startTime)) {
            return true;
        }
        // 根据订单中的产品和startTime计算出deadline
        Timestamp calculatedDeadline = calculateOffsetTime(startTime, amount * period, periodUnit);
        long calculatedDeadlineTime = calculatedDeadline.getTime();
        long deadlineTime = deadline.getTime();
        /**
         * 数据库有四舍五入逻辑, 如将4月30号23:59:59:97左右转换为5月1号0点,
         * 实际权益可能是5月1号0点~5月30号0点,比自然月相差1天, 因此此类情况不告警
         */
        if (PRODUCT_PERIOD_UNIT_MONTH == periodUnit) {
            int hours = (int) ((calculatedDeadlineTime - deadlineTime) / (1000 * 60 * 60));
            return hours > 24;
        }
        int minutes = (int) ((calculatedDeadlineTime - deadlineTime) / (1000 * 60));
        // 考虑同产品的买赠场景,实际deadline应该大于或者等于calculatedDeadline,
        // 当实际deadline小于calculatedDeadline, 并有5分钟以上误差时,判断为权益起止时间非法
        return minutes > 5;
    }

    public static Timestamp calculateOffsetTime(Timestamp begin, Integer offset, Integer unit) {

        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(begin.getTime());

        Integer field = Calendar.DAY_OF_MONTH;
        switch (unit.intValue()) {
            case PRODUCT_PERIOD_UNIT_HOUR:
                field = Calendar.HOUR;
                break;
            case PRODUCT_PERIOD_UNIT_DAY:
                field = Calendar.DAY_OF_MONTH;
                break;
            case PRODUCT_PERIOD_UNIT_MONTH:
                field = Calendar.MONTH;
                break;
            case PRODUCT_PERIOD_UNIT_MINUTE:
                field = Calendar.MINUTE;
                break;
            case PRODUCT_PERIOD_UNIT_SECOND:
                field = Calendar.SECOND;
                break;
            case PRODUCT_PERIOD_UNIT_WEEK:
                field = Calendar.WEEK_OF_MONTH;
                break;
            case YEAR_UNIT:
                field = Calendar.YEAR;
                break;
        }
        c.add(field, offset);
        return new Timestamp(c.getTimeInMillis());
    }

    private long getTimeDiff(Date time1, Date time2) {
        long diff = time2.getTime() - time1.getTime();
        long days = diff / (1000 * 60 * 60 * 24);
        if (days < 0) {
            days = 0;
        }
        return days;
    }

    private long calculateTotalDays(List<Order> orderList) {
        return orderList.stream()
            .mapToLong(order -> calculateDaysBetween(order.getStartTime(), order.getDeadline()))
            .sum();
    }

    private long calculateDaysBetween(Timestamp startTime, Timestamp endTime) {
        LocalDate startDate = startTime.toLocalDateTime().toLocalDate();
        LocalDate endDate = endTime.toLocalDateTime().toLocalDate();
        return ChronoUnit.DAYS.between(startDate, endDate);
    }
}
