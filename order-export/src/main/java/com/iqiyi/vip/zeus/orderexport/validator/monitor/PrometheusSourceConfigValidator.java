package com.iqiyi.vip.zeus.orderexport.validator.monitor;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.orderexport.param.AlertRuleCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.param.MonitorCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.param.TianyanMonitorSaveParam;

/**
 * @author: guojing
 * @date: 2023/12/18 14:44
 */
@Profile("!sg")
@Component
public class PrometheusSourceConfigValidator extends AbstractMonitorConfigValidator {

    @Override
    protected DataSourceType getDataSourceType() {
        return DataSourceType.Prometheus;
    }

    @Override
    public void checkConfig(MonitorCreateOrUpdateParam createOrUpdateParam) {
        List<ZeusMonitorQuery> monitorQueryList = createOrUpdateParam.getQuery();
        AlertRuleCreateOrUpdateParam alertRuleCreateOrUpdateParam = createOrUpdateParam.getAlertRuleParam();
        if (alertRuleCreateOrUpdateParam != null && BooleanUtils.toBoolean(alertRuleCreateOrUpdateParam.getEnableSmartAlert())) {
            boolean smartAlertMetricNameEmpty = monitorQueryList.stream()
                .anyMatch(zeusMonitorQuery -> StringUtils.isBlank(zeusMonitorQuery.getSmartAlertMetricName()));
            if (smartAlertMetricNameEmpty) {
                throw BizException.newParamException("智能告警指标名称不能为空");
            }
        }
        for (ZeusMonitorQuery query : monitorQueryList) {
            checkSingleQuery(query);
        }
    }

    @Override
    public void checkConfig(TianyanMonitorSaveParam createOrUpdateParam) {
        List<ZeusMonitorQuery> monitorQueryList = createOrUpdateParam.getQuery();
//        boolean smartAlertMetricNameEmpty = monitorQueryList.stream()
//            .anyMatch(zeusMonitorQuery -> StringUtils.isBlank(zeusMonitorQuery.getSmartAlertMetricName()));
//        if (smartAlertMetricNameEmpty) {
//            throw BizException.newParamException("智能告警指标名称不能为空");
//        }
        for (ZeusMonitorQuery query : monitorQueryList) {
            checkSingleQuery(query);
        }
    }
}
