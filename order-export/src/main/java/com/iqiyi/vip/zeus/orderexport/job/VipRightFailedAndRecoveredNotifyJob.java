package com.iqiyi.vip.zeus.orderexport.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.util.CloudConfigUtil;

import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.LAST_NOTIFY_FAILED_COUNT;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.LAST_NOTIFY_FAILED_TS;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.LAST_NOTIFY_RECOVERED_COUNT;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.LAST_NOTIFY_RECOVERED_TS;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.RIGHT_FAILED_ORDERS_REDIS_KEY;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.RIGHT_FAILED_ORDERS_SET_REDIS_KEY;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.RIGHT_FAILED_TIMESTAMP_KEY;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.RIGHT_RECOVERED_ORDERS_REDIS_KEY;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.RIGHT_RECOVERED_ORDERS_SET_REDIS_KEY;
import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.RIGHT_RECOVERED_TIMESTAMP_KEY;

/**
 * 权益开通失败及恢复的通知
 *
 * @author: linpeihui
 * @createTime: 2025/02/10
 */
@Slf4j
@Component
public class VipRightFailedAndRecoveredNotifyJob extends IJobHandler {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AlterService alterService;

    @Value("${intl.flag:false}")
    private Boolean isIntlEnv;

    @Job("vipRightFailedAndRecoveredNotifyJob")
    @Override
    public void execute() throws Exception {
        // 获取前5条失败和恢复记录
        Set<String> failedOrderIds = stringRedisTemplate.opsForSet().members(RIGHT_FAILED_ORDERS_SET_REDIS_KEY);
        List<String> failedOrderDetails = stringRedisTemplate.opsForList().range(RIGHT_FAILED_ORDERS_REDIS_KEY, 0, -1);

        List<String> filteredFailedOrderDetails = filterFailedOrderDetails(failedOrderDetails, failedOrderIds);
        List<String> recoveredOrderDetails = stringRedisTemplate.opsForList().range(RIGHT_RECOVERED_ORDERS_REDIS_KEY, 0, 4);

        // 获取计数和时间戳
        List<Object> redisResult = executeRedisScriptForCountsAndTimestamps();

        Long failedCount = parseLongFromRedisResult(redisResult.get(0));
        Long recoveredCount = parseLongFromRedisResult(redisResult.get(1));
        Long failedTimestamp = parseTimestampFromRedisResult(redisResult.get(2));
        Long recoveredTimestamp = parseTimestampFromRedisResult(redisResult.get(3));

        if (shouldClearData(failedTimestamp, recoveredTimestamp)) {
            clearRedisData();
            return;
        }

        if (isNoChangeSinceLastNotification(failedCount, recoveredCount, failedTimestamp, recoveredTimestamp)) {
            log.info("No changes detected since last notification. Skip sending.");
            return;
        }

        // 计算恢复进度百分比，并格式化输出
        String recoveryProgressFormatted = calculateRecoveryProgress(failedCount, recoveredCount);

        // 构建通知内容
        String alertContext = buildAlertContext(failedCount, recoveredCount, recoveryProgressFormatted, filteredFailedOrderDetails, recoveredOrderDetails);

        // 发送通知
        sendNotification(alertContext, failedCount, recoveredCount, recoveryProgressFormatted);

        // 更新通知状态
        updateNotificationStatus(failedCount, recoveredCount, failedTimestamp, recoveredTimestamp);
    }

    /**
     * 过滤失败订单详情
     */
    private List<String> filterFailedOrderDetails(List<String> failedOrderDetails, Set<String> failedOrderIds) {
        if (CollectionUtils.isEmpty(failedOrderDetails) || CollectionUtils.isEmpty(failedOrderDetails)) {
            return new ArrayList<>();
        }
        return failedOrderDetails.stream()
            .filter(detail -> failedOrderIds != null && failedOrderIds.stream().anyMatch(detail::startsWith))
            .limit(5)
            .collect(Collectors.toList());
    }

    /**
     * 执行Redis脚本以获取计数和时间戳
     */
    private List<Object> executeRedisScriptForCountsAndTimestamps() {
        //获取失败订单和恢复订单的数量以及它们的时间戳
        String script = "local failedCount = redis.call('scard', KEYS[1]); " +
            "local recoveredCount = redis.call('llen', KEYS[2]); " +
            "local failedTimestamp = redis.call('get', KEYS[3]); " +
            "local recoveredTimestamp = redis.call('get', KEYS[4]); " +
            "return {failedCount, recoveredCount, failedTimestamp, recoveredTimestamp};";

        return stringRedisTemplate.execute((RedisCallback<List<Object>>) connection -> {
            byte[] scriptBytes = script.getBytes(StandardCharsets.UTF_8);
            return connection.eval(scriptBytes, ReturnType.MULTI, 4,
                RIGHT_FAILED_ORDERS_SET_REDIS_KEY.getBytes(),
                RIGHT_RECOVERED_ORDERS_REDIS_KEY.getBytes(),
                RIGHT_FAILED_TIMESTAMP_KEY.getBytes(),
                RIGHT_RECOVERED_TIMESTAMP_KEY.getBytes());
        });
    }

    /**
     * 从Redis结果中解析Long值
     */
    private Long parseLongFromRedisResult(Object result) {
        return result != null ? Long.parseLong(result.toString()) : 0L;
    }

    /**
     * 从Redis结果中解析时间戳
     */
    private Long parseTimestampFromRedisResult(Object result) {
        try {
            return result != null ? Long.parseLong(new String((byte[]) result, StandardCharsets.UTF_8)) : 0L;
        } catch (Exception e) {
            log.error("Failed to parse timestamp from Redis", e);
            return 0L;
        }
    }

    /**
     * 检查是否与上次通知状态一致
     */
    private boolean isNoChangeSinceLastNotification(Long failedCount, Long recoveredCount, Long failedTimestamp, Long recoveredTimestamp) {
        Long lastFailedCount = getLongFromRedis(LAST_NOTIFY_FAILED_COUNT);
        Long lastRecoveredCount = getLongFromRedis(LAST_NOTIFY_RECOVERED_COUNT);
        Long lastFailedTs = getLongFromRedis(LAST_NOTIFY_FAILED_TS);
        Long lastRecoveredTs = getLongFromRedis(LAST_NOTIFY_RECOVERED_TS);

        return Objects.equals(failedCount, lastFailedCount) &&
            Objects.equals(recoveredCount, lastRecoveredCount) &&
            Objects.equals(failedTimestamp, lastFailedTs) &&
            Objects.equals(recoveredTimestamp, lastRecoveredTs);
    }

    /**
     * 检查是否应该清空数据
     */
    private boolean shouldClearData(Long failedTimestamp, Long recoveredTimestamp) {
        Integer waitMinutes = CloudConfigUtil.clearVipRightNotifyWaitMinute();
        Long nMinutesInMillis = waitMinutes * 60 * 1000L;
        Long currentTimeMillis = System.currentTimeMillis();

        return (currentTimeMillis - failedTimestamp > nMinutesInMillis) &&
            (currentTimeMillis - recoveredTimestamp > nMinutesInMillis);
    }

    /**
     * 清空Redis数据
     */
    private void clearRedisData() {
        // 检查 key 是否存在
        Boolean failedOrdersKeyExists = stringRedisTemplate.hasKey(RIGHT_FAILED_ORDERS_REDIS_KEY);
        Boolean recoveredOrdersKeyExists = stringRedisTemplate.hasKey(RIGHT_RECOVERED_ORDERS_REDIS_KEY);
        Boolean failedOrdersSetKeyExists = stringRedisTemplate.hasKey(RIGHT_FAILED_ORDERS_SET_REDIS_KEY);
        Boolean recoveredOrdersSetKeyExists = stringRedisTemplate.hasKey(RIGHT_RECOVERED_ORDERS_SET_REDIS_KEY);
        Boolean failedTimestampKeyExists = stringRedisTemplate.hasKey(RIGHT_FAILED_TIMESTAMP_KEY);
        Boolean recoveredTimestampKeyExists = stringRedisTemplate.hasKey(RIGHT_RECOVERED_TIMESTAMP_KEY);

        // 如果所有 key 都不存在，直接返回
        if (Boolean.FALSE.equals(failedOrdersKeyExists) &&
            Boolean.FALSE.equals(recoveredOrdersKeyExists) &&
            Boolean.FALSE.equals(failedOrdersSetKeyExists) &&
            Boolean.FALSE.equals(recoveredOrdersSetKeyExists) &&
            Boolean.FALSE.equals(failedTimestampKeyExists) &&
            Boolean.FALSE.equals(recoveredTimestampKeyExists)) {
            log.info("All keys already deleted. No need to clear data.");
            return;
        }

        // 清空相关数据
        String clearScript = "redis.call('del', KEYS[1], KEYS[2], KEYS[3], KEYS[4], KEYS[5], KEYS[6]); return 1;";
        stringRedisTemplate.execute((RedisCallback<Long>) connection -> {
            connection.eval(clearScript.getBytes(), ReturnType.INTEGER, 6,
                RIGHT_FAILED_ORDERS_REDIS_KEY.getBytes(),
                RIGHT_RECOVERED_ORDERS_REDIS_KEY.getBytes(),
                RIGHT_FAILED_ORDERS_SET_REDIS_KEY.getBytes(),
                RIGHT_RECOVERED_ORDERS_SET_REDIS_KEY.getBytes(),
                RIGHT_FAILED_TIMESTAMP_KEY.getBytes(),
                RIGHT_RECOVERED_TIMESTAMP_KEY.getBytes());
            return 1L;
        });

        // 重置通知状态为0
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_FAILED_COUNT, "0");
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_RECOVERED_COUNT, "0");
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_FAILED_TS, "0");
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_RECOVERED_TS, "0");

        log.info("Cleared all data and reset notification status.");
    }

    /**
     * 计算恢复进度百分比
     */
    private String calculateRecoveryProgress(Long failedCount, Long recoveredCount) {
        double totalOrders = failedCount + recoveredCount;
        double recoveryProgress = totalOrders > 0 ? (recoveredCount / totalOrders) * 100 : 0.0;
        return (recoveryProgress % 1 == 0) ?
            String.format("%.0f%%", recoveryProgress) :
            String.format("%.2f%%", recoveryProgress);
    }

    /**
     * 构建通知内容
     */
    private String buildAlertContext(Long failedCount, Long recoveredCount, String recoveryProgressFormatted,
        List<String> filteredFailedOrderDetails, List<String> recoveredOrderDetails) {
        StringBuilder alertContext = new StringBuilder(String.format(
            "总订单量: %d\n失败: %d\n已恢复: %d\n恢复进度: %s\n",
            failedCount + recoveredCount,
            failedCount,
            recoveredCount,
            recoveryProgressFormatted
        ));
        if (CollectionUtils.isNotEmpty(filteredFailedOrderDetails)) {
            alertContext.append("失败订单（前5条）：\n");
            for (String detail : filteredFailedOrderDetails) {
                String[] parts = detail.split(":", 2);
                alertContext.append(parts.length > 1 && StringUtils.isBlank(parts[1]) ? parts[0] : detail).append("\n");
            }
        }
        if (recoveredCount > 0 && CollectionUtils.isNotEmpty(recoveredOrderDetails)) {
            alertContext.append("恢复订单（前5条）：\n");
            recoveredOrderDetails.forEach(detail -> alertContext.append(detail).append("\n"));
        }
        return alertContext.toString();
    }

    /**
     * 发送通知
     */
    private void sendNotification(String alertContext, Long failedCount, Long recoveredCount, String recoveryProgressFormatted) {
        // 构建通知标题
        String title = (isIntlEnv ? "玄武-国际站-" : "玄武-") + "订单履约状态通知（失败及恢复）";

        // 如果恢复进度为 100%，在标题后追加 "—已全部恢复！"
        if (StringUtils.isNotBlank(recoveryProgressFormatted) && "100%".equals(recoveryProgressFormatted)) {
            title += "—已全部恢复！";
        }

        // 发送通知
        alterService.sendHotChat(title, alertContext, "");
        log.info("Sent notification. Failed: {}, Recovered: {}", failedCount, recoveredCount);
    }

    /**
     * 更新通知状态
     */
    private void updateNotificationStatus(Long failedCount, Long recoveredCount, Long failedTimestamp, Long recoveredTimestamp) {
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_FAILED_COUNT, failedCount.toString());
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_RECOVERED_COUNT, recoveredCount.toString());
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_FAILED_TS, failedTimestamp.toString());
        stringRedisTemplate.opsForValue().set(LAST_NOTIFY_RECOVERED_TS, recoveredTimestamp.toString());
    }

    /**
     * 辅助方法：从Redis获取Long值
     */
    private Long getLongFromRedis(String key) {
        String val = stringRedisTemplate.opsForValue().get(key);
        return val != null ? Long.parseLong(val) : null;
    }

}
