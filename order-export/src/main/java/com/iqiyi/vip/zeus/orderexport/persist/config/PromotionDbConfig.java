package com.iqiyi.vip.zeus.orderexport.persist.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;

import com.iqiyi.vip.zeus.core.config.AbstractDBConfig;

/**
 * @Author: <PERSON>ei<PERSON>
 * @Date: 2022/08/12
 */
@Configuration
@Slf4j
@Profile("!sg")
public class PromotionDbConfig extends AbstractDBConfig {
    protected final String beanPrefix = "promotion";

    @Resource
    private PromotionResourceConfig promotionResourceConfig;

    @Bean(name = beanPrefix + "DataSource")
    protected DataSource dataSource() {
        return super.dataSource(promotionResourceConfig);
    }

    @Bean(name = beanPrefix + "SqlSessionFactory")
    protected SqlSessionFactory sqlSessionFactory(@Qualifier(beanPrefix + "DataSource") DataSource dataSource)
            throws Exception {
        log.info("数据源启动完成:" + dataSource.getConnection());
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
        sqlSessionFactoryBean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean(name = beanPrefix + "SqlSessionTemplate")
    protected SqlSessionTemplate sqlSessionTemplate(
            @Qualifier(beanPrefix + "SqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);

    }

    @Bean(name = beanPrefix + "TransactionManager")
    protected PlatformTransactionManager transactionManager(
            @Qualifier(beanPrefix + "DataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
