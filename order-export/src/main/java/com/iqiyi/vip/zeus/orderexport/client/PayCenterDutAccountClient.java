package com.iqiyi.vip.zeus.orderexport.client;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;

import com.qiyi.vip.commons.util.SignUtil;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.orderexport.client.resp.AccountResponse;
import com.iqiyi.vip.zeus.orderexport.constant.Constant;

/**
 * @author: guojing
 * @date: 2025/3/25 19:21
 */
@Slf4j
@Component
public class PayCenterDutAccountClient {

    private static final String DUT_QUERY_URL = "/pay/dut/query.action";

    @Value("${pay.center.dut.account.domain:http://inter.account.qiyi.domain}")
    private String payCenterDutAccountDomain;
    @Value("${pay.center.dut.account.signKey:707bae914efa48cb87f1c505c0a26c1a}")
    private String payCenterDutAccountSignKey;

    @Resource
    private RestTemplate payCenterDutAccountRestTemplate;

    public AccountResponse queryBindInfo(Long userId, Integer dutType) {
        Map<String, String> configs = Maps.newHashMap();
        configs.put("uid", String.valueOf(userId));
        if (dutType != null) {
            configs.put("type", String.valueOf(dutType));
        }
        configs.put("version", "1.0");
        configs.put("sign", SignUtil.getSign(configs, payCenterDutAccountSignKey));

        try {
            String url = payCenterDutAccountDomain + DUT_QUERY_URL + "?" + Joiner.on("&").withKeyValueSeparator("=").join(configs);
            ResponseEntity<AccountResponse> responseEntity = payCenterDutAccountRestTemplate.exchange(
                url, HttpMethod.GET, null, new ParameterizedTypeReference<AccountResponse>() {}
            );
            AccountResponse body = responseEntity.getBody();
            log.info("getDutBindInfo, url:{}, result: {}", url, JacksonUtils.toJsonString(body));
            if (body != null && Constant.COMMON_SUC.equals(body.getCode())) {
                return body;
            }
        } catch (RestClientException e) {
            log.error("An exception is thrown when get User by userId={}, type={}, error={}", userId, dutType, e.getMessage());
            return null;
        }
        return null;
    }


}
