package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/4/27 16:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpiringRule {

    private Long id;
    private String name;
    private String skuId;//商品id
    private String skuName;  //商品  skuService查 name
    private Integer strategy;//定价策略
    private String strategyValue;//价格
    private String strategyName; // 1-折扣 2-定价 3-定单价 4-立减
    private Integer priority;
    private String status = "有效-上线";
    private Timestamp validStartTime;
    private Timestamp validEndTime;
    private Long operator; //操作人id
    private String operatorName; //域账号
    private Timestamp updateTime; // 操作时间
    private String actCode;
    private String currencyType;
    private String originActCode;
}
