//package com.iqiyi.vip.zeus.orderexport.security;
//
//import org.springframework.security.core.GrantedAuthority;
//
///**
// * @author: guojing
// * @date: 2023/12/13 10:29
// */
//public class AuthorityInfo implements GrantedAuthority {
//
//    private String authority;
//
//    public AuthorityInfo(String authority) {
//        this.authority = authority;
//    }
//
//    public void setAuthority(String authority) {
//        this.authority = authority;
//    }
//
//    @Override
//    public String getAuthority() {
//        return authority;
//    }
//
//}
