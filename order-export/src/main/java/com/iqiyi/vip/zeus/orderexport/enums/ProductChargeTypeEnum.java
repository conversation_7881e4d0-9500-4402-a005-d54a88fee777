package com.iqiyi.vip.zeus.orderexport.enums;

/**
 * 商品chargeType枚举
 *
 * @Author: <PERSON>
 * @Date: 2021/3/8
 */
public enum ProductChargeTypeEnum {
    /**
     * 单点商品
     */
    PRODUCT_CHARGE_TYP_SINGLE(0),
    /**
     * 会员商品
     */
    PRODUCT_CHARGE_TYP_VIP(1),
    /**
     * 套餐
     */
    PRODUCT_CHARGE_TYP_COMBO(2),

    /**
     * 营销
     */
    PRODUCT_CHARGE_TYP_MARKETING(3);

    private int value;

    ProductChargeTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public int intValue() {
        return value;
    }
}

