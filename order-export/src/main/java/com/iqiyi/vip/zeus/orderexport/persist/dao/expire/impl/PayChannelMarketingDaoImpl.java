package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.PayChannelMarketing;
import com.iqiyi.vip.zeus.orderexport.entity.Payment;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PayChannelMarketingDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PaymentDao;

/**
 * <AUTHOR>
 * @date 2024/4/19 9:13
 */
@Repository
@Profile("!sg")
public class PayChannelMarketingDaoImpl implements PayChannelMarketingDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<PayChannelMarketing> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(PayChannelMarketingDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
