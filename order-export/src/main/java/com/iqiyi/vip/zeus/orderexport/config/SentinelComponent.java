package com.iqiyi.vip.zeus.orderexport.config;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.alibaba.csp.sentinel.datasource.apollo.Constants;
import com.alibaba.csp.sentinel.datasource.apollo.SentinelApolloDataSource;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.CircuitBreakerStrategy;
import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.EventObserverRegistry;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iqiyi.vip.zeus.core.utils.BizDateUtil;

/**
 * @auther: guojing
 * @date: 2023/5/30 1:47 PM
 */
@Configuration
public class SentinelComponent {

    private static final Log logger = LogFactory.getLog(SentinelComponent.class.getName());

    //可根据需要开启
    @Bean
    public SentinelApolloDataSource sentinelApolloDataSource() {
        SentinelApolloDataSource sentinelApolloDataSource = new SentinelApolloDataSource();
        // 各规则的配置中心动态加载按需分别开启
        // 限流
        sentinelApolloDataSource.flowRuleSwitch(Constants.ON);
        // 降级
        sentinelApolloDataSource.degradeRuleSwitch(Constants.ON);
        // 授权
        sentinelApolloDataSource.authorityRuleSwitch(Constants.ON);
        // 系统保护
        sentinelApolloDataSource.systemRuleSwitch(Constants.ON);
        // 参数限流
        sentinelApolloDataSource.paramFlowRuleSwitch(Constants.ON);
        // 网关 普通服务需关闭此项
        //sentinelApolloDataSource.gatewayRuleSwitch(Constants.ON);

//        initDegradeRule();
        registerStateChangeObserver();
        return sentinelApolloDataSource;
    }

    @Bean
    public SentinelResourceAspect sentinelResourceAspect() {
        return new SentinelResourceAspect();
    }

//    private void initDegradeRule() {
//        DegradeRule orderCoreGetOrderRuleOfSlowRequest = createSlowReqDegradeRule("httpclient:/api/orders/repo/byOrderCode",
//            200,  5000, 20, 10000, 0.6d);
//        DegradeRule orderCoreGetOrderRuleOfErrorRatio = createErrorRatioDegradeRule("httpclient:/api/orders/repo/byOrderCode",
//            0.5d,  5000, 20, 10000);
//        List<DegradeRule> rules = Arrays.asList(orderCoreGetOrderRuleOfSlowRequest, orderCoreGetOrderRuleOfErrorRatio);
//        DegradeRuleManager.loadRules(rules);
//        System.out.println("Degrade rule loaded: " + rules);
//    }

    private DegradeRule createSlowReqDegradeRule(String resourceName, int count, int timeWindow, int minRequestAmount, int statIntervalMs, double slowRatioThreshold) {
        return createDegradeRule(CircuitBreakerStrategy.SLOW_REQUEST_RATIO, resourceName, count, timeWindow, minRequestAmount, statIntervalMs, slowRatioThreshold);
    }

    private DegradeRule createErrorRatioDegradeRule(String resourceName, double count, int timeWindow, int minRequestAmount, int statIntervalMs) {
        return createDegradeRule(CircuitBreakerStrategy.ERROR_RATIO, resourceName, count, timeWindow, minRequestAmount, statIntervalMs, 1.0d);
    }

    private DegradeRule createDegradeRule(CircuitBreakerStrategy grade, String resourceName, double count, int timeWindow, int minRequestAmount, int statIntervalMs, double slowRatioThreshold) {
        return new DegradeRule(resourceName)
            .setGrade(grade.getType())
            .setCount(count)
            .setTimeWindow(timeWindow)
            .setMinRequestAmount(minRequestAmount)
            .setStatIntervalMs(statIntervalMs)
            .setSlowRatioThreshold(slowRatioThreshold);
    }

    private void registerStateChangeObserver() {
        EventObserverRegistry.getInstance().addStateChangeObserver("logging",
            (prevState, newState, rule, snapshotValue) -> logger.info(String.format("resourceName: %s, state: %s -> %s at %s%n",
                rule.getResource(), prevState.name(), newState.name(), BizDateUtil.getNowDateTime())));
    }

}
