package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.core.req.ProjectSearchParam;
import com.iqiyi.vip.zeus.eagleclient.api.DevOpsApi;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Project;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: guojing
 * @date: 2023/12/13 17:06
 */
@Profile("!sg")
@Slf4j
@RestController
@RequestMapping("/zeus/project")
@Api(tags = "项目相关接口")
public class ProjectController {

    @Resource
    private DevOpsApi devOpsApi;

    /**
     * 返回当前用户所在团队下所有的项目
     * @param searchParam
     */
    @ApiOperation(value = "返回当前用户所在团队下所有的项目")
    @GetMapping(value = "/list")
    public BaseResponse<List<Project>> list(@Validated ProjectSearchParam searchParam) {
        List<Project> projectList = devOpsApi.searchProject(searchParam.getName(), searchParam.getTeamCode(), null);
        return BaseResponse.createSuccessList(projectList);
    }

}
