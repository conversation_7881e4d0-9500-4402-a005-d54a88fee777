package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.StoreSwitch;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.StoreSwitchDao;

/**
 * <AUTHOR>
 * @date 2024/4/8 21:29
 */
@Repository
@Profile("!sg")
public class StoreSwitchDaoImpl implements StoreSwitchDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<StoreSwitch> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(StoreSwitchDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
