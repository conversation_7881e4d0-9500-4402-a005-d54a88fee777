package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.redis.core.TimeToLive;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/4/27 17:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpiringPackage {

    private Integer id; //策略id
    private String name; // 策略名
    private String storeCode; //收银台code
    private String store; // 收银台
    private String version; //版本
    private Integer levelId; //活动层级id
    private String level; // 活动层级
    private Integer priority; // 优先级
    private String status = "有效-上线";
    private Timestamp validStartTime;
    private Timestamp validEndTime;
    private String operator;
    private Timestamp updateTime;
}
