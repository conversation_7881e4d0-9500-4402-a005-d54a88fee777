package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2021/8/17 15:22
 */
@Data
public class AutoRenewDutType {

    /**
     * id.
     */
    protected Long id;

    /**
     * 代扣方式.
     */
    private Integer dutType;

    /**
     * 代扣方式名称.
     */
    private String name;

    /**
     * 会员类型.
     */
    private Long vipType;

    /**
     * 升级源会员类型.
     */
    private Long sourceVipType;

    /**
     * 支付渠道.
     */
    private Integer payChannel;

    /**
     * 支付渠道名称.
     */
    private String payChannelName;

    /**
     * 支付渠道类型.
     */
    private Integer payChannelType;

    /**
     * 代扣支付方式.
     */
    private Integer dutPayType;

    /**
     * 产品编码.
     */
    private String productCode;

    /**
     * 是否支持切换时长(1:支持,0:不支持)
     */
    private Short changeAmount;

    /**
     * 是否支持直接取消自动续费(1:支持,0:不支持)
     */
    private Short directCancel;

    /**
     * 取消自动续费时解绑(0:不解绑，1:解绑)
     */
    private Short cancelAutorenwUnbind;

    /**
     * 是否支持纯签约(1:支持,0:不支持)
     */
    private Short supportPureSign;

    /**
     * 优先级(数字越大优先级越高)
     */
    private Short priority;

    /**
     * 状态.
     */
    private Integer status;

    /**
     * 业务方code
     */
    private String businessCode;

    /**
     * 有效开始时间.
     */
    private Timestamp validStartTime;
    /**
     * 有效结束时间.
     */
    private Timestamp validEndTime;

}
