package com.iqiyi.vip.zeus.orderexport.util.mail;

import lombok.Data;

import java.util.List;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2018/8/5 Time: 12:44
 * 邮件内容
 */
@Data
public class TableMailContent {

    /**
     * 表格说明,可为空
     */
    private String tableComment;
    /**
     * 表格列头,可为空
     */
    private List<String> tableTitles;
    /**
     * 表格内容,可为空,每个list里数据对应一行
     */
    private List<List<Object>> tableContents;

    /**
     * 是否需要显示序号列
     */
    private boolean needSequenceCol = true;

    public TableMailContent() {
    }

    public TableMailContent(List<String> tableTitles, List<List<Object>> tableContents) {
        this.tableTitles = tableTitles;
        this.tableContents = tableContents;
    }

    public TableMailContent(String tableComment, List<String> tableTitles, List<List<Object>> tableContents) {
        this.tableComment = tableComment;
        this.tableTitles = tableTitles;
        this.tableContents = tableContents;
    }
}
