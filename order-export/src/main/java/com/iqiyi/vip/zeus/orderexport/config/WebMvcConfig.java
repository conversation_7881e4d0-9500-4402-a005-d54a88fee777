package com.iqiyi.vip.zeus.orderexport.config;

import com.alibaba.csp.sentinel.adapter.spring.webmvc.SentinelWebInterceptor;
import com.alibaba.csp.sentinel.adapter.spring.webmvc.config.SentinelWebMvcConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.google.common.collect.Lists;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.orderexport.interceptor.LoginUserInterceptor;

import static com.alibaba.fastjson.serializer.SerializerFeature.BrowserSecure;
import static com.alibaba.fastjson.serializer.SerializerFeature.DisableCircularReferenceDetect;

/**
 * @author: guojing
 * @date: 2023/12/13 11:04
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Resource
    private LoginUserInterceptor loginUserInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        SentinelWebMvcConfig config = new SentinelWebMvcConfig();
        // Enable the HTTP method prefix.
        config.setHttpMethodSpecify(true);
        config.setBlockExceptionHandler((request, response, e) -> {
            response.setHeader("Content-Type", "application/json;charset=UTF-8");
            PrintWriter out = response.getWriter();

            Map<String, String> map = new HashMap<>();
            map.put("code", "Q00449");
            map.put("msg", "被限流了");
            map.put("message", "被限流了");
            out.print(JSON.toJSONString(map));
            out.flush();
            out.close();
        });
        registry.addInterceptor(new SentinelWebInterceptor(config)).addPathPatterns("/zeus/**");

        registry.addInterceptor(loginUserInterceptor).addPathPatterns("/zeus/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
            .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
            .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        /**
         * 解决 ResponseBody返回字符串带双引号 问题
         * 注意，stringConverter 要放在 fastJsonpHttpMessageConverter4 之前，否则不生效
         */
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        List<MediaType> supportedMediaTypes = Lists.newArrayList();
        supportedMediaTypes.add(MediaType.valueOf("text/plain;charset=UTF-8"));
        supportedMediaTypes.add(MediaType.valueOf("text/html;charset=UTF-8"));
        supportedMediaTypes.add(MediaType.valueOf("application/json;charset=UTF-8"));
        supportedMediaTypes.add(MediaType.valueOf("application/javascript;charset=UTF-8"));
        stringConverter.setSupportedMediaTypes(supportedMediaTypes);
        stringConverter.setWriteAcceptCharset(false);

        FastJsonHttpMessageConverter fastJsonHttpMessageConverter = new FastJsonHttpMessageConverter();
        List<MediaType> fastJsonSupportedMediaTypes = Lists.newArrayList();
        fastJsonSupportedMediaTypes.add(MediaType.valueOf("application/json;charset=UTF-8"));
        fastJsonHttpMessageConverter.setSupportedMediaTypes(fastJsonSupportedMediaTypes);
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(BrowserSecure, DisableCircularReferenceDetect);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);

        converters.add(0, stringConverter);
        converters.add(1, fastJsonHttpMessageConverter);
    }

}
