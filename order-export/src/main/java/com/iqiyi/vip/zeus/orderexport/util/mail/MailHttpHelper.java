package com.iqiyi.vip.zeus.orderexport.util.mail;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.HtmlUtils;

import java.nio.charset.Charset;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Http工具类 User: zhangdaoguang Date: 2018/8/15 Time: 14:53
 */
@Slf4j
public class MailHttpHelper {

    private static RestTemplate restTemplate;

    static {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(2000);
        requestFactory.setReadTimeout(5000);
        restTemplate = new RestTemplate(requestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
    }

    /**
     * GET 请求
     *
     * @param url 请求地址
     * @param params 参数
     * @return 请求结果json对象
     */
    public static JSONObject doGet(String url, Map<String, Object> params) throws Exception {
        return doGet(url, params, restTemplate);
    }

    /**
     * GET 请求 -  支持自定义请求头
     *
     * @param url 请求地址
     * @param params 参数
     * @param headers 请求头
     * @return 请求结果json对象
     */
    public static JSONObject doGet(String url, Map<String, Object> params, Map<String, Object> headers)
        throws Exception {
        return doGet(url, params, headers, restTemplate);
    }

    /**
     * POST 请求
     *
     * @param url 请求地址
     * @param params 参数
     * @return 请求结果json对象
     */
    public static JSONObject doPost(String url, Map<String, Object> params) throws Exception {
        return doPost(url, params, restTemplate);
    }

    /**
     * POST 请求 -  支持自定义请求头
     *
     * @param url 请求地址
     * @param params 参数
     * @param headers 请求头
     * @return 请求结果json对象
     */
    public static JSONObject doPost(String url, Map<String, Object> params, Map<String, Object> headers)
        throws Exception {
        return doPost(url, params, headers, restTemplate);
    }

    /**
     * GET 请求  (指定restTemplate)
     *
     * @param url 请求地址
     * @param params 参数
     * @param restTemplate 自定义restTemplate
     * @return 请求结果json对象
     */
    public static JSONObject doGet(String url, Map<String, Object> params, RestTemplate restTemplate) throws Exception {
        try {
            log.info("[HTTP-get][url:{}][params:{}]", url, params);
            JSONObject result = restTemplate.getForObject(buildQueryUrl(url, params), JSONObject.class);
            log.info("[HTTP-get][url:{}][params:{}][result:{}]", url, params, result);
            return result;
        } catch (Exception e) {
            log.error("[HTTP-get][Exception][url:{}][params:{}]", url, params, e);
            throw e;
        }
    }

    /**
     * GET 请求 -  支持自定义请求头   (指定restTemplate)
     *
     * @param url 请求地址
     * @param params 参数
     * @param headers 请求头
     * @param restTemplate 自定义restTemplate
     * @return 请求结果json对象
     */
    public static JSONObject doGet(String url, Map<String, Object> params,
        Map<String, Object> headers, RestTemplate restTemplate) throws Exception {
        try {
            log.info("[HTTP-get][url:{}][params:{}][headers:{}]", url, params, headers);
            HttpHeaders requestHeaders = new HttpHeaders();
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                requestHeaders.add(entry.getKey(), String.valueOf(entry.getValue()));
            }
            HttpEntity<String> requestEntity = new HttpEntity<String>(null, requestHeaders);
            JSONObject result = restTemplate.exchange(buildQueryUrl(url, params), HttpMethod.GET,
                requestEntity, JSONObject.class).getBody();
            log.info("[HTTP-get][url:{}][params:{}][headers:{}][result:{}]", url, params, headers, result);
            return result;
        } catch (Exception e) {
            log.error("[HTTP-get][Exception][url:{}][params:{}][headers:{}]", url, params, headers, e);
            throw e;
        }
    }

    /**
     * POST 请求    (指定restTemplate)
     *
     * @param url 请求地址
     * @param params 参数
     * @param restTemplate 自定义restTemplate
     * @return 请求结果json对象
     */
    public static JSONObject doPost(String url, Map<String, Object> params, RestTemplate restTemplate)
        throws Exception {
        try {
            log.info("[HTTP-post][url:{}][params:{}]", url, params);
            MultiValueMap multiValueMap = new LinkedMultiValueMap();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                multiValueMap.put(entry.getKey(), Collections.singletonList(entry.getValue()));
            }
            HttpEntity httpEntity = new HttpEntity(multiValueMap);
            JSONObject result = restTemplate.postForObject(url, httpEntity, JSONObject.class);
            log.info("[HTTP-post][url:{}][params:{}][result:{}]", url, params, result);
            return result;
        } catch (Exception e) {
            log.error("[HTTP-post][Exception][url:{}][params:{}]", url, params, e);
            throw e;
        }
    }

    /**
     * POST 请求 -  支持自定义请求头 (指定restTemplate)
     *
     * @param url 请求地址
     * @param params 参数
     * @param headers 请求头
     * @param restTemplate 自定义restTemplate
     * @return 请求结果json对象
     */
    public static JSONObject doPost(String url, Map<String, Object> params,
        Map<String, Object> headers, RestTemplate restTemplate) throws Exception {
        try {
            log.info("[HTTP-post][url:{}][params:{}]", url, params);
            HttpHeaders requestHeaders = new HttpHeaders();
            for (Map.Entry<String, Object> entry : headers.entrySet()) {
                requestHeaders.add(entry.getKey(), String.valueOf(entry.getValue()));
            }

            MultiValueMap multiValueMap = new LinkedMultiValueMap();
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                multiValueMap.put(entry.getKey(), Collections.singletonList(entry.getValue()));
            }
            HttpEntity httpEntity = new HttpEntity(multiValueMap, requestHeaders);
            JSONObject result = restTemplate.postForObject(url, httpEntity, JSONObject.class);
            log.info("[HTTP-post][url:{}][params:{}][result:{}]", url, params, result);
            return result;
        } catch (Exception e) {
            log.error("[HTTP-post][Exception][url:{}][params:{}]", url, params, e);
            throw e;
        }
    }

    private static String buildQueryUrl(String baseUrl, Map<String, Object> queryParams) {
        StringBuilder sb = new StringBuilder(baseUrl);
        sb.append("?");
        Iterator finalUrl = queryParams.entrySet().iterator();

        while (finalUrl.hasNext()) {
            Map.Entry entry = (Map.Entry) finalUrl.next();
            String value = convertValue(entry.getValue());
            sb.append((String) entry.getKey()).append("=").append(HtmlUtils.htmlEscape(value)).append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }

    private static String convertValue(Object value) {
        return value instanceof Object[] ? StringUtils.join(((Object[]) value), ",") : value.toString();
    }

    private static List<HttpMessageConverter<?>> transConverters(List<HttpMessageConverter<?>> oldMessageConverters) {
        List<HttpMessageConverter<?>> messageConverters = Lists.newArrayList();
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
        for (HttpMessageConverter converter : oldMessageConverters) {
            if (converter instanceof StringHttpMessageConverter) {
                messageConverters.add(stringHttpMessageConverter);
            } else {
                messageConverters.add(converter);
            }
        }
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(SerializerFeature.WriteMapNullValue,
            SerializerFeature.QuoteFieldNames, SerializerFeature.DisableCircularReferenceDetect);
        fastConverter.setFastJsonConfig(fastJsonConfig);

        List<MediaType> fastMediaTypes = Lists.newArrayList();
        fastMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        fastMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE + ";UTF-8"));
        fastMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_HTML + ";UTF-8"));
        fastConverter.setSupportedMediaTypes(fastMediaTypes);
        messageConverters.add(fastConverter);
        return messageConverters;
    }
}

