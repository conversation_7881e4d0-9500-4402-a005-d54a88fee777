package com.iqiyi.vip.zeus.orderexport.config;

import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

import com.iqiyi.kit.http.client.spring.ApacheFactoryProperties;
import com.iqiyi.kit.http.client.spring.ClientHttpRequestFactories;
import com.iqiyi.kit.http.client.spring.HttpClientImplementation;
import com.iqiyi.kit.http.client.spring.RestTemplateBuilder;

@Configuration
public class HttpConfig {

    @LoadBalanced
    @Bean(name = "lbRestTemplate")
    RestTemplate lbRestTemplate() {
        return RestTemplateBuilder.create() // create 方法用于创建一个 RestTemplateBuilder 实例
            .clientHttpRequestFactory(ClientHttpRequestFactories.newApacheFactory(getApacheFactoryProperties())) // 为 RestTemplate 设置 ClientHttpRequestFactory
            .implementation(HttpClientImplementation.APACHE)
            .messageConverters(getFastJsonMessageConverters()) // 设置 MessageConverters
            .build(); // 根据之前的设置创建 RestTemplate
    }

    @Bean(name = "payChannelRestTemplate")
    RestTemplate payChannelRestTemplate() {
        return RestTemplateBuilder.create() // create 方法用于创建一个 RestTemplateBuilder 实例
            .clientHttpRequestFactory(ClientHttpRequestFactories.newApacheFactory(getApacheFactoryProperties())) // 为 RestTemplate 设置 ClientHttpRequestFactory
            .implementation(HttpClientImplementation.APACHE)
            .messageConverters(getFastJsonMessageConverters()) // 设置 MessageConverters
            .build(); // 根据之前的设置创建 RestTemplate
    }

    @Bean(name = "restTemplate")
    public RestTemplate restTemplate() {

        return RestTemplateBuilder.create() // create 方法用于创建一个 RestTemplateBuilder 实例
            .clientHttpRequestFactory(ClientHttpRequestFactories.newApacheFactory(getApacheFactoryProperties())) // 为 RestTemplate 设置 ClientHttpRequestFactory
            .implementation(HttpClientImplementation.APACHE)
            .messageConverters(getFastJsonMessageConverters()) // 设置 MessageConverters
            .build(); // 根据之前的设置创建 RestTemplate
    }

    private List<HttpMessageConverter<?>> getFastJsonMessageConverters() {
        FastJsonHttpMessageConverter fastJsonHttpMessageConverter =
            new FastJsonHttpMessageConverter(); // 创建 MessageConverter，用于转换 Request 和 Response body。不是必须创建，这里是为了对 MessageConverter 进行自定义配置
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        fastJsonHttpMessageConverter.setSupportedMediaTypes(supportedMediaTypes);
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setFeatures(Feature.AllowUnQuotedFieldNames);
        fastJsonConfig.setSerializerFeatures(SerializerFeature.WriteMapNullValue);
        fastJsonHttpMessageConverter.setFastJsonConfig(fastJsonConfig);
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        messageConverters.add(fastJsonHttpMessageConverter);
        return messageConverters;
    }

    private ApacheFactoryProperties getApacheFactoryProperties() {
        // 创建用于配置 ClientHttpRequestFactory 的属性类，主要用于设置超时时间、连接数等属性
        ApacheFactoryProperties apacheFactoryProperties = new ApacheFactoryProperties();
        apacheFactoryProperties.setConnectTimeoutInMillis(200000);
        apacheFactoryProperties.setSocketTimeoutInMillis(120000);
        apacheFactoryProperties.setDefaultMaxPerRouteConnectionNumber(10);//跟服务器个数有关系,单服务IP维度
        apacheFactoryProperties.setMaxTotalConnectionNumber(1000);

        return apacheFactoryProperties;
    }

}