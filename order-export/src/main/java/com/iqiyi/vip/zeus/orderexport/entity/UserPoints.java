package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <PERSON>
 * @Date: 2022/11/11
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class UserPoints {

    /**
     * 积分活动code
     */
    private String actCode;
    /**
     * 可以减免金额，单位分
     */
    private Integer discountAmount;
    /**
     * 积分单品code 积分系统分配
     */
    private String skuCode;
    /**
     * 减免金额的积分值
     */
    private Integer consumedPoints;
    /**
     * 活动类型
     */
    private Integer activityType;

}
