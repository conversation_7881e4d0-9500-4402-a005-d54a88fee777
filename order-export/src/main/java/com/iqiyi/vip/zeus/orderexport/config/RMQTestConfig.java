package com.iqiyi.vip.zeus.orderexport.config;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.constant.MQConstant;
import com.iqiyi.vip.zeus.orderexport.consumer.OrderDelayMsgConsumer;
import com.iqiyi.vip.zeus.orderexport.consumer.OrderPaidConsumer;

/**
 * 测试环境验证功能使用
 *
 * @Author: Lin Peihui
 * @Date: 2020/11/23
 */
@Profile({"dev"})
@Configuration
public class RMQTestConfig {

    @Value("${trade.order.rmq.namesrvaddr}")
    private String tradeOrderRmqNamesrvAddr;

    @Value("${trade.order.rmq.paid.consumer.token}")
    private String tradeOrderRmqPaidConsumerToken;
    @Value("${trade.order.rmq.monitor.producer.token}")
    private String tradeOrderRmqMonitorProducerToken;
    @Value("${trade.order.rmq.monitor.consumer.token}")
    private String tradeOrderRmqMonitorConsumerToken;

    @Resource
    private OrderDelayMsgConsumer orderDelayMsgConsumer;
    @Resource
    private OrderPaidConsumer orderPaidConsumer;

    @Bean(name = "orderMonitorDelayProducer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQProducer orderMonitorDelayProducer() {
        DefaultMQProducer producer = new DefaultMQProducer(MQConstant.TRADE_ORDER_MONITOR_DELAY_PRODUCER_GROUP);
        producer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        producer.setToken(tradeOrderRmqMonitorProducerToken);
        return producer;
    }

    @Bean(name = "orderMonitorDelayMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer orderMonitorDelayMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.TRADE_ORDER_MONITOR_DELAY_CONSUMER_GROUP);
        consumer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        consumer.setToken(tradeOrderRmqMonitorConsumerToken);
        consumer.subscribe(MQConstant.TRADE_ORDER_MONITOR_DELAY_TOPIC, "*");
        consumer.registerMessageListener(orderDelayMsgConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "orderPaidMsgConsumer", initMethod = "start",destroyMethod = "shutdown")
    public DefaultMQPushConsumer orderPaidMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.TRADE_ORDER_PAID_CONSUMER_GROUP);
        consumer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        consumer.setToken(tradeOrderRmqPaidConsumerToken);
        consumer.subscribe(MQConstant.TRADE_ORDER_PAID_TOPIC, "*");
        consumer.registerMessageListener(orderPaidConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

}
