package com.iqiyi.vip.zeus.orderexport.util;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * @author: guojing
 * @date: 2025/3/26 22:52
 */
public class SignUtil {

    public static String generateSign(Map<String, String> params, String signKey) {
        Map<String, String> signParams = Maps.transformValues(params, input -> input);
        String strForSign = Joiner.on("&").withKeyValueSeparator("=").join(new TreeMap<>(signParams)).concat(signKey);
        return DigestUtils.md5Hex(strForSign);
    }

    public static String generateSignWithObjValue(Map<String, Object> params, String signKey) {
        Map<String, String> signParams = Maps.transformValues(params, input -> input != null ? input.toString() : "");
        signParams.remove("sign");
        SortedMap<String, String> sortedParams = new TreeMap<>(signParams);
        String strForSign = Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sortedParams).concat(signKey);
        return DigestUtils.md5Hex(strForSign);
    }

}
