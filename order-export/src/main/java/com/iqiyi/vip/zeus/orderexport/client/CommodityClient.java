package com.iqiyi.vip.zeus.orderexport.client;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.iqiyi.vip.zeus.orderexport.constant.Constant;
import com.iqiyi.vip.v.commodity.model.DataResult;
import com.iqiyi.vip.v.commodity.request.BatchQuerySkuInfoRequest;
import com.iqiyi.vip.v.commodity.request.QuerySkuRequest;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.v.commodity.services.SkuService;

/**
 * @Author: Lin Peihui
 * @Date: 2022/11/24
 */
@Slf4j
@Component
//@Profile("!sg")
public class CommodityClient {

    @Value("${commodity.config.app.caller}")
    private String caller;
    @Value("${commodity.config.app.signKey}")
    private String signKey;

    @Resource
    private SkuService skuService;

    /**
     * 根据skuId查询商品信息
     */
    public Optional<QuerySkuResponse> queryCommodity(String skuId) {
        QuerySkuRequest querySkuRequest = new QuerySkuRequest();
        querySkuRequest.setSkuId(skuId);
        querySkuRequest.setCaller(caller);
        querySkuRequest.setSignKey(signKey);
        querySkuRequest.setRetryCount(1);
        long start = System.currentTimeMillis();
        try {
            DataResult<QuerySkuResponse> result = skuService.query(querySkuRequest);
            log.info("queryCommodity params:{}, cost:{}ms, result:{}", querySkuRequest, (System.currentTimeMillis() - start), JSON.toJSON(result));

            if (isFailed(result)) {
                log.info("queryCommodity responseEntity_body_null params:{}", querySkuRequest);
                return Optional.empty();
            }
            return Optional.of(result.getData());
        } catch (Exception e) {
            log.error("queryCommodity exception params:{} ", querySkuRequest, e);
            return Optional.empty();
        }
    }

    /**
     * 根据skuId列表批量查询商品信息
     */
    public Optional<Map<String, QuerySkuResponse>> batchQueryCommodity(String skuIds) {
        if (StringUtils.isEmpty(skuIds)) {
            return Optional.empty();
        }
        BatchQuerySkuInfoRequest batchQuerySkuInfoRequest = new BatchQuerySkuInfoRequest();
        batchQuerySkuInfoRequest.setSkuIds(skuIds);
        batchQuerySkuInfoRequest.setCaller(caller);
        batchQuerySkuInfoRequest.setSignKey(signKey);
        batchQuerySkuInfoRequest.setRetryCount(1);
        long start = System.currentTimeMillis();
        try {
            DataResult<Map<String, QuerySkuResponse>> result = skuService.batchQuery(batchQuerySkuInfoRequest);
            log.info("queryCommoditys params:{}, cost:{}ms, result:{}", skuIds, (System.currentTimeMillis() - start), JSON.toJSON(result));

            if (isFailed(result)) {
                log.info("queryCommoditys responseEntity_body_null params:{}", skuIds);
                return Optional.empty();
            }
            return Optional.of(result.getData());
        } catch (Exception e) {
            log.error("queryCommoditys exception params:{} ", skuIds, e);
            return Optional.empty();
        }
    }

    private boolean isFailed(DataResult<?> result) {
        return Objects.isNull(result) || !Constant.COMMON_SUC.equals(result.getCode());
    }

}
