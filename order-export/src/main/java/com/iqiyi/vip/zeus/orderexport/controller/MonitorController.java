package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MonitorCategory;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.KeyValuePair;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorAndAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorDetail;
import com.iqiyi.vip.zeus.core.req.MonitorSearchParam;
import com.iqiyi.vip.zeus.core.service.EagleDashboardService;
import com.iqiyi.vip.zeus.core.service.SmartAlertComponent;
import com.iqiyi.vip.zeus.core.service.ZeusAlertRuleService;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorAssembleService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.param.AlertRuleCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.param.MonitorCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.validator.alertrule.ZeusAlertRuleValidator;
import com.iqiyi.vip.zeus.orderexport.validator.monitor.MonitorConfigValidatorFactory;

/**
 * @author: guojing
 * @date: 2023/12/2 18:19
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/monitor")
@Api(tags = "宙斯监控相关接口")
public class MonitorController {

    @Resource
    private ZeusMonitorService zeusMonitorService;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;
    @Resource
    private EagleDashboardService eagleDashboardService;
    @Resource
    private MonitorConfigValidatorFactory monitorConfigValidatorFactory;
    @Resource
    private ZeusAlertRuleValidator alertRuleValidator;
    @Resource
    private ZeusAlertRuleService alertRuleService;
    @Resource
    private ZeusMonitorAssembleService monitorAssembleService;
    @Resource
    private SmartAlertComponent smartAlertComponent;

    /**
     * 获取监控种类枚举信息
     */
    @ApiOperation(value = "监控种类枚举列表")
    @GetMapping(value = "/allMonitorCategoryInfo")
    public BaseResponse<List<KeyValuePair<Integer, String>>> monitorCategory() {
        List<KeyValuePair<Integer, String>> monitorCategoryList = Arrays.stream(MonitorCategory.values())
            .map(monitorCategory -> new KeyValuePair<>(monitorCategory.getValue(), monitorCategory.getDesc()))
            .collect(Collectors.toList());
        return BaseResponse.createSuccessList(monitorCategoryList);
    }

    /**
     * 创建监控
     */
    @ApiOperation(value = "创建监控")
    @PostMapping(value = "/create")
    public BaseResponse<Integer> create(@Validated @RequestBody MonitorCreateOrUpdateParam createParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        if (createParam.getId() != null) {
            throw BizException.newParamException("无需指定监控id");
        }
        MonitorCategory monitorCategory = MonitorCategory.parseValue(createParam.getCategory());
        if (monitorCategory == null) {
            throw BizException.newParamException("未知的监控种类");
        }
        if (monitorCategory == MonitorCategory.DATA_TREND && StringUtils.isBlank(createParam.getDashboardUid())) {
            throw BizException.newParamException("监控Dashboard不能为空");
        }
        if (createParam.getPanelId() != null) {
            throw BizException.newParamException("无需指定监控Panel");
        }
        ZeusDatasource zeusDatasource = zeusDatasourceService.getById(createParam.getDatasourceId());
        if (zeusDatasource == null || zeusDatasource.invalid()) {
            throw BizException.newParamException("依赖的数据源不存在");
        }
        if (currentUser.notUnderThisTeam(zeusDatasource.getTeamCode())) {
            throw BizException.newParamException("无权使用此数据源");
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
        monitorConfigValidatorFactory.getValidator(dataSourceType).checkConfig(createParam);
        DashboardWithMeta dashboardWithMeta = null;
        if (StringUtils.isNotBlank(createParam.getDashboardUid())) {
            dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(createParam.getDashboardUid());
            if (dashboardWithMeta == null) {
                throw BizException.newParamException("监控Dashboard不存在");
            }
        }
        ZeusMonitor createMonitorData = createParam.toZeusMonitor();
        createMonitorData.setTeamCode(realTeam.getTeamCode());
        createMonitorData.setCreateUser(currentUser.getOaAccount());
        createMonitorData.setUpdateUser(currentUser.getOaAccount());

        ZeusAlertRule createAlertRuleData = null;
        AlertRuleCreateOrUpdateParam alertRuleParam = createParam.getAlertRuleParam();
        if (alertRuleParam != null) {
            alertRuleValidator.checkAlertRuleParamWhenCreateMonitor(alertRuleParam);
            createAlertRuleData = createParam.getAlertRuleParam().toZeusAlertRule();
            createAlertRuleData.setTeamCode(realTeam.getTeamCode());
            createAlertRuleData.setCreateUser(currentUser.getOaAccount());
            createAlertRuleData.setUpdateUser(currentUser.getOaAccount());
            if (BooleanUtils.toBoolean(alertRuleParam.getEnableSmartAlert())) {
                String smartAlertRuleName = smartAlertComponent.checkAndGenerateRuleName(createMonitorData.getName());
                createAlertRuleData.setSmartAlertRule(smartAlertRuleName);
            }
        }
        Integer monitorId = monitorAssembleService.createMonitorAndAlertRule(createMonitorData, createAlertRuleData, zeusDatasource, dashboardWithMeta);
        if (monitorId == null) {
            return BaseResponse.createSystemError("监控创建失败");
        }
        return BaseResponse.createSuccess(monitorId);
    }

    /**
     * 更新监控
     */
    @ApiOperation(value = "更新监控")
    @PostMapping(value = "/update")
    public BaseResponse<Boolean> update(@Validated @RequestBody MonitorCreateOrUpdateParam updateParam) {
        if (updateParam.getId() == null) {
            throw BizException.newParamException("监控id不能为空");
        }
        ZeusMonitorDetail monitorDetailFromDB = zeusMonitorService.getDetailById(updateParam.getId());
        if (monitorDetailFromDB == null) {
            throw BizException.newParamException("监控不存在");
        }
        ZeusDatasource zeusDatasource = monitorDetailFromDB.getDatasource();
        if (!zeusDatasource.getId().equals(updateParam.getDatasourceId())) {
            throw BizException.newParamException("不能修改数据源");
        }
        ZeusMonitor monitorInDB = monitorDetailFromDB.getMonitor();
        if (!monitorInDB.getCategory().equals(updateParam.getCategory())) {
            throw BizException.newParamException("监控种类不能修改");
        }
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        if (!Objects.equals(realTeam.getTeamCode(), monitorInDB.getTeamCode())) {
            throw BizException.newParamException("仅可更新自己所在团队下的监控");
        }
        if (!monitorInDB.getDashboardUid().equals(updateParam.getDashboardUid())) {
            throw BizException.newParamException("监控Dashboard不能修改");
        }
        if (!monitorInDB.getPanelId().equals(updateParam.getPanelId())) {
            throw BizException.newParamException("监控Panel Id不能修改");
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(zeusDatasource.getType());
        monitorConfigValidatorFactory.getValidator(dataSourceType).checkConfig(updateParam);

        DashboardWithMeta dashboardWithMeta = null;
        if (StringUtils.isNotBlank(updateParam.getDashboardUid())) {
            dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(updateParam.getDashboardUid());
            if (dashboardWithMeta == null) {
                throw BizException.newParamException("监控Dashboard不存在");
            }
        }

        ZeusAlertRule updateAlertRuleData = null;
        AlertRuleCreateOrUpdateParam alertRuleParam = updateParam.getAlertRuleParam();
        if (alertRuleParam != null) {
            alertRuleValidator.checkAlertRuleParamWhenUpdateMonitor(updateParam.getAlertRuleParam(), realTeam);
            updateAlertRuleData = alertRuleParam.toZeusAlertRule();
            updateAlertRuleData.setUpdateUser(currentUser.getOaAccount());
            if (BooleanUtils.toBoolean(alertRuleParam.getEnableSmartAlert())) {
                String smartAlertRuleName = smartAlertComponent.checkAndGenerateRuleName(updateParam.getName());
                updateAlertRuleData.setSmartAlertRule(smartAlertRuleName);
            }
        }

        ZeusMonitor updateMonitorData = updateParam.toZeusMonitor();
        updateMonitorData.setUpdateUser(currentUser.getOaAccount());
        boolean updated = monitorAssembleService.updateMonitorAndAlertRule(updateMonitorData, updateAlertRuleData, monitorInDB, zeusDatasource, dashboardWithMeta);
        return BaseResponse.createSuccess(updated);
    }

    /**
     * 删除监控
     */
    @ApiOperation(value = "删除监控")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "监控id", dataType = "Integer", required = true, paramType = "query")})
    @PostMapping(value = "/delete")
    public BaseResponse<Boolean> delete(@NotNull(message = "监控ID不能为空") Integer id) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        ZeusMonitor monitorFromDB = zeusMonitorService.getById(id);
        if (monitorFromDB == null) {
            throw BizException.newParamException("监控不存在");
        }
        if (!Objects.equals(monitorFromDB.getTeamCode(), realTeam.getTeamCode())) {
            throw BizException.newParamException("无权操作此监控");
        }
        return BaseResponse.createSuccess(zeusMonitorService.delete(monitorFromDB));
    }

    @ApiOperation(value = "根据id查询监控详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "监控id", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getDetailById")
    public BaseResponse<ZeusMonitorAndAlertRule> getDetailById(@NotNull(message = "监控ID不能为空") Integer id) {
        ZeusMonitor monitor = zeusMonitorService.getFriendlyMonitorById(id);
        if (monitor == null) {
            return BaseResponse.createSuccess(null);
        }
        ZeusMonitorAndAlertRule result = new ZeusMonitorAndAlertRule();
        BeanUtils.copyProperties(monitor, result);
        ZeusAlertRule zeusAlertRule = alertRuleService.getByMonitor(monitor.getId());
        if (zeusAlertRule != null) {
            result.setAlertRule(zeusAlertRule);
        }
        return BaseResponse.createSuccess(result);
    }

    @ApiOperation(value = "搜索监控")
    @GetMapping(value = "/search")
    public BaseResponse<List<ZeusMonitor>> search(@Validated MonitorSearchParam searchParam) {
        return BaseResponse.createSuccessList(zeusMonitorService.search(searchParam));
    }

}
