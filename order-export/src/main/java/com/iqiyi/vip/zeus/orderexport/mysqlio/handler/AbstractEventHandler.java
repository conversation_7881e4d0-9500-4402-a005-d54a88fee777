package com.iqiyi.vip.zeus.orderexport.mysqlio.handler;

import lombok.extern.slf4j.Slf4j;

import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractEventHandler implements CanalEventHandler {

    @Override
    public boolean accept(MapCanalEvent event) {
        if (event == null) {
            log.warn("CanalEvent is null");
            return false;
        }
        if (CanalEventUtil.isDelete(event.getEventType()) || !event.getTableName().equals(this.getTableName())) {
            log.warn("Receiver Delete Event, Can Not Process This Event.");
            return false;
        }
        return true;
    }

}
