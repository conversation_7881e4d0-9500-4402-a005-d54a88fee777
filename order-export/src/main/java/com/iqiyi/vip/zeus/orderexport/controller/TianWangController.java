package com.iqiyi.vip.zeus.orderexport.controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 天网控制器
 * 使用Thymeleaf模板引擎，支持页面拆分
 * 
 * <AUTHOR>
 * @date 2024-01-10 10:00:00
 */
@Controller
@RequestMapping("/tianwang")
public class TianWangController {

    /**
     * 天网主页 - 重定向到仪表盘
     */
    @GetMapping("")
    public String index() {
        return "redirect:/tianwang/dashboard";
    }

    /**
     * 仪表盘页面
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据源列表
        List<Map<String, Object>> datasources = new ArrayList<>();
        // 这里可以调用service获取真实数据
        
        // 模拟稽核项列表
        List<Map<String, Object>> guardItems = new ArrayList<>();
        // 这里可以调用service获取真实数据
        
        // 模拟报告数据
        List<Map<String, Object>> reportData = new ArrayList<>();
        // 这里可以调用service获取真实数据
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "dashboard";
    }

    /**
     * 业务管理页面
     */
    @GetMapping("/business")
    public String business(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据
        List<Map<String, Object>> datasources = new ArrayList<>();
        List<Map<String, Object>> guardItems = new ArrayList<>();
        List<Map<String, Object>> reportData = new ArrayList<>();
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "business";
    }

    /**
     * 数据源管理页面
     */
    @GetMapping("/datasource")
    public String datasource(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据
        List<Map<String, Object>> datasources = new ArrayList<>();
        List<Map<String, Object>> guardItems = new ArrayList<>();
        List<Map<String, Object>> reportData = new ArrayList<>();
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "datasource";
    }

    /**
     * 稽核项管理页面
     */
    @GetMapping("/guard-items")
    public String guardItems(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据
        List<Map<String, Object>> datasources = new ArrayList<>();
        List<Map<String, Object>> guardItems = new ArrayList<>();
        List<Map<String, Object>> reportData = new ArrayList<>();
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "guard-items";
    }

    /**
     * 我的订阅页面
     */
    @GetMapping("/subscription")
    public String subscription(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据
        List<Map<String, Object>> datasources = new ArrayList<>();
        List<Map<String, Object>> guardItems = new ArrayList<>();
        List<Map<String, Object>> reportData = new ArrayList<>();
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "subscription";
    }

    /**
     * 稽核报告页面
     */
    @GetMapping("/reports")
    public String reports(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据
        List<Map<String, Object>> datasources = new ArrayList<>();
        List<Map<String, Object>> guardItems = new ArrayList<>();
        List<Map<String, Object>> reportData = new ArrayList<>();
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "reports";
    }

    /**
     * 操作日志页面
     */
    @GetMapping("/logs")
    public String logs(Model model) {
        // 用户信息
        Map<String, Object> user = new HashMap<>();
        user.put("name", "张三");
        user.put("role", "DEVELOPER");
        user.put("avatar", "https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32");
        
        // 模拟数据
        List<Map<String, Object>> datasources = new ArrayList<>();
        List<Map<String, Object>> guardItems = new ArrayList<>();
        List<Map<String, Object>> reportData = new ArrayList<>();
        
        model.addAttribute("pageTitle", "天网");
        model.addAttribute("user", user);
        model.addAttribute("datasources", datasources);
        model.addAttribute("guardItems", guardItems);
        model.addAttribute("reportData", reportData);
        
        return "logs";
    }

    /**
     * Vue测试页面
     */
    @GetMapping("/test")
    public String test() {
        return "test";
    }
}
