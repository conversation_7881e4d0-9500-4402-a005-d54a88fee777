package com.iqiyi.vip.zeus.orderexport.handler;

import com.google.common.base.Splitter;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;

/**
 * Created at: 2021-09-14
 *
 * <AUTHOR>
 */
@Component
public class AutoRenewMetricHandler {

    @Resource
    private AutoRenewDutTypeDao autoRenewDutTypeDao;

    public void reportAsyncTaskData(String metricName, Map<String, Object> asyncTaskMap) {
        String taskFullName = MapUtils.getString(asyncTaskMap, "classname", null);
        if (StringUtils.isBlank(taskFullName)) {
            taskFullName = MapUtils.getString(asyncTaskMap, "className", null);
        }
        if (StringUtils.isBlank(taskFullName)) {
            return;
        }
        String taskName = ClassUtils.getShortClassName(taskFullName);
        if (StringUtils.isNotBlank(taskName) && (taskName.equals("AutoRenewConfirmTask") || "AutoRenewRefundTask".equals(taskName))) {
            return;
        }
        String taskData = MapUtils.getString(asyncTaskMap, "data", "");
        Map<String, String> taskDataMap = Splitter.on("&").withKeyValueSeparator("=").split(taskData);
        String taskType = MapUtils.getString(taskDataMap, "taskType", "unknown");
        String generatedBy = MapUtils.getString(taskDataMap, "generatedBy", "unknown");
        Integer vipType = MapUtils.getInteger(taskDataMap, "vipType", null);
        Integer dutType = getDutType(taskDataMap);
        String notifyCount = MapUtils.getString(asyncTaskMap, "notifyCount", "0");
        if (vipType == null) {
            vipType = autoRenewDutTypeDao.getVipTypeByDutType(dutType);
        }
        String agreementType = MapUtils.getString(taskDataMap, "agreementType", "1");
        String agreementNo = MapUtils.getString(taskDataMap, "agreementNo", "0");
        Tag taskNameTag = new ImmutableTag("taskName", taskName);
        Tag taskTypeTag = new ImmutableTag("taskType", taskType);
        Tag generatedByTag = new ImmutableTag("generatedBy", generatedBy);
        Tag dutTypeTag = new ImmutableTag("dutType", Objects.toString(dutType, "0"));
        Tag vipTypeTag = new ImmutableTag("vipType", Objects.toString(vipType, "0"));
        Tag agreementTypeTag = new ImmutableTag("agreementType", agreementType);
        Tag agreementNoTag = new ImmutableTag("agreementNo", agreementNo);
        Tag notifyCountTag = new ImmutableTag("notifyCount", notifyCount);
        List<Tag> tags = Arrays.asList(taskNameTag, taskTypeTag, agreementTypeTag, agreementNoTag, dutTypeTag, vipTypeTag, generatedByTag, notifyCountTag);
        Metrics.counter(metricName, tags).increment();
    }

    private Integer getDutType(Map<String, String> taskDataMap) {
        Integer dutBindType = MapUtils.getInteger(taskDataMap, "dutBindType", null);
        if (dutBindType != null) {
            return dutBindType;
        }
        return MapUtils.getInteger(taskDataMap, "dutType", null);
    }

}
