package com.iqiyi.vip.zeus.orderexport.mysqlio.handler;

import com.google.common.collect.Lists;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DutRenewLogHandler extends AbstractEventHandler {

    @Resource
    private AutoRenewDutTypeDao autoRenewDutTypeDao;

    @Override
    public void handCanalEvent(MapCanalEvent event) {
        String eventType = event.getEventType();
        Map<String, Object> rowAfter = event.getRowAfter();
        log.info("calc record count, eventType:{}, userId:{}", eventType, MapUtils.getLong(rowAfter, "user_id"));
        String agreementType = MapUtils.getString(rowAfter, "agreement_type", "1");
        String agreementNo = MapUtils.getString(rowAfter, "agreement_no", "0");
        String operateType = MapUtils.getString(rowAfter, "operate_type", "3");
        String vipType = MapUtils.getString(rowAfter, "vip_type", "1");
        String amount = MapUtils.getString(rowAfter, "amount", "1");
        String dutType = MapUtils.getString(rowAfter, "type", "0");
        String dutStatus = MapUtils.getString(rowAfter, "status", "0");
        boolean hasOrderCode = rowAfter.containsKey("order_code");
        String errorCode = MapUtils.getString(rowAfter, "error_code", "default");
        String thirdErrorCode = MapUtils.getString(rowAfter, "third_error_code", "null");
        Integer payChannel = autoRenewDutTypeDao.getPayChannelByDutType(Integer.valueOf(dutType));
        String description = MapUtils.getString(rowAfter, "description");
        Map<String, Object> descriptionMap = JacksonUtils.parseMap(description);
        String taskType = MapUtils.getString(descriptionMap, "taskType");

        Tag agreementTypeTag = new ImmutableTag("agreementType", agreementType);
        Tag agreementNoTag = new ImmutableTag("agreementNo", agreementNo);
        Tag operateTypeTag = new ImmutableTag("operateType", operateType);
        Tag vipTypeTag = new ImmutableTag("vipType", vipType);
        Tag amountTag = new ImmutableTag("amount", amount);
        Tag dutTypeTag = new ImmutableTag("dutType", dutType);
        Tag payChannelTag = new ImmutableTag("payChannel", Objects.toString(payChannel, "0"));
        Tag dutStatusTag = new ImmutableTag("dutStatus", dutStatus);
        Tag errorCodeTag = new ImmutableTag("errorCode", errorCode);
        Tag thirdErrorCodeTag = new ImmutableTag("thirdErrorCode", thirdErrorCode);
        Tag hasOrderCodeTag = new ImmutableTag("hasOrderCode", BooleanUtils.toStringTrueFalse(hasOrderCode));
        List<Tag> tags = Lists.newArrayList(agreementTypeTag, agreementNoTag, operateTypeTag, vipTypeTag, amountTag,
            dutTypeTag, payChannelTag, dutStatusTag, errorCodeTag, thirdErrorCodeTag, hasOrderCodeTag);
        if (StringUtils.isNotBlank(taskType)) {
            Tag taskTypeTag = new ImmutableTag("taskType", taskType);
            tags.add(taskTypeTag);
        }

        if (CanalEventUtil.isInsert(eventType)) {
            Metrics.counter("autorenew_renew_log_insert_total", tags).increment();
        }
        if (CanalEventUtil.isUpdate(eventType)) {
            Metrics.counter("autorenew_renew_log_update_total", tags).increment();
        }
    }

    @Override
    public String getTableName() {
        return "boss_dut_renew_log";
    }
}
