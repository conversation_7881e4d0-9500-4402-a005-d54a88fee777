package com.iqiyi.vip.zeus.orderexport.util;

/**
 * @Author: <PERSON>
 * @Date: 2022/08/15
 */
public class HashCodeUtils {

    public static long hashCode(String str) {
        char[] value = str.toCharArray();
        long hash = 0;
        long h = hash;
        if (h == 0 && value.length > 0) {
            char val[] = value;

            for (int i = 0; i < value.length; i++) {
                h = 31 * h + val[i];
            }
        }
        return h;
    }
}
