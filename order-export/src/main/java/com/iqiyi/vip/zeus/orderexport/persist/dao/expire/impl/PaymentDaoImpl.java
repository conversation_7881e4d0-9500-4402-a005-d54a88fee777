package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Payment;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PaymentDao;

/**
 * <AUTHOR>
 * @date 2024/4/8 20:29
 */
@Repository
@Profile("!sg")
public class PaymentDaoImpl implements PaymentDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<Payment> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(PaymentDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
