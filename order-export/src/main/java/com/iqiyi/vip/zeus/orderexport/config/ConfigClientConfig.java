package com.iqiyi.vip.zeus.orderexport.config;

import com.ctrip.framework.apollo.core.ConfigRegion;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.solar.config.client.CloudConfigChange;
import com.iqiyi.solar.config.client.CloudConfigService;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ConfigClientConfig {

    @Value("${cloud.config.app.name}")
    private String appName;
    @Value("${cloud.config.app.env}")
    private String appEnv;
    @Value("${cloud.config.app.region}")
    private String appRegion;

    @Bean(name="cloudConfig")
    public CloudConfig cloudConfig() {
        CloudConfig cloudConfig = CloudConfigService.builder()
            .withConfigRegion(ConfigRegion.fromRegion(StringUtils.defaultString(appRegion, ConfigRegion.DEFAULT.getRegion())))
            .withAppID(appName)
            .withEnv(appEnv)
            .build();
        cloudConfig.addChangeListener(cloudConfigEvent -> {
            for (String changedKey : cloudConfigEvent.changedKeys()) {
                CloudConfigChange change = cloudConfigEvent.getChange(changedKey);
                log.info("namespace: application changed, key = {}, old value = {}, new value = {}", changedKey, change.getOldValue(), change.getNewValue());
            }
        });
        return cloudConfig;
    }

}