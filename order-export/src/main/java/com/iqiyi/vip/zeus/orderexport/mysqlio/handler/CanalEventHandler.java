package com.iqiyi.vip.zeus.orderexport.mysqlio.handler;

import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEvent;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
public interface CanalEventHandler {

    /**
     * 获取该Handler处理的表名
     */
    String getTableName();

    /**
     * 判断是否可以处理改事件
     * @param event {@link CanalEvent}
     * @return boolean return true if this handler can handle the event.
     */
    boolean accept(MapCanalEvent event);

    /**
     * 处理mysql IO　事件
     * @param event　{@link CanalEvent}
     */
    void handCanalEvent(MapCanalEvent event);

}
