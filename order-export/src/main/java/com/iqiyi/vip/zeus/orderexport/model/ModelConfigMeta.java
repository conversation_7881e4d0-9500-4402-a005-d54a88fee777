package com.iqiyi.vip.zeus.orderexport.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.iqiyi.vip.zeus.core.model.FieldConfigMeta;

/**
 * @author: guojing
 * @date: 2023/12/26 16:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("模型配置元数据")
public class ModelConfigMeta {

    @ApiModelProperty(value = "模型名称")
    private String name;
    @ApiModelProperty(value = "模型字段配置列表")
    private List<FieldConfigMeta> fieldConfigMetaList;

}
