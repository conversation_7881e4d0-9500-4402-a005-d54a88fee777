package com.iqiyi.vip.zeus.orderexport.exception;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/5/22 12:03
 */
@AllArgsConstructor
public class NetWorkException extends RuntimeException {

    private final String errorCode;
    private final String errorMsg;

    public static final String TIMEOUT_EXCEPTION = "TIMEOUT_EXCEPTION";

    @Override
    public String toString() {
        return "NetWorkException{" +
                "errorCode='" + errorCode + '\'' +
                ", errorMsg='" + errorMsg + '\'' +
                '}';
    }
}