package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * Created at: 2021-02-20
 *
 * <AUTHOR>
 */
@Data
public class PresentRecord {

    /**
     * 购买uid
     */
    private String buyUid;
    /**
     * 领取uid
     */
    private String receiveUid;
    /**
     * 购买的会员类型
     */
    private String buyType;
    /**
     * 赠送的会员类型
     */
    private String presentType;
    private Timestamp createTime;
    private Timestamp updateTime;

}
