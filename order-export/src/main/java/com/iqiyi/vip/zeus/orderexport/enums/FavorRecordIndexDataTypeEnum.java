package com.iqiyi.vip.zeus.orderexport.enums;

/**
 * @Author: <PERSON>
 * @Date: 2022/08/15
 */
public enum FavorRecordIndexDataTypeEnum {

    UID(1, "uid"),
    PAY_ACCOUNT(2, "第三方支付账号"),
    DEVICE_ID(3, "设备号"),
    PHONE_NUM(4, "手机号"),
    PHONE_NUM_ENCODE(5, "手机号加密");


    private final Integer type;
    private final String desc;

    FavorRecordIndexDataTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
