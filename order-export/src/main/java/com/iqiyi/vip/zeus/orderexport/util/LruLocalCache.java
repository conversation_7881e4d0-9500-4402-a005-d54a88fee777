package com.iqiyi.vip.zeus.orderexport.util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <PERSON>
 * @Date: 2023/2/23
 */
@Component
public class LruLocalCache {
    private ConcurrentHashMap<String, String> FIRST_STEP_MAP = new ConcurrentHashMap<>();

    private Cache<String, String> LRU_LOCAL_CACHE;
    /**
     * 默认值
     */
    private static final String DEFAULT_VAL = "1";

    @Value("${cache.max.size}")
    private Integer maxCacheSize;
    @Value("${cache.expire.hours}")
    private Integer hours;

    @PostConstruct
    public void init() {
        /**
         * 最大500w数据，缓存6个小时;
         * 过期同时删除hashMap中的数据(remove listener)
         */
        LRU_LOCAL_CACHE = Caffeine.newBuilder().expireAfterWrite(hours, TimeUnit.HOURS)
            .maximumSize(maxCacheSize)
            .recordStats()
            .removalListener((RemovalListener<String, String>) (key, val, removalCause) -> FIRST_STEP_MAP.remove(key)).build();
    }

    /**
     * 判断是否concurrentHashMap中有数据(支持并发)
     *
     * @param key
     * @return
     */
    public boolean isExist(String key) {
        String val = FIRST_STEP_MAP.put(key, DEFAULT_VAL);
        // 不存在，则同时put cache
        if (StringUtils.isBlank(val)) {
            LRU_LOCAL_CACHE.put(key, DEFAULT_VAL);
        }
        return StringUtils.isNotBlank(val);
    }

    /**
     * 获取缓存统计信息
     *
     * @return
     */
    public CacheStats getCacheStats() {
        return LRU_LOCAL_CACHE.stats();
    }

    /**
     * @return
     */
    public Integer getMapSize() {
        return FIRST_STEP_MAP.size();
    }

}
