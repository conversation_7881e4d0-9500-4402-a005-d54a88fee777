package com.iqiyi.vip.zeus.orderexport.job;

import com.google.common.collect.Lists;
import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.zeus.orderexport.client.CommodityClient;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.component.UserService;
import com.iqiyi.vip.zeus.orderexport.entity.ExpiringPackage;
import com.iqiyi.vip.zeus.orderexport.entity.ExpiringRule;
import com.iqiyi.vip.zeus.orderexport.model.ExpiringRuleAndPackageReq;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.BasicDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.ExpiringPackageDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.ExpiringRuleDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.LevelDao;
import com.iqiyi.vip.zeus.orderexport.util.CloudConfigUtil;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailComponent;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailHeader;
import com.iqiyi.vip.zeus.orderexport.util.mail.TableMailContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2023/4/26 17:30
 */
@Slf4j
@Component
@Profile("!sg")
public class ExpiringRuleAndPackageMonitorJob extends IJobHandler {

    @ConfigJsonValue("${specialTime:[]}")
    private List<String> specialTime;
//    private List<String> specialTime = new ArrayList(){{add("05-06");}};
    @Resource
    MailComponent mailComponent;

    @Value("${rangeLeft}")
    String rangeLeft;

    @Value("${rangeRight}")
    String rangeRight;

    @Value("${specialRangeLeft}")
    String specialRangeLeft;

    @Value("${specialRangeRight}")
    String specialRangeRight;
    @Resource
    private ExpiringRuleDao expiringRuleDao;
    @Resource
    private ExpiringPackageDao expiringPackageDao;
    @Resource
    private BasicDao basicDao;
    @Resource
    private LevelDao levelDao;
    @Resource
    private UserService userService;
    @Resource
    private CommodityClient commodityClient;
    @Resource
    private AlterService alterService;
    @Resource
    private TemplateEngine templateEngine;
    private static final String[] DEFAULT_ADDRESSEES = new String[]{"<EMAIL>"};

    @Job("expiringRuleAndPackageMonitorJob")
    @Override
    public void execute() {
        StopWatch stopWatch = StopWatch.createStarted();
        String param = JobHelper.getJobParam();
        Long jobId = JobHelper.getJobId();
        log.info("---------- Start execute expiringDataMonitorJob[{}] ----------", jobId);
        JobHelper.log("---------- Start execute expiringDataMonitorJob[{0}] ----------", jobId);

        DateTimeFormatter df = DateTimeFormatter.ofPattern("MM-dd");
        LocalDateTime time = LocalDateTime.now();
        String cur = df.format(time);

        if (specialTime.contains(cur)) {
            specialTrigger(jobId, param);
        }
        commonTrigger(jobId, param);

        log.info("---------- expiringDataMonitorJob finished[{}]. cost:{}ms. ----------", jobId, stopWatch
            .getTime());
        JobHelper.log("---------- expiringDataMonitorJob finished[{0}], cost:{2}ms. ----------", jobId, stopWatch
            .getTime());
    }

    private void specialTrigger(Long jobId, String param) {
        log.info("---------- Start specialTrigger ----------");
        List<ExpiringRuleAndPackageReq> expiringRuleAndPackageReqList = CloudConfigUtil.getExpiringRuleAndPackageReqList();
        String[] addressees = StringUtils.isNotEmpty(param) ? param.split(",") : DEFAULT_ADDRESSEES;
        List<String> addresseesList = Arrays.asList(addressees);

        log.info("req:{}",expiringRuleAndPackageReqList);
        if (CollectionUtils.isEmpty(expiringRuleAndPackageReqList)) {
            log.info("expiringDataReqList is empty. jobId:{}", jobId);
        } else {
            for (ExpiringRuleAndPackageReq req : expiringRuleAndPackageReqList) {
                //规则
                if ("order".equals(req.getDatabase())) {
                    List<ExpiringRule> expiringRuleList = expiringRuleDao.querySpecialExpiringRule(specialRangeLeft, specialRangeRight);
                    Map<String, List<ExpiringRule>> map = new HashMap<>();
                    for (ExpiringRule r : expiringRuleList) {
                        if (r.getOperator() != null) {
                            r.setOperatorName(userService.getUserNameById(r.getOperator()));
                        }
                        if (r.getSkuId() != null) {
                            Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(r.getSkuId());
                            if (!skuResponseOptional.isPresent()) {
                                log.error("Query commodity by sku is not present. skuId:{}, ruleId:{}", r.getSkuId(), r.getId());
                            } else {
                                r.setSkuName(skuResponseOptional.get().getSkuName());
                            }
                        }
                        if (addresseesList.contains(r.getOperatorName() + "@qiyi.com")) {
                            continue;
                        }
                        if (map.containsKey(r.getOperatorName())) {
                            map.get(r.getOperatorName()).add(r);
                        } else {
                            map.put(r.getOperatorName(), new ArrayList<>());
                        }
                    }
                    sendRuleMail(req, expiringRuleList, addressees);
                    for (Map.Entry<String, List<ExpiringRule>> entry : map.entrySet()) {
                        sendRuleMail(req, entry.getValue(), new String[]{entry.getKey() + "@qiyi.com"});
                    }
                    //套餐
                } else if ("store".equals(req.getDatabase())) {
                    List<ExpiringPackage> expiringPackageList = expiringPackageDao.querySpecialExpiringPackage(specialRangeLeft, specialRangeRight);
                    Map<String, List<ExpiringPackage>> map = new HashMap<>();
                    for (ExpiringPackage p : expiringPackageList) {
                        p.setStore(basicDao.getNameByCode(p.getStoreCode()));
                        p.setLevel(levelDao.getMarkById(p.getLevelId()));
                        if (addresseesList.contains(p.getOperator() + "@qiyi.com")) {
                            continue;
                        }
                        if (map.containsKey(p.getOperator())) {
                            map.get(p.getOperator()).add(p);
                        } else {
                            map.put(p.getOperator(), new ArrayList<>());
                        }
                    }
                    sendPackageMail(req, expiringPackageList, addressees);
                    for (Map.Entry<String, List<ExpiringPackage>> entry : map.entrySet()) {
                        sendPackageMail(req, entry.getValue(), new String[]{entry.getKey() + "@qiyi.com"});
                    }
                }
            }
        }
    }

    private void sendRuleMail(ExpiringRuleAndPackageReq req, List<ExpiringRule> expiringRuleList, String[] addressees) {
        if (CollectionUtils.isEmpty(expiringRuleList)) {
            return;
        }
        List<List<Object>> tableContents = Lists.newArrayList();
        for (ExpiringRule expiringRule : expiringRuleList) {
            List<Object> oneRowData = Lists.newArrayList(
//"规则id", "规则名称", "商品", "定价策略","价格","优先级","状态","开始时间","结束时间","操作人","操作时间"
                String.valueOf(expiringRule.getId()),
                expiringRule.getName(),
                expiringRule.getSkuName(),
                expiringRule.getStrategy(),
                expiringRule.getStrategyValue(),
                expiringRule.getPriority(),
                expiringRule.getStatus(),
                expiringRule.getValidStartTime(),
                expiringRule.getValidEndTime(),
                expiringRule.getOperatorName(),
                expiringRule.getUpdateTime()
            );
            tableContents.add(oneRowData);
        }

        MailHeader mailHeader = new MailHeader();
        mailHeader.setTos(addressees);
        mailHeader.setTitle(String.format("【重要提醒】价格规则即将到期-%s", req.getModule()));
        mailHeader.setNeedTitlePrefix(false);

        String tableComment = "下列规则将在12.30--2.29过期，请产品和运营同学确认是否需要调整策略！！！";
        List<String> tableTitles = Lists.newArrayList("规则id", "规则名称", "商品", "定价策略", "价格", "优先级", "状态", "开始时间", "结束时间", "操作人", "操作时间");
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        log.info("[ExpiringDataMonitorJob] start send email");
        JobHelper.log("[ExpiringDataMonitorJob] start send email");

        try {
            mailComponent.sendMail(mailHeader, Collections.singletonList(mailContent));
            log.info("[ExpiringDataMonitorJob] send email finished, addressees:{}", Arrays.toString(addressees));
            JobHelper.log("[ExpiringDataMonitorJob] send email finished, addressees:{0}", Arrays.toString(addressees));
        } catch (Exception e) {
            log.error("[ExpiringDataMonitorJob] send mail occurred exception", e);
            JobHelper.log("[ExpiringDataMonitorJob] send mail occurred exception", e);
        }
    }

    private void sendPackageMail(ExpiringRuleAndPackageReq req, List<ExpiringPackage> expiringPackageList, String[] addressees) {
        if (CollectionUtils.isEmpty(expiringPackageList)) {
            return;
        }
        List<List<Object>> tableContents = Lists.newArrayList();
        for (ExpiringPackage expiringPackage : expiringPackageList) {
            List<Object> oneRowData = Lists.newArrayList(
//"策略ID", "策略名称", "收银台", "版本","活动层级","优先级","状态","开始日期","结束日期","操作人","操作时间"
                String.valueOf(expiringPackage.getId()),
                expiringPackage.getName(),
                expiringPackage.getStore(),
                expiringPackage.getVersion(),
                expiringPackage.getLevel(),
                expiringPackage.getPriority(),
                expiringPackage.getStatus(),
                expiringPackage.getValidStartTime(),
                expiringPackage.getValidEndTime(),
                expiringPackage.getOperator(),
                expiringPackage.getUpdateTime()
            );
            tableContents.add(oneRowData);
        }

        MailHeader mailHeader = new MailHeader();
        mailHeader.setTos(addressees);
        mailHeader.setTitle(String.format("【重要提醒】收银台套餐即将到期-%s", req.getModule()));
        mailHeader.setNeedTitlePrefix(false);

        String tableComment = "下列策略即将在12.30--2.29到期，请产品和运营同学确认是否需要调整策略！！！";
        List<String> tableTitles = Lists.newArrayList("策略ID", "策略名称", "收银台", "版本", "活动层级", "优先级", "状态", "开始日期", "结束日期", "操作人", "操作时间");
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        log.info("[ExpiringDataMonitorJob] start send email");
        JobHelper.log("[ExpiringDataMonitorJob] start send email");
        try {
            mailComponent.sendMail(mailHeader, Collections.singletonList(mailContent));
            log.info("[ExpiringDataMonitorJob] send email finished, addressees:{}", Arrays.toString(addressees));
            JobHelper.log("[ExpiringDataMonitorJob] send email finished, addressees:{0}", Arrays.toString(addressees));
        } catch (Exception e) {
            log.error("[ExpiringDataMonitorJob] send mail occurred exception", e);
            JobHelper.log("[ExpiringDataMonitorJob] send mail occurred exception", e);
        }
    }

    public void commonTrigger(Long jobId, String param) {
        List<ExpiringRuleAndPackageReq> expiringRuleAndPackageReqList = CloudConfigUtil.getExpiringRuleAndPackageReqList();
        String[] addressees = StringUtils.isNotEmpty(param) ? param.split(",") : DEFAULT_ADDRESSEES;

        List<String> addresseesList = Arrays.asList(addressees);
        StringBuilder address = new StringBuilder();
        for (String s : addressees) {
            address.append(s.substring(0, s.length() - 9)+",");
        }
        address.deleteCharAt(address.length() - 1);
        log.info("req:{}",expiringRuleAndPackageReqList);
        if (CollectionUtils.isEmpty(expiringRuleAndPackageReqList)) {
            log.info("expiringDataReqList is empty. jobId:{}", jobId);
        } else {
            for (ExpiringRuleAndPackageReq req : expiringRuleAndPackageReqList) {
                //规则
                if ("order".equals(req.getDatabase())) {
                    List<ExpiringRule> expiringRuleList = expiringRuleDao.queryExpiringRule(rangeLeft, rangeRight);
                    for (ExpiringRule r : expiringRuleList) {
                        if (r.getSkuId() != null) {
                            Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(r.getSkuId());
                            if (!skuResponseOptional.isPresent()) {
                                log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", r.getSkuId(), r.getId());
                            } else {
                                r.setSkuName(skuResponseOptional.get().getSkuName());
                            }
                        }
                        Context context = new Context();
                        context.setVariable("ruleId", r.getId());
                        context.setVariable("ruleName", r.getName());
                        context.setVariable("skuName", r.getSkuName());
                        context.setVariable("startTime", r.getValidStartTime());
                        context.setVariable("endTime", r.getValidEndTime());
                        context.setVariable("price", Integer.valueOf(r.getStrategyValue()) / 100);
                        context.setVariable("operator", r.getOperatorName());
                        String content = templateEngine.process("ruleMail", context);
                        Long cur = System.currentTimeMillis();
                        long diff = (r.getValidEndTime().getTime() - cur) / (1000 * 24 * 60 * 60);
                        String contentHotChat =
                            "您负责的数据（ID：" + r.getId() + r.getName() + "）将在" + diff + "天后到期，请登录奇悦系统，在交易管理-价格规则进行处理";
                        alterService.sendMailAndHotChat(r.getId(),null,address.toString(),content,contentHotChat,0);
                        if (!addresseesList.contains(r.getOperatorName() + "@qiyi.com")) {
                            alterService.sendMailAndHotChat(r.getId(), r.getOperator(), r.getOperatorName(), content, contentHotChat, 0);
                        }
                        log.info("send to id:{} addresses:{}", r.getOperator(), r.getOperatorName());
                    }
                    //套餐
                } else if ("store".equals(req.getDatabase())) {
                    List<ExpiringPackage> expiringPackageList = expiringPackageDao.queryExpiringPackage(rangeLeft, rangeRight);
                    for (ExpiringPackage p : expiringPackageList) {
                        p.setStore(basicDao.getNameByCode(p.getStoreCode()));
                        p.setLevel(levelDao.getMarkById(p.getLevelId()));
                        Context context = new Context();
                        context.setVariable("packageId", p.getId());
                        context.setVariable("packageName", p.getName());
                        context.setVariable("level", p.getLevel());
                        context.setVariable("startTime", p.getValidStartTime());
                        context.setVariable("endTime", p.getValidEndTime());
                        context.setVariable("operator", p.getOperator());
                        String content = templateEngine.process("packageMail", context);
                        Long cur = System.currentTimeMillis();
                        long diff = (p.getValidEndTime().getTime() - cur) / (1000 * 24 * 60 * 60);
                        String contentHotChat = "您负责的数据（ID：" + p.getId() + " " + p.getName() + "）将在" + diff
                            + "天后到期，请登录奇悦系统，在交易管理-价格规则进行处理";
                        alterService.sendMailAndHotChat(p.getId().longValue(), null, address.toString(), content, contentHotChat, 1);
                        if (!addresseesList.contains(p.getOperator() + "@qiyi.com")) {
                            alterService.sendMailAndHotChat(p.getId().longValue(), null, p.getOperator(), content, contentHotChat, 1);
                        }
                        log.info("send to addresses:{}", p.getOperator());
                    }
                }
            }
        }
    }
}
