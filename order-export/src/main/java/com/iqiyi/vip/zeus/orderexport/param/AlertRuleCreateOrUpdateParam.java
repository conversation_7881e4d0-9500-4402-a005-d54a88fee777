package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRuleCondition;

/**
 * @author: guojing
 * @date: 2023/12/20 11:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("创建或更新告警规则参数模型")
public class AlertRuleCreateOrUpdateParam {

    /**
     * 告警规则id
     */
    @ApiModelProperty(value = "告警规则id")
    private Integer id;
    /**
     * 监控id
     */
    @ApiModelProperty(value = "监控id")
    @NotNull(message = "监控id不能为空")
    private Integer monitorId;
    /**
     * 告警规则名称
     */
    @ApiModelProperty(value = "告警规则名称")
    @NotBlank(message = "告警规则名称不能为空")
    private String name;
    /**
     * 持续时间, 默认3m
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     * @see com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit
     */
    @ApiModelProperty(value = "持续时间,默认3m,格式：\"(number)(unit)\", 比如：\"1m\", 或者：\"0\". 合法的单位: s, m, h, d, w")
    @NotBlank(message = "持续时间不能为空")
    private String duration;
    /**
     * 检测频率, 默认1m
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     * @see com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit
     */
    @ApiModelProperty(value = "检测频率，默认1m,格式：\"(number)(unit)\", 比如：\"1m\", 或者：\"0\". 合法的单位: s, m, h, d, w")
    @NotBlank(message = "检测频率不能为空")
    private String checkFrequency;
    /**
     * 告警规则条件，多组之间为或关系
     */
    @ApiModelProperty(value = "告警规则条件")
    @NotEmpty(message = "告警规则条件不能为空")
    private List<ZeusAlertRuleCondition> conditions;
    /**
     * 告警级别
     */
    @ApiModelProperty(value = "告警级别")
    private String level;
    /**
     * 告警接收人，多个用逗号分隔
     */
    @ApiModelProperty(value = "告警接收人，多个用逗号分隔")
    private String receivers;
    /**
     * 对应鹰眼上的告警规则uid
     */
    @ApiModelProperty(value = "对应鹰眼上的告警规则uid")
    private String eagleUid;
    /**
     * 是否启用智能告警
     */
    @ApiModelProperty(value = "是否启用智能告警")
    private Boolean enableSmartAlert;

    public ZeusAlertRule toZeusAlertRule() {
        return ZeusAlertRule.builder()
            .id(id)
            .monitorId(monitorId)
            .name(name)
            .duration(duration)
            .checkFrequency(checkFrequency)
            .conditions(conditions)
            .level(level)
            .receivers(receivers)
            .eagleUid(eagleUid)
            .build();
    }

}
