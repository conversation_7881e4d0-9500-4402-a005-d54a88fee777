package com.iqiyi.vip.zeus.orderexport.persist.dao.coupon;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2022/2/23
 */
@Repository
@Profile("!sg")
public class CouponUsedDaoImpl implements CouponUsedDao {

    @Resource
    private SqlSessionTemplate couponSqlSessionTemplate;

    @Override
    public String  getCouponCodeByOrderCode(String orderCode) {
        return couponSqlSessionTemplate.getMapper(CouponUsedDao.class).getCouponCodeByOrderCode(orderCode);
    }

    @Override
    public List<String> getOrderCodeByCouponCode(String couponCode) {
        return couponSqlSessionTemplate.getMapper(CouponUsedDao.class).getOrderCodeByCouponCode(couponCode);
    }
}
