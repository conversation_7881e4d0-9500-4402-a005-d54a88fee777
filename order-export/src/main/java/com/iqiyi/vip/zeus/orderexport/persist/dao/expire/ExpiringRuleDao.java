package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringRule;

/**
 * <AUTHOR>
 * @date 2023/5/3 20:59
 */

public interface ExpiringRuleDao {

    @Select("select id,name,sku_id,strategy,strategy_value,priority,valid_start_time,"
        + "valid_end_time,operator,operator_name,update_Time,act_code,currency_type,origin_act_code "
        + "from qiyue_rule "
        + "where status =1 and uid_list is null "
        + "and valid_start_time<now() "
        + "and valid_end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<ExpiringRule> queryExpiringRule(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

    @Select("select  id,name,sku_id,strategy,strategy_value,priority,valid_start_time,"
        + "valid_end_time,operator,operator_name,update_Time "
        + "FROM qiyue_rule "
        + "where status =1 and uid_list is null "
        + "and valid_end_time>concat(YEAR(now()),#{rangeLeft}) "
        + "and valid_end_time<concat(YEAR(now())+1,#{rangeRight}) ")
    List<ExpiringRule> querySpecialExpiringRule(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);
}
