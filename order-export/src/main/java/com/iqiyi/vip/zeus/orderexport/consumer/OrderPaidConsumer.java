package com.iqiyi.vip.zeus.orderexport.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.handler.OrderPaidHandler;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;

/**
 * @Author: <PERSON>
 * @Date: 2022/08/16
 */
@Slf4j
@Component
public class OrderPaidConsumer extends BaseRMQConsumer {
    @Resource
    private OrderPaidHandler orderPaidHandler;

    @Override
    protected ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt messageExt : list) {
            String msgBody = null;
            try {
                msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                OrderDto orderDto = JSON.parseObject(msgBody, OrderDto.class);

                log.info("Consume order paid msg. msgId: {}, orderCode:{}", messageExt.getMsgId(), orderDto.getOrderCode());
                boolean isNotNeedHandle = orderPaidHandler.notNeedHandle(orderDto);
                if (isNotNeedHandle) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                orderPaidHandler.sendDelayMsg(orderDto.getOrderCode(), 1);
                orderPaidHandler.sendDelayMsgOnce(orderDto.getOrderCode());
            } catch (Exception e) {
                log.error("process order paid msg error. msgId:{}, msgBody:{}", messageExt.getMsgId(), msgBody, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
