package com.iqiyi.vip.zeus.orderexport.job;


import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.zeus.orderexport.persist.dao.present.VipPresentAsyncTaskDao;
import com.iqiyi.vip.zeus.orderexport.util.DateUtils;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;
import com.iqiyi.vip.repository.ClusterAsyncTask;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created at: 2021-09-27
 * 买赠服务异步任务监控Job
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class VipPresentAsyncTaskMonitorJob {

    /**
     * job默认执行间隔，单位：秒
     */
    private static final int EXEC_INTERVAL_SECONDS = 30;

    @Resource
    VipPresentAsyncTaskDao asyncTaskDao;

    @Job("vipPresentAsyncTaskMonitorJob")
    public void execute() throws Exception {
        String param = JobHelper.getJobParam();
        Long jobId = JobHelper.getJobId();
        log.info("---------- Start execute VipPresentAsyncTaskMonitorJob, jobId:{} ----------", jobId);
        JobHelper.log("---------- Start execute VipPresentAsyncTaskMonitorJob, jobId:{0} ----------", jobId);
        StopWatch stopWatch = StopWatch.createStarted();
        String execIntervalSecondsStr = param;
        int execIntervalSeconds = NumberUtils.toInt(execIntervalSecondsStr, EXEC_INTERVAL_SECONDS);
        Timestamp now = DateUtils.currentTimestamp();
        Timestamp nSecondsAgo = DateUtils.minusSeconds(now, execIntervalSeconds);

        List<ClusterAsyncTask> asyncTasks = asyncTaskDao.getAsyncTasks(nSecondsAgo, now);
        int asyncTaskCount = 0;
        for (ClusterAsyncTask asyncTask : asyncTasks) {
            if (StringUtils.isBlank(asyncTask.getData())) {
                continue;
            }
            Map<String, Object> taskData = JacksonUtils.parseMap(asyncTask.getData());
            Map<String, Object> presentOrder = (Map<String, Object>)MapUtils.getMap(taskData, "presentOrder");
            if (presentOrder == null || presentOrder.isEmpty()) {
                continue;
            }
            String buyVipType = MapUtils.getString(presentOrder, "buyType");
            String presentVipType = MapUtils.getString(presentOrder, "presentType");
            if (StringUtils.isBlank(buyVipType) || StringUtils.isBlank(presentVipType)) {
                continue;
            }

            String taskName = ClassUtils.getShortClassName(asyncTask.getClassName());
            Integer inQueue = asyncTask.getInQueue();
            Tag taskNameTag = new ImmutableTag("taskName", taskName);
            Tag buyVipTypeTag = new ImmutableTag("buyVipType", buyVipType);
            Tag presentVipTypeTag = new ImmutableTag("presentVipType", presentVipType);
            Tag inQueueTag = new ImmutableTag("inQueue", inQueue.toString());
            List<Tag> tags = Arrays.asList(taskNameTag, buyVipTypeTag, presentVipTypeTag, inQueueTag);
            Metrics.counter("vip_trade_present_async_task_total", tags).increment();

            asyncTaskCount++;
        }

        log.info("---------- VipPresentAsyncTaskMonitorJob finished, jobId:{}, asyncTaskCount:{}, cost:{}ms. ----------", JobHelper.getJobId(), asyncTaskCount, stopWatch.getTime());
        JobHelper.log("---------- VipPresentAsyncTaskMonitorJob finished, jobId:{0}, asyncTaskCount:{1}, cost:{2}ms. ----------", JobHelper.getJobId(), asyncTaskCount, stopWatch.getTime());
        JobHelper.handleSuccess();
    }

}
