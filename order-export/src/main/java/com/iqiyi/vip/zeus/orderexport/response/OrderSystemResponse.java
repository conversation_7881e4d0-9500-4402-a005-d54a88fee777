package com.iqiyi.vip.zeus.orderexport.response;

/**
 * <AUTHOR>
 * @date 2024/5/22 11:38
 */
import com.iqiyi.vip.zeus.orderexport.enums.OrderSystemResponseCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/22 11:25
 */
@NoArgsConstructor
public class OrderSystemResponse<D, E> {
    private OrderSystemResponseCode code;
    private D data;
    private E exception;

    private String msg;

    private OrderSystemResponse(OrderSystemResponseCode code, D data, E exception) {
        this.code = code;
        this.data = data;
        this.exception = exception;
        if (Objects.nonNull(exception)) {
            RuntimeException runtimeException = (RuntimeException) exception;
            msg = runtimeException.getMessage();
        }
    }

    @SuppressWarnings("unchecked")
    public static OrderSystemResponse<Void, Void> ok() {
        return OrderSystemResponse.ok(null);
    }

    public static <D> OrderSystemResponse ok(D data) {
        return new OrderSystemResponse<>(OrderSystemResponseCode.A00000, data, null);
    }

    public static <E> OrderSystemResponse failure(E exception) {
        return new OrderSystemResponse<>(OrderSystemResponseCode.FAILURE, null, exception);
    }

    public OrderSystemResponseCode getCode() {
        return code;
    }

    public D getData() {
        return data;
    }

    public E getException() {
        return exception;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}

