package com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorRecordDao;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/12
 */
@Repository
@Profile("!sg")
public class FavorRecordDaoImpl implements FavorRecordDao {

    @Resource
    SqlSessionTemplate promotionSqlSessionTemplate;

    @Override
    public List<Long> queryIdsByFavorIdAndFavorDataAndType(String tableName, Long favorId, String favorData, Integer indexDataType, String excludedOrderCode) {
        return promotionSqlSessionTemplate.getMapper(FavorRecordDao.class)
            .queryIdsByFavorIdAndFavorDataAndType(tableName, favorId, favorData, indexDataType, excludedOrderCode);
    }
}
