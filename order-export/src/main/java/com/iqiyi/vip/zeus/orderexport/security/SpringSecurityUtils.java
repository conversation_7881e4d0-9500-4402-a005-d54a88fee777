//package com.iqiyi.vip.zeus.orderexport.security;
//
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.context.SecurityContext;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.security.core.userdetails.UserDetails;
//
///**
// * @author: guojing
// * @date: 2023/12/13 10:11
// */
//public class SpringSecurityUtils {
//
//    public static Authentication getAuthentication() {
//        SecurityContext context = SecurityContextHolder.getContext();
//
//        if (context == null) {
//            return null;
//        }
//        return context.getAuthentication();
//    }
//
//    @SuppressWarnings("unchecked")
//    public static <T extends UserDetails> T getCurrentUser() {
//        Authentication authentication = getAuthentication();
//
//        if (authentication == null) {
//            return null;
//        }
//
//        Object principal = authentication.getPrincipal();
//        if (!(principal instanceof UserDetails)) {
//            return null;
//        }
//
//        return (T) principal;
//    }
//
//}
