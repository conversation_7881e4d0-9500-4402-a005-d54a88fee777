package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;

/**
 * @author: guojing
 * @date: 2023/12/20 11:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "创建或更新数据源参数模型")
public class DatasourceCreateOrUpdateParam {

    /**
     * 宙斯数据源ID
     */
    @ApiModelProperty(value = "数据源id")
    private Integer id;
    /**
     * 宙斯数据源名称
     * 具有唯一性
     */
    @ApiModelProperty(value = "数据源名称", required = true)
    @NotBlank(message = "数据源名称不能为空")
    private String name;
    /**
     * 宙斯数据源描述
     */
    @ApiModelProperty(value = "数据源描述")
    private String description;
    /**
     * 宙斯数据源类型
     * @see DataSourceType
     */
    @ApiModelProperty(value = "数据源类型", required = true)
    @NotBlank(message = "数据源类型不能为空")
    private String type;
    /**
     * 所属团队
     */
    @ApiModelProperty(value = "所属团队")
    @NotBlank(message = "团队不能为空")
    private String teamCode;
    /**
     * 数据源url
     */
    @ApiModelProperty(value = "数据源url", required = true)
    @NotBlank(message = "数据源url不能为空")
    private String url;
    /**
     * 扩展字段配置
     */
    @ApiModelProperty(value = "数据源扩展信息")
    private Map<String, Object> extraData;
    /**
     * 敏感字段配置
     */
    @ApiModelProperty(value = "数据源敏感字段信息")
    private Map<String, Object> sensitiveExtraData;

    public ZeusDatasource toZeusDatasource() {
        return ZeusDatasource.builder()
                .id(id)
                .name(name)
                .description(description)
                .type(type)
                .teamCode(teamCode)
                .url(url)
                .extraData(extraData)
                .sensitiveExtraData(sensitiveExtraData)
                .build();
    }

}
