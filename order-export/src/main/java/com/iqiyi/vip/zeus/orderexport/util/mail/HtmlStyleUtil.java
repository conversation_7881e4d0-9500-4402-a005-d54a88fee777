package com.iqiyi.vip.zeus.orderexport.util.mail;

import com.alibaba.fastjson.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * html样式工具类
 * @author: <PERSON>hangdaoguang Date: 2018/8/8 Time: 19:41
 */
public class HtmlStyleUtil {

    /**
     * 将表格内容列表转成html格式字符串
     *
     * @param tableMailContentList 表格内容列表
     * @return html形式的字符串
     */
    public static String tableHtml(List<TableMailContent> tableMailContentList) {
        StringBuilder mailContent = HtmlStyleUtil.getStyleTableStr();
        for (TableMailContent tableList : tableMailContentList) {
            if (StringUtils.isNotBlank(tableList.getTableComment())) {
                mailContent.append("<h3>")
                    .append(tableList.getTableComment())
                    .append("</h3>");
            }

            mailContent.append("<table class=bordered>");
            if (CollectionUtils.isNotEmpty(tableList.getTableTitles())) {
                //列头通过List-->JSON字符创-->List实现深度复制，目的是解决业务方列头复用，导致序号列出现重复
                List<String> tableTitleCols = JSON.parseObject(JSON.toJSONString(tableList.getTableTitles()), List.class);
                if (tableList.isNeedSequenceCol()) {
                    tableTitleCols.add(0,"序号");
                }
                mailContent.append("<thead>");
                mailContent.append("<tr>");
                for (String tableTitle : tableTitleCols) {
                    mailContent.append("<th>").append(tableTitle).append("</th>");
                }
                mailContent.append("</tr>");
                mailContent.append("</thead>");
            }

            if (CollectionUtils.isNotEmpty(tableList.getTableContents())) {
                int colNumber = 1;
                for (List<Object> tableContents : tableList.getTableContents()) {
                    mailContent.append("<tr>");
                    if (tableList.isNeedSequenceCol()) {
                        tableContents.add(0,Integer.valueOf(colNumber));
                    }
                    colNumber++;
                    for (Object cont : tableContents) {
                        mailContent.append("<td>").append(MailStringUtils.calNumberThousands(cont)).append("</td>");
                    }
                    mailContent.append("</tr>");
                }
            }
            mailContent.append("</table>");
            mailContent.append("</br>");
        }
        return mailContent.toString();
    }


    /**
     * html table表格样式
     */
    private static StringBuilder getStyleTableStr() {
        StringBuilder sb = new StringBuilder();
        sb.append("<style>\n"
            + "\n"
            + "body {\n"
            + "    width: 90%;\n"
            + "    margin: 20px auto;\n"
            + "    font-family: 'trebuchet MS', 'Lucida sans', Arial;\n"
            + "    font-size: 12px;\n"
            + "    color: #444;\n"
            + "}\n"
            + "\n"
            + "table {\n"
            + "    *border-collapse: collapse; /* IE7 and lower */\n"
            + "    border-spacing: 0;\n"
            + "    font-size: 14px;\n"
            + "    width: 100%;    \n"
            + "}\n"
            + "\n"
            + ".bordered {\n"
            + "    border: solid #ccc 1px;\n"
            + "    -moz-border-radius: 6px;\n"
            + "    -webkit-border-radius: 6px;\n"
            + "    border-radius: 6px;\n"
            + "    -webkit-box-shadow: 0 1px 1px #ccc; \n"
            + "    -moz-box-shadow: 0 1px 1px #ccc; \n"
            + "    box-shadow: 0 1px 1px #ccc;         \n"
            + "}\n"
            + "\n"
            + ".bordered tr:hover {\n"
            + "    background: #fbf8e9;\n"
            + "    -o-transition: all 0.1s ease-in-out;\n"
            + "    -webkit-transition: all 0.1s ease-in-out;\n"
            + "    -moz-transition: all 0.1s ease-in-out;\n"
            + "    -ms-transition: all 0.1s ease-in-out;\n"
            + "    transition: all 0.1s ease-in-out;     \n"
            + "}    \n"
            + "    \n"
            + ".bordered td, .bordered th {\n"
            + "    border-left: 1px solid #ccc;\n"
            + "    border-top: 1px solid #ccc;\n"
            + "    padding: 10px;\n"
            + "    text-align: left;    \n"
            + "}\n"
            + "\n"
            + ".bordered th {\n"
            + "    background-color: #dce9f9;\n"
            + "    background-image: -webkit-gradient(linear, left top, left bottom, from(#ebf3fc), to(#dce9f9));\n"
            + "    background-image: -webkit-linear-gradient(top, #ebf3fc, #dce9f9);\n"
            + "    background-image:    -moz-linear-gradient(top, #ebf3fc, #dce9f9);\n"
            + "    background-image:     -ms-linear-gradient(top, #ebf3fc, #dce9f9);\n"
            + "    background-image:      -o-linear-gradient(top, #ebf3fc, #dce9f9);\n"
            + "    background-image:         linear-gradient(top, #ebf3fc, #dce9f9);\n"
            + "    -webkit-box-shadow: 0 1px 0 rgba(255,255,255,.8) inset; \n"
            + "    -moz-box-shadow:0 1px 0 rgba(255,255,255,.8) inset;  \n"
            + "    box-shadow: 0 1px 0 rgba(255,255,255,.8) inset;        \n"
            + "    border-top: none;\n"
            + "    text-shadow: 0 1px 0 rgba(255,255,255,.5); \n"
            + "}\n"
            + "\n"
            + ".bordered td:first-child, .bordered th:first-child {\n"
            + "    border-left: none;\n"
            + "}\n"
            + "\n"
            + ".bordered th:first-child {\n"
            + "    -moz-border-radius: 6px 0 0 0;\n"
            + "    -webkit-border-radius: 6px 0 0 0;\n"
            + "    border-radius: 6px 0 0 0;\n"
            + "}\n"
            + "\n"
            + ".bordered th:last-child {\n"
            + "    -moz-border-radius: 0 6px 0 0;\n"
            + "    -webkit-border-radius: 0 6px 0 0;\n"
            + "    border-radius: 0 6px 0 0;\n"
            + "}\n"
            + "\n"
            + ".bordered th:only-child{\n"
            + "    -moz-border-radius: 6px 6px 0 0;\n"
            + "    -webkit-border-radius: 6px 6px 0 0;\n"
            + "    border-radius: 6px 6px 0 0;\n"
            + "}\n"
            + "\n"
            + ".bordered tr:last-child td:first-child {\n"
            + "    -moz-border-radius: 0 0 0 6px;\n"
            + "    -webkit-border-radius: 0 0 0 6px;\n"
            + "    border-radius: 0 0 0 6px;\n"
            + "}\n"
            + "\n"
            + ".bordered tr:last-child td:last-child {\n"
            + "    -moz-border-radius: 0 0 6px 0;\n"
            + "    -webkit-border-radius: 0 0 6px 0;\n"
            + "    border-radius: 0 0 6px 0;\n"
            + "}\n"
            + "\n"
            + "\n"
            + "\n"
            + "/*----------------------*/\n"
            + "\n"
            + ".zebra td, .zebra th {\n"
            + "    padding: 10px;\n"
            + "    border-bottom: 1px solid #f2f2f2;    \n"
            + "}\n"
            + "\n"
            + ".zebra tbody tr:nth-child(even) {\n"
            + "    background: #f5f5f5;\n"
            + "    -webkit-box-shadow: 0 1px 0 rgba(255,255,255,.8) inset; \n"
            + "    -moz-box-shadow:0 1px 0 rgba(255,255,255,.8) inset;  \n"
            + "    box-shadow: 0 1px 0 rgba(255,255,255,.8) inset;        \n"
            + "}\n"
            + "\n"
            + ".zebra th {\n"
            + "    text-align: left;\n"
            + "    text-shadow: 0 1px 0 rgba(255,255,255,.5); \n"
            + "    border-bottom: 1px solid #ccc;\n"
            + "    background-color: #eee;\n"
            + "    background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), to(#eee));\n"
            + "    background-image: -webkit-linear-gradient(top, #f5f5f5, #eee);\n"
            + "    background-image:    -moz-linear-gradient(top, #f5f5f5, #eee);\n"
            + "    background-image:     -ms-linear-gradient(top, #f5f5f5, #eee);\n"
            + "    background-image:      -o-linear-gradient(top, #f5f5f5, #eee); \n"
            + "    background-image:         linear-gradient(top, #f5f5f5, #eee);\n"
            + "}\n"
            + "\n"
            + ".zebra th:first-child {\n"
            + "    -moz-border-radius: 6px 0 0 0;\n"
            + "    -webkit-border-radius: 6px 0 0 0;\n"
            + "    border-radius: 6px 0 0 0;  \n"
            + "}\n"
            + "\n"
            + ".zebra th:last-child {\n"
            + "    -moz-border-radius: 0 6px 0 0;\n"
            + "    -webkit-border-radius: 0 6px 0 0;\n"
            + "    border-radius: 0 6px 0 0;\n"
            + "}\n"
            + "\n"
            + ".zebra th:only-child{\n"
            + "    -moz-border-radius: 6px 6px 0 0;\n"
            + "    -webkit-border-radius: 6px 6px 0 0;\n"
            + "    border-radius: 6px 6px 0 0;\n"
            + "}\n"
            + "\n"
            + ".zebra tfoot td {\n"
            + "    border-bottom: 0;\n"
            + "    border-top: 1px solid #fff;\n"
            + "    background-color: #f1f1f1;  \n"
            + "}\n"
            + "\n"
            + ".zebra tfoot td:first-child {\n"
            + "    -moz-border-radius: 0 0 0 6px;\n"
            + "    -webkit-border-radius: 0 0 0 6px;\n"
            + "    border-radius: 0 0 0 6px;\n"
            + "}\n"
            + "\n"
            + ".zebra tfoot td:last-child {\n"
            + "    -moz-border-radius: 0 0 6px 0;\n"
            + "    -webkit-border-radius: 0 0 6px 0;\n"
            + "    border-radius: 0 0 6px 0;\n"
            + "}\n"
            + "\n"
            + ".zebra tfoot td:only-child{\n"
            + "    -moz-border-radius: 0 0 6px 6px;\n"
            + "    -webkit-border-radius: 0 0 6px 6px\n"
            + "    border-radius: 0 0 6px 6px\n"
            + "}\n"
            + "  \n"
            + "</style>");
        return sb;
    }
}
