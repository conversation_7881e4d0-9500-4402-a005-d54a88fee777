package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.BasicDao;

/**
 * <AUTHOR>
 * @date 2023/5/4 19:11
 */
@Repository
@Profile("!sg")
public class BasicDaoImpl implements BasicDao {

    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public String getNameByCode(String code) {
        return storeSqlSessionTemplate.getMapper(BasicDao.class).getNameByCode(code);
    }
}
