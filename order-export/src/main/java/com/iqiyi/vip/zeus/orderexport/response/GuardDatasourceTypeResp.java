package com.iqiyi.vip.zeus.orderexport.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.GuardDataQueryType;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;

/**
 * 数据源类型响应对象
 * 包含数据源类型的所有字段信息
 *
 * <AUTHOR>
 * @date 2025-01-27 10:30:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GuardDatasourceTypeResp {
    
    /**
     * 数据源类型名称
     */
    private String value;
    
    /**
     * 数据查询类型
     */
    private GuardDataQueryType queryType;
    
    /**
     * 连接URL示例
     */
    private String connUrlDemo;
    
    /**
     * 连接配置示例
     */
    private Map<String, Object> connConfigDemo;
    
    /**
     * 从枚举转换为响应对象
     */
    public static GuardDatasourceTypeResp fromEnum(GuardDatasourceType type) {
        return new GuardDatasourceTypeResp(
            type.getValue(),
            type.getQueryType(),
            type.getConnUrlDemo(),
            JacksonUtils.parseMap(type.getConnConfigDemo())
        );
    }
}
