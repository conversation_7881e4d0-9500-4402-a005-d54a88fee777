package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.constants.DatasourceConstants;
import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MonitorCategory;
import com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.DashboardDisplayInfo;
import com.iqiyi.vip.zeus.core.model.SmartAlertRule;
import com.iqiyi.vip.zeus.core.model.TianyanMonitorDetail;
import com.iqiyi.vip.zeus.core.model.TimeIntervalPair;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.req.DashboardCreateParam;
import com.iqiyi.vip.zeus.core.service.EagleDashboardService;
import com.iqiyi.vip.zeus.core.service.EagleFolderService;
import com.iqiyi.vip.zeus.core.service.SmartAlertRuleService;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorAssembleService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.FolderSimpleInfo;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.constant.Constant;
import com.iqiyi.vip.zeus.orderexport.param.TianyanMonitorSaveParam;
import com.iqiyi.vip.zeus.orderexport.param.TianyanSmartAlertSaveParam;
import com.iqiyi.vip.zeus.orderexport.validator.monitor.MonitorConfigValidatorFactory;

/**
 * @author: guojing
 * @date: 2025/3/4 22:11
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/monitor/tianyan")
@Api(tags = "天眼业务指标监控相关接口")
public class TianyanMonitorController {

    private static final String TIANYAN_DASHBOARD = "天眼业务指标监控";

    @Resource
    private ZeusMonitorService zeusMonitorService;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;
    @Resource
    private EagleDashboardService eagleDashboardService;
    @Resource
    private MonitorConfigValidatorFactory monitorConfigValidatorFactory;
    @Resource
    private ZeusMonitorAssembleService monitorAssembleService;
    @Resource
    private EagleFolderService eagleFolderService;
    @Resource
    private SmartAlertRuleService smartAlertRuleService;

    /**
     * 创建监控
     */
    @ApiOperation(value = "创建监控")
    @PostMapping(value = "/create")
    public BaseResponse<Integer> create(@Validated @RequestBody TianyanMonitorSaveParam createParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        if (createParam.getId() != null) {
            throw BizException.newParamException("无需指定监控id");
        }
        ZeusDatasource prometheusDatasource = zeusDatasourceService.getByName(DatasourceConstants.DATASOURCE_PROMETHEUS);
        if (prometheusDatasource == null || prometheusDatasource.invalid()) {
            throw BizException.newParamException("依赖的数据源不存在");
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(prometheusDatasource.getType());
        monitorConfigValidatorFactory.getValidator(dataSourceType).checkConfig(createParam);

        String dashboardName = TIANYAN_DASHBOARD + "-" + createParam.getThemeTypeName() + "-" + createParam.getTargetName();
        FolderSimpleInfo folderSimpleInfo = eagleFolderService.getOrCreate(realTeam.getCnName());
        String dashboardUid = eagleDashboardService.exist(folderSimpleInfo.getId(), dashboardName);
        if (StringUtils.isBlank(dashboardUid)) {
            DashboardCreateParam dashboardCreateParam = new DashboardCreateParam(dashboardName);
            DashboardDisplayInfo dashboardDisplayInfo = eagleDashboardService.create(dashboardCreateParam);
            dashboardUid = dashboardDisplayInfo.getUid();
        }
        DashboardWithMeta dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(dashboardUid);
        if (dashboardWithMeta == null) {
            throw BizException.newParamException("监控Dashboard不存在");
        }

        ZeusMonitor createMonitorData = createParam.toZeusMonitor();
        createMonitorData.setCategory(MonitorCategory.DATA_TREND.getValue());
        createMonitorData.setDatasourceId(prometheusDatasource.getId());
        createMonitorData.setDashboardUid(dashboardUid);
        createMonitorData.setTeamCode(realTeam.getTeamCode());
        createMonitorData.setCreateUser(currentUser.getOaAccount());
        createMonitorData.setUpdateUser(currentUser.getOaAccount());
        createMonitorData.setExtraDataItem(ZeusMonitor.NOT_CREATE_RECORD, true);

        SmartAlertRule createSmartAlertRule = null;
        TianyanSmartAlertSaveParam createSmartAlertParam = createParam.getSmartAlertParam();
        if (createSmartAlertParam != null) {
            checkSmartAlertParam(createSmartAlertParam);
            createSmartAlertRule = createSmartAlertParam.toSmartAlertRule();
            String receivers = StringUtils.isNotBlank(createSmartAlertParam.getReceivers())
                ? createSmartAlertParam.getReceivers()
                : createMonitorData.getCreateUser();
            createSmartAlertParam.setReceivers(receivers);
        }

        Integer monitorId = monitorAssembleService.createMonitorAndAlertRuleFromTianyan(createMonitorData, createSmartAlertRule, prometheusDatasource, dashboardWithMeta);
        if (monitorId == null) {
            return BaseResponse.createSystemError("监控创建失败");
        }
        return BaseResponse.createSuccess(monitorId);
    }

    private void checkSmartAlertParam(TianyanSmartAlertSaveParam smartAlertSaveParam) {
        String durationStr = smartAlertSaveParam.getDuration();
        if (StringUtils.isNotBlank(durationStr)) {
            TimeIntervalPair timeIntervalPair = TimeIntervalPair.of(durationStr);
            if (timeIntervalPair == null) {
                throw BizException.newParamException("告警持续时间取值不合法，取值范围:[1m,1h]");
            }
            int seconds = OffsetTimeUnit.toSeconds(durationStr);
            if (!timeIntervalPair.minuteUnit() && !timeIntervalPair.hourUnit() && (seconds < 60 || seconds > Constant.ONE_DAY_IN_SECONDS)) {
                throw BizException.newParamException("告警持续时间取值不合法，取值范围:[1m,1d]");
            }
        }

        String checkFrequencyStr = smartAlertSaveParam.getCheckFrequency();
        if (StringUtils.isNotBlank(checkFrequencyStr)) {
            TimeIntervalPair timeIntervalPair = TimeIntervalPair.of(checkFrequencyStr);
            if (timeIntervalPair == null) {
                throw BizException.newParamException("告警持续时间取值不合法");
            }
        }
    }

    private void checkSmartAlertParamWhenUpdate(TianyanSmartAlertSaveParam smartAlertSaveParam) {
        checkSmartAlertParam(smartAlertSaveParam);
        SmartAlertRule smartAlertRuleFromDB = smartAlertRuleService.selectByMonitorId(smartAlertSaveParam.getMonitorId());
        if (smartAlertRuleFromDB != null) {
            smartAlertSaveParam.setId(smartAlertRuleFromDB.getId());
            if (!Objects.equals(smartAlertSaveParam.getRuleName(), smartAlertRuleFromDB.getRuleName())) {
                throw BizException.newParamException("智能告警规则名称不能修改");
            }
        }
    }

    @ApiOperation(value = "更新监控")
    @PostMapping(value = "/update")
    public BaseResponse<Boolean> update(@Validated @RequestBody TianyanMonitorSaveParam updateParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        if (updateParam.getId() == null) {
            throw BizException.newParamException("监控id不能为空");
        }
        ZeusMonitor monitorFromDB = zeusMonitorService.getById(updateParam.getId());
        if (monitorFromDB == null) {
            throw BizException.newParamException("监控不存在");
        }
        ZeusDatasource prometheusDatasource = zeusDatasourceService.getByName(DatasourceConstants.DATASOURCE_PROMETHEUS);
        if (prometheusDatasource == null || prometheusDatasource.invalid()) {
            throw BizException.newParamException("依赖的数据源不存在");
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(prometheusDatasource.getType());
        monitorConfigValidatorFactory.getValidator(dataSourceType).checkConfig(updateParam);

        String dashboardName = TIANYAN_DASHBOARD + "-" + updateParam.getThemeTypeName() + "-" + updateParam.getTargetName();
        FolderSimpleInfo folderSimpleInfo = eagleFolderService.getOrCreate(realTeam.getCnName());
        String dashboardUid = eagleDashboardService.exist(folderSimpleInfo.getId(), dashboardName);
        if (!Objects.equals(dashboardUid, monitorFromDB.getDashboardUid())) {
            throw BizException.newParamException("不能修改监控主题名称(修改指标名称等)");
        }
        DashboardWithMeta dashboardWithMeta = eagleDashboardService.getByUidWithoutCache(monitorFromDB.getDashboardUid());
        if (dashboardWithMeta == null) {
            throw BizException.newParamException("监控Dashboard不存在");
        }

        ZeusMonitor updateMonitorData = updateParam.toZeusMonitor();
        updateMonitorData.setUpdateUser(currentUser.getOaAccount());

        SmartAlertRule updateSmartAlertRule = null;
        TianyanSmartAlertSaveParam updateSmartAlertParam = updateParam.getSmartAlertParam();
        if (updateSmartAlertParam != null) {
            updateSmartAlertParam.setMonitorId(updateParam.getId());
            checkSmartAlertParamWhenUpdate(updateSmartAlertParam);
            updateSmartAlertRule = updateSmartAlertParam.toSmartAlertRule();
            String receivers = StringUtils.isNotBlank(updateSmartAlertRule.getReceivers())
                ? updateSmartAlertRule.getReceivers()
                : updateMonitorData.getUpdateUser();
            updateSmartAlertParam.setReceivers(receivers);
        }

        boolean updated = monitorAssembleService.updateMonitorAndAlertRuleFromTianyan(updateMonitorData, updateSmartAlertRule, monitorFromDB, dashboardWithMeta);
        return BaseResponse.createSuccess(updated);
    }

    @ApiOperation(value = "删除监控")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "监控id", dataType = "Integer", required = true, paramType = "query")})
    @PostMapping(value = "/delete")
    public BaseResponse<Boolean> delete(@NotNull(message = "监控ID不能为空") Integer id) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        ZeusMonitor monitorFromDB = zeusMonitorService.getById(id);
        if (monitorFromDB == null) {
            throw BizException.newParamException("监控不存在");
        }
        if (!Objects.equals(monitorFromDB.getTeamCode(), realTeam.getTeamCode())) {
            throw BizException.newParamException("无权操作此监控");
        }
        return BaseResponse.createSuccess(monitorAssembleService.deleteTianyanMonitor(monitorFromDB));
    }

    @ApiOperation(value = "根据id查询监控详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "监控id", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getDetailById")
    public BaseResponse<TianyanMonitorDetail> getDetailById(@NotNull(message = "监控ID不能为空") Integer id) {
        ZeusMonitor monitor = zeusMonitorService.getFriendlyMonitorById(id);
        if (monitor == null) {
            return BaseResponse.createSuccess(null);
        }
        TianyanMonitorDetail result = new TianyanMonitorDetail();
        BeanUtils.copyProperties(monitor, result);
        SmartAlertRule smartAlertRule = smartAlertRuleService.selectByMonitorId(monitor.getId());
        if (smartAlertRule != null) {
            result.setSmartAlertRule(smartAlertRule);
        }
        return BaseResponse.createSuccess(result);
    }

    @ApiOperation(value = "根据智能告警规则名称判断监控是否存在")
    @ApiImplicitParams({@ApiImplicitParam(name = "smartAlertRuleName", value = "智能告警规则名称", dataType = "String", required = true, paramType = "query")})
    @GetMapping(value = "/existMonitorByRuleName")
    public BaseResponse<Integer> existMonitorByRuleName(@NotNull(message = "智能告警规则名称不能为空") String smartAlertRuleName) {
        SmartAlertRule smartAlertRule = smartAlertRuleService.selectByRuleName(smartAlertRuleName);
        return BaseResponse.createSuccess(smartAlertRule != null ? smartAlertRule.getMonitorId() : null);
    }

}
