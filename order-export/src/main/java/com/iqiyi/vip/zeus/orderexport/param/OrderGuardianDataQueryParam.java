package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: guojing
 * @date: 2025/6/16 20:27
 */
@Data
@ApiModel("订单稽核查询参数")
public class OrderGuardianDataQueryParam {

    /**
     * 订单稽核监控项id
     */
    @ApiModelProperty(value = "订单稽核监控项id")
    @NotNull(message = "订单稽核监控项id不能为空")
    private Integer monitorId;
    /**
     * 查询日期，格式：yyyy-MM-dd
     */
    @ApiModelProperty(value = "查询日期，格式：yyyy-MM-dd")
    @NotBlank(message = "查询日期不能为空")
    private String queryDay;
    /**
     * 按第一列分组后，每个分组下展示最大行数
     */
    @ApiModelProperty(value = "按第一列分组后，每组展示最大行数，默认100行")
    private Integer displayMaxRowsPerGroup;

}
