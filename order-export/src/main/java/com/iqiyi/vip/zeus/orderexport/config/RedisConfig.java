package com.iqiyi.vip.zeus.orderexport.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import com.iqiyi.smartredis.conn.manager.SmartRedisConnectionFactory;

/**
 * @Author: <PERSON> P<PERSON>hui
 * @Date: 2022/7/19
 */
@Configuration
public class RedisConfig {

    @Bean
    public SmartRedisConnectionFactory smartRedisConnectionFactory() {
        return new SmartRedisConnectionFactory("xuanwu");
    }

    @Bean
    @ConditionalOnSingleCandidate(SmartRedisConnectionFactory.class)
    public RedisTemplate<String, Object> redisTemplate(SmartRedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        return redisTemplate;
    }

    @Bean
    @ConditionalOnSingleCandidate(SmartRedisConnectionFactory.class)
    public StringRedisTemplate stringRedisTemplate(SmartRedisConnectionFactory redisConnectionFactory) {
        return new StringRedisTemplate(redisConnectionFactory);
    }

}
