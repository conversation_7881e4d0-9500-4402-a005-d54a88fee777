package com.iqiyi.vip.zeus.orderexport.component.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


import com.qiyi.ocm.common.http.HttpClient;
import com.qiyi.ocm.common.result.DataResult;
import com.iqiyi.vip.zeus.orderexport.component.UserService;
import com.iqiyi.vip.zeus.orderexport.model.AuthUserVO;

/**
 * <AUTHOR>
 * @date 2023/5/4 16:54
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    private static final String url = "http://admin.vip.qiyi.domain/ops-auth/authority/user/queryById";
    @Resource
    private HttpClient ocmHttpClient;

    @Override
    public String getUserNameById(Long userId) {
        Map<String, Object> param = new HashMap<>();
        param.put("userId", userId.intValue());
        param.put("systemCode", "qiyue");
        try {
            DataResult<AuthUserVO> dataResult = ocmHttpClient.getDataResult(url, param, AuthUserVO.class);
            if ("A00000".equals(dataResult.getCode()) && dataResult.getData() != null && null != dataResult.getData()) {
                return dataResult.getData().getName();
            }
        } catch (Exception e) {
            log.info("queryById error {}", e);
        }
        return null;
    }
}
