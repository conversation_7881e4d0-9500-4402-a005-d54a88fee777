package com.iqiyi.vip.zeus.orderexport.mysqlio;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/24
 */
public final class CanalEventUtil {

    private static final String[] DATE_TIME_FIELDS = new String[] { "create_time", "modify_time", "pay_time",
            "begin_time", "update_time", "valid_time", "start_time" };

    public static boolean isInsert(String eventType) {
        return "INSERT".equals(eventType);
    }

    public static boolean isUpdate(String eventType) {
        return "UPDATE".equals(eventType);
    }

    public static boolean isDelete(String eventType) {
        return "DELETE".equals(eventType);
    }
}
