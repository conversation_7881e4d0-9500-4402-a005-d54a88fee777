package com.iqiyi.vip.zeus.orderexport.job;

import com.google.common.collect.Lists;
import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.zeus.orderexport.entity.AutoRenewDutType;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;
import com.iqiyi.vip.zeus.orderexport.util.CloudConfigUtil;
import com.iqiyi.vip.zeus.orderexport.util.DateUtils;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailComponent;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailHeader;
import com.iqiyi.vip.zeus.orderexport.util.mail.TableMailContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/8/17 15:12
 */
@Slf4j
@Component
public class DutTypeExpireMonitorJob extends IJobHandler {

    private static final int DEFAULT_DAYS = 30;
    private static final String[] DEFAULT_ADDRESSEES = new String[]{"<EMAIL>"};

    @Resource
    private AutoRenewDutTypeDao autoRenewDutTypeDao;
    @Resource
    private MailComponent mailComponent;

    @Job("dutTypeExpireMonitorJob")
    public void execute() throws Exception {
        StopWatch stopWatch = StopWatch.createStarted();
        String param = JobHelper.getJobParam();
        Long jobId = JobHelper.getJobId();
        log.info("---------- Start execute DutTypeExpireMonitorJob[{}] ----------", jobId);
        JobHelper.log("---------- Start execute DutTypeExpireMonitorJob[{0}] ----------", jobId);
        List<AutoRenewDutType> autoRenewDutTypes = autoRenewDutTypeDao.selectValidEndTimeNotNullRecords();
        if (CollectionUtils.isEmpty(autoRenewDutTypes)) {
            log.info("---------- DutTypeExpireMonitorJob[{}], will expired dutType count: 0, cost:{}ms", jobId, stopWatch.getTime());
            JobHelper.log("---------- DutTypeExpireMonitorJob[{0}], will expired dutType count: 0, cost:{1}ms", jobId, stopWatch.getTime());
            return;
        }

        List<Integer> remindDaysBeforeDutTypeExpire = CloudConfigUtil.remindDaysBeforeDutTypeExpire();
        if (CollectionUtils.isEmpty(remindDaysBeforeDutTypeExpire)) {
            log.warn("---------- DutTypeExpireMonitorJob[{}] not config remind days ----------", jobId);
            JobHelper.log("---------- DutTypeExpireMonitorJob[{0}] not config remind days ----------", jobId);
            remindDaysBeforeDutTypeExpire = Collections.singletonList(DEFAULT_DAYS);
        }

        List<AutoRenewDutType> willExpiredDutTypes = new ArrayList<>();
        for (AutoRenewDutType autoRenewDutType : autoRenewDutTypes) {
            boolean willExpire = willExpire(autoRenewDutType, remindDaysBeforeDutTypeExpire);
            if (!willExpire) {
                continue;
            }
            willExpiredDutTypes.add(autoRenewDutType);
        }
        String[] addressees = StringUtils.isNotEmpty(param)? param.split(",") : DEFAULT_ADDRESSEES;
        sendMail(willExpiredDutTypes, addressees);

        int willExpiredCount = willExpiredDutTypes.size();
        log.info("---------- DutTypeExpireMonitorJob finished[{}], will expired dutType count:{}, cost:{}ms. ----------", jobId, willExpiredCount, stopWatch
            .getTime());
        JobHelper.log("---------- DutTypeExpireMonitorJob finished[{0}], will expired dutType count:{1}, cost:{2}ms. ----------", jobId, willExpiredCount, stopWatch
            .getTime());
        return;
    }

    /**
     * dutType是否即将到期
     */
    private boolean willExpire(AutoRenewDutType autoRenewDutType, List<Integer> remindDaysBeforeDutTypeExpire) {
        Timestamp currentTime = DateUtils.currentTimestamp();
        Timestamp validEndTime = autoRenewDutType.getValidEndTime();
        if (validEndTime == null || validEndTime.before(currentTime)) {
            return false;
        }
        Instant validEndTimeInstant = Instant.ofEpochMilli(validEndTime.getTime());
        long days = ChronoUnit.DAYS.between(Instant.now(), validEndTimeInstant);
        boolean willExpire = false;
        for (Integer remindDay : remindDaysBeforeDutTypeExpire) {
            //距过期时间超过15天时，每个时间点只提醒一次，否则每天提醒
            if (days > 15) {
                if (days == remindDay) {
                    willExpire = true;
                    break;
                }
            } else {
                if (days <= remindDay) {
                    willExpire = true;
                    break;
                }
            }
        }
        return willExpire;
    }

    private void sendMail(List<AutoRenewDutType> willExpiredDutTypes, String[] addressees) {
        if (CollectionUtils.isEmpty(willExpiredDutTypes)) {
            return;
        }
        willExpiredDutTypes.sort(Comparator.comparing(AutoRenewDutType::getValidEndTime)
            .thenComparing(AutoRenewDutType::getVipType)
            .thenComparing(AutoRenewDutType::getPayChannel)
            .thenComparing(AutoRenewDutType::getDutType));
        List<List<Object>> tableContents = Lists.newArrayList();
        for (AutoRenewDutType autoRenewDutType : willExpiredDutTypes) {
            String sourceVipType = autoRenewDutType.getSourceVipType() != null ? autoRenewDutType.getSourceVipType().toString() : "";
            List<Object> oneRowData = Lists.newArrayList(sourceVipType,
                autoRenewDutType.getVipType(),
                autoRenewDutType.getDutType(),
                autoRenewDutType.getName(),
                autoRenewDutType.getPayChannelName(),
                autoRenewDutType.getPriority(),
                autoRenewDutType.getValidStartTime(),
                autoRenewDutType.getValidEndTime()
            );
            tableContents.add(oneRowData);
        }

        MailHeader mailHeader = new MailHeader();
        mailHeader.setTos(addressees);
        mailHeader.setTitle("【重要提醒】代扣方式即将到期提醒");
        mailHeader.setNeedTitlePrefix(false);

        String tableComment = "下列代扣方式即将到期，签约下列代扣方式的用户，到期后不再发起续费，请研发、产品和运营同学确认是否需要调整策略！！！";
        List<String> tableTitles = Lists.newArrayList("原会员类型", "会员类型", "代扣方式", "名称", "支付渠道名称", "优先级", "生效开始时间", "到期时间");
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        log.info("[DutTypeExpireMonitorJob] start send email");
        JobHelper.log("[DutTypeExpireMonitorJob] start send email");
        try {
            mailComponent.sendMail(mailHeader, Collections.singletonList(mailContent));
            log.info("[DutTypeExpireMonitorJob] send email finished, addressees:{}", Arrays.toString(addressees));
            JobHelper.log("[DutTypeExpireMonitorJob] send email finished, addressees:{0}", Arrays.toString(addressees));
        } catch (Exception e) {
            log.error("[DutTypeExpireMonitorJob] send mail occurred exception", e);
            JobHelper.log("[DutTypeExpireMonitorJob] send mail occurred exception", e);
        }
    }

}
