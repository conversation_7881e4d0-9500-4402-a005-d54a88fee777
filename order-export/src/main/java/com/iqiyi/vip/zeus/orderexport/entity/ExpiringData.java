package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;

/**
 * @Author: <PERSON>
 * @Date: 2023/3/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpiringData {
    private Long id;
    private Timestamp validStartTime;
    private Timestamp validEndTime;
    private String name;
    private String promotion;
    private String actName;

    /**
     * 获取展示名称
     * @return
     */
    public String getDesc() {
        if (StringUtils.isNotBlank(name)) {
            return name;
        }
        if (StringUtils.isNotBlank(promotion)) {
            return promotion;
        }
        if (StringUtils.isNotBlank(actName)) {
            return actName;
        }
        return "";
    }
}
