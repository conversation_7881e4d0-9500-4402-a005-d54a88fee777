package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringPackage;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.ExpiringPackageDao;

/**
 * <AUTHOR>
 * @date 2023/5/3 20:35
 */
@Repository
@Profile("!sg")
public class ExpiringPackageDaoImpl implements ExpiringPackageDao {

    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<ExpiringPackage> queryExpiringPackage(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(ExpiringPackageDao.class).queryExpiringPackage(rangeLeft, rangeRight);
    }

    @Override
    public List<ExpiringPackage> querySpecialExpiringPackage(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(ExpiringPackageDao.class).querySpecialExpiringPackage(rangeLeft, rangeRight);
    }
}
