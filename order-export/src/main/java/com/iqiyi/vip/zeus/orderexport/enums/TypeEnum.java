package com.iqiyi.vip.zeus.orderexport.enums;

/**
 * 订单上的Type字段枚举
 *
 * @Author: <PERSON>
 * @Date: 2021/3/4
 */
public enum TypeEnum {
    NORMAL(1, "普通订单"),
    TEST(-1, "测试订单"),
    VIP_RIGHT_DONATION(-2, "权益转赠"),
    SETTLE(11, "芝麻GO结算单"),
    ZHIMA_GO_DUT(12, "芝麻GO代扣单"),
    WECHAT_PAY_SCORE_CREATE(13, "微信支付分创单"),
    WECHAT_PAY_SCORE_COMPLETE(14, "微信支付分结单");

    private int code;
    private String desc;

    TypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isSettle(Integer type) {
        return type != null && SETTLE.getCode() == type;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
