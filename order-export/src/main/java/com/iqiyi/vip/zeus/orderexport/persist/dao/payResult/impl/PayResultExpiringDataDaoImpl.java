package com.iqiyi.vip.zeus.orderexport.persist.dao.payResult.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringData;
import com.iqiyi.vip.zeus.orderexport.model.ExpiringDataReq;
import com.iqiyi.vip.zeus.orderexport.persist.dao.ExpiringDataDao;

/**
 * @Author: Lin Peihui
 * @Date: 2022/8/12
 */
@Repository
@Profile("!sg")
public class PayResultExpiringDataDaoImpl implements ExpiringDataDao {

    @Resource
    SqlSessionTemplate payResultSqlSessionTemplate;

    @Override
    public List<ExpiringData> queryExpiringData(ExpiringDataReq expiringDataReq) {
        return payResultSqlSessionTemplate.getMapper(ExpiringDataDao.class)
            .queryExpiringData(expiringDataReq);
    }
}
