package com.iqiyi.vip.zeus.orderexport.consumer;

import com.alibaba.fastjson.JSON;

import com.iqiyi.vip.zeus.orderexport.handler.MetricExportHandler;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.OrderCanalEvent;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/23
 */
@Slf4j
@Component
public class OrderBinlogConsumer extends BaseRMQConsumer {
    @Resource
    private MetricExportHandler metricExportHandler;

    @Override
    protected ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt messageExt : list) {
            String msgBody = null;
            try {
                msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                OrderCanalEvent event = JSON.parseObject(msgBody, OrderCanalEvent.class);
                if (CanalEventUtil.isDelete(event.getEventType())) {
                    continue;
                }

                log.info("Consume order binlog. msgId: {}, orderCode:{}", messageExt.getMsgId(), event.getRowAfter().getOrderCode());
                metricExportHandler.handle(event);
            } catch (Exception e) {
                log.error("process binlog message error. msgId:{}, msgBody:{}", messageExt.getMsgId(), msgBody, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
