package com.iqiyi.vip.zeus.orderexport.persist.dao.refund;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2025/02/24
 */

@Repository
public class RefundDaoImpl implements RefundDao {
    @Resource
    private SqlSessionTemplate refundSqlSessionTemplate;

    @Override
    public List<String> getRefundOrderCodeByIdempotentCode(String idempotentCode) {
        return refundSqlSessionTemplate.getMapper(RefundDao.class).getRefundOrderCodeByIdempotentCode(idempotentCode);
    }

}
