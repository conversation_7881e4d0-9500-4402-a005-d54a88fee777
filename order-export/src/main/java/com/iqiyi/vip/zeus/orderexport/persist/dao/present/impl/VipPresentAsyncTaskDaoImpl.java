package com.iqiyi.vip.zeus.orderexport.persist.dao.present.impl;

import com.iqiyi.vip.zeus.orderexport.persist.dao.present.VipPresentAsyncTaskDao;
import com.iqiyi.vip.repository.ClusterAsyncTask;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created at: 2021-09-27
 *
 * <AUTHOR>
 */
@Repository
public class VipPresentAsyncTaskDaoImpl implements VipPresentAsyncTaskDao {

    @Resource
    SqlSessionTemplate presentSqlSessionTemplate;

    @Override
    public List<ClusterAsyncTask> getAsyncTasks(Timestamp nSecondsAgo, Timestamp now) {
        return presentSqlSessionTemplate.getMapper(VipPresentAsyncTaskDao.class).getAsyncTasks(nSecondsAgo, now);
    }
}
