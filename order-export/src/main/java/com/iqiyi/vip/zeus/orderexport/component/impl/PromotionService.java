package com.iqiyi.vip.zeus.orderexport.component.impl;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.iqiyi.vip.zeus.orderexport.constant.Constant;
import com.iqiyi.vip.zeus.orderexport.enums.FavorRecordIndexDataTypeEnum;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.model.OrderReferDto;
import com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorActivityDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorRecordDao;
import com.iqiyi.vip.zeus.orderexport.util.HashCodeUtils;

/**
 * @Author: Lin Peihui
 * @Date: 2022/8/12
 */
@Slf4j
@Component
@Profile("!sg")
public class PromotionService {

    @Resource
    private FavorActivityDao favorActivityDao;
    @Resource
    private FavorRecordDao favorRecordDao;
    @Value("${favor.record.table.number}")
    private String favorRecordTableNumber;


    public boolean hasTakenPartInFavorAct(OrderDto order) {
        Optional<OrderReferDto> referDtoOptional = order.extractReferDto();
        if (!referDtoOptional.isPresent()) {
            return false;
        }
        OrderReferDto referDto = referDtoOptional.get();
        String orderActCode = referDto.getActCode();
        String actCode = getMainActCode(orderActCode);
        if (referDto.getBusinessProperty() == null && StringUtils.isBlank(actCode)) {
            return false;
        }
        Long favorId = null;
        if (referDto.getBusinessProperty() != null) {
            Object favorIdObj = referDto.getBusinessProperty().get("favorId");
            if (favorIdObj != null) {
                favorId = Long.valueOf(String.valueOf(favorIdObj));
            }
        }
        if (favorId == null && StringUtils.isNotBlank(actCode)) {
            favorId = favorActivityDao.getFavorIdByActCode(actCode);
        }
        if (favorId == null) {
            return false;
        }
        Boolean hasTaken;
        if (order.getUserId() != null) {
            hasTaken = hasTakenPartInFavorAct(favorId, String.valueOf(order.getUserId()), FavorRecordIndexDataTypeEnum.UID.getType(), order.getOrderCode());
            if (hasTaken) {
                return true;
            }
        }
        hasTaken = hasTakenPartInFavorAct(favorId, order.getAccountId(), FavorRecordIndexDataTypeEnum.PAY_ACCOUNT.getType(), order.getOrderCode());
        if (hasTaken) {
            return true;
        }
        String deviceId = getFromFrVersion(order, "d");
        hasTaken = hasTakenPartInFavorAct(favorId, deviceId, FavorRecordIndexDataTypeEnum.DEVICE_ID.getType(), order.getOrderCode());
        if (hasTaken) {
            return true;
        }
        hasTaken = hasTakenPartInFavorAct(favorId, order.getPhoneNum(), FavorRecordIndexDataTypeEnum.PHONE_NUM.getType(), order.getOrderCode());
        return hasTaken;
    }

    private Boolean hasTakenPartInFavorAct(Long favorId, String favorData, Integer indexDataType, String orderCode) {
        if (StringUtils.isBlank(favorData)) {
            return false;
        }
        long index = Math.abs(HashCodeUtils.hashCode(favorData));
        List<Long> idList = favorRecordDao.queryIdsByFavorIdAndFavorDataAndType(getTableName(
            index, Constant.FAVOR_RECORD_TABLE_SEQ_NUM_LENGTH), favorId, favorData, indexDataType, orderCode);
        return idList != null && idList.size() > 0;
    }

    private String getTableName(long hashcode, int length) {
        long remainder = hashcode % Integer.valueOf(favorRecordTableNumber);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length - String.valueOf(remainder).length(); i++) {
            sb.append("0");
        }
        sb.append(remainder);
        return "promotion_favor_record_" + sb.toString();
    }

    private String getFromFrVersion(OrderDto order, String key) {
        if (org.apache.commons.lang3.StringUtils.isBlank(order.getFrVersion())) {
            return null;
        }
        Map<String, String> params = Maps.newHashMap();
        try {
            String[] paramsArr = order.getFrVersion().split("&");
            if (paramsArr.length == 0) {
                return null;
            }
            for (String paramStr : paramsArr) {
                if (paramStr.contains("=")) {
                    String[] paramArr = paramStr.split("=");
                    if (paramArr.length == 2) {
                        params.put(paramArr[0], paramArr[1]);
                    }
                }
            }
        } catch (Exception e) {
            log.info("An error happened when split fr_version {} of order {}", order.getFrVersion(), order.getOrderCode(), e);
        }
        return params.get(key);
    }

    private String getMainActCode(String orderActCode) {
        if (StringUtils.isBlank(orderActCode)) {
            return null;
        }
        List<String> actCodeList = Splitter.on(",").trimResults().splitToList(orderActCode);
        for (String actCode : actCodeList) {
            if (!actCode.startsWith("main_")) {
                continue;
            }
            return actCode.substring("main_".length());
        }
        return null;
    }

}
