package com.iqiyi.vip.zeus.orderexport.validator.result;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;

/**
 * @author: guojing
 * @date: 2023/12/27 20:10
 */
@Data
@NoArgsConstructor
public class AlertRuleCreateParamCheckResult {

    private ZeusMonitor zeusMonitor;
    private DashboardWithMeta dashboardWithMeta;

    public AlertRuleCreateParamCheckResult(ZeusMonitor zeusMonitor, DashboardWithMeta dashboardWithMeta) {
        this.zeusMonitor = zeusMonitor;
        this.dashboardWithMeta = dashboardWithMeta;
    }
}