package com.iqiyi.vip.zeus.orderexport.persist.dao.order.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.TidbOrderDao;

/**
 * @Author: <PERSON> @Date: 2022/2/23
 */
@Repository
public class TidbOrderDaoImpl implements TidbOrderDao {

    @Resource
    private SqlSessionTemplate tidbOrderSqlSessionTemplate;

    @Override
    public List<OrderDto> getByOrderCodeList(List<String> orderCodeList) {
        return tidbOrderSqlSessionTemplate.getMapper(TidbOrderDao.class).getByOrderCodeList(orderCodeList);
    }

    @Override
    public OrderDto getByOrderCode(String orderCode) {
        return tidbOrderSqlSessionTemplate.getMapper(TidbOrderDao.class).getByOrderCode(orderCode);
    }
}
