package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.LevelDao;

/**
 * <AUTHOR>
 * @date 2023/5/4 19:21
 */
@Repository
@Profile("!sg")
public class LevelDaoImpl implements LevelDao {

    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public String getMarkById(Integer id) {
        return storeSqlSessionTemplate.getMapper(LevelDao.class).getMarkById(id);
    }
}
