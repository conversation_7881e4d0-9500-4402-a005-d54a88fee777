package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Gift;

/**
 * <AUTHOR>
 * @date 2024/4/10 9:10
 */
public interface GiftDao {
    @Select("select * from qiyue_gift "
        + "where start_time<now() "
        + "and end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and end_time<date_add(now(),interval #{rangeRight} day)")
    List<Gift> queryExpiringData(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

}
