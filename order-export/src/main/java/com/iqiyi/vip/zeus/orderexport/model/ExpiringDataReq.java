package com.iqiyi.vip.zeus.orderexport.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Author: <PERSON>
 * @Date: 2023/3/8
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ExpiringDataReq {

    /**
     * 模块描述
     */
    private String module;
    /**
     * 数据库
     */
    private String database;
    /**
     * 表名
     */
    private String tableName;
    /**
     * 根据deadline来计算过期时间
     */
    private boolean calcByDeadline;
    /**
     * 过期前多少天前提醒
     */
    private Integer offsetDay = 3;

    /**
     * 计算截止时间
     * @return
     */
    public String getValidEndTime() {
        if (calcByDeadline) {
            return null;
        }
        return getOffsetTime();
    }


    /**
     * 计算截止时间
     * @return
     */
    public String getDeadline() {
        if (!calcByDeadline) {
            return null;
        }
        return getOffsetTime();
    }

    private String getOffsetTime() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.now().plusDays(offsetDay + 1).withHour(0).withMinute(0).withSecond(0);
        return df.format(time);
    }

}
