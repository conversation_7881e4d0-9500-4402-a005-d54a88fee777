package com.iqiyi.vip.zeus.orderexport.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;
import com.iqiyi.vip.zeus.orderexport.client.AutoRenewClient;
import com.iqiyi.vip.zeus.orderexport.client.PayCenterDutAccountClient;
import com.iqiyi.vip.zeus.orderexport.client.resp.AccountResponse;
import com.iqiyi.vip.zeus.orderexport.client.resp.UserAgreementSimpleInfoResult;
import com.iqiyi.vip.zeus.orderexport.entity.AgreementNoInfo;
import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AgreementNoInfoDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;

/**
 * @author: guojing
 * @date: 2025/3/26 18:31
 */
@Slf4j
@Component
public class SignPaidMsgHandler extends BaseRMQConsumer {

    private static final List<Integer> AUTO_RENEW_LIST = Arrays.asList(1, 3);

    @Value("${check.pay.center.dut.bind:true}")
    private Boolean checkPayCenterDutBind;
    @Value("${sign.paid.msg.delay.level:3}")
    private Integer signPaidMsgDelayLevel;

    @Resource
    private PayCenterDutAccountClient payCenterDutAccountClient;
    @Resource
    private AutoRenewClient autoRenewClient;
    @Resource
    private AgreementNoInfoDao agreementNoInfoDao;
    @Resource
    private AutoRenewDutTypeDao autoRenewDutTypeDao;


    @Override
    protected ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt messageExt : list) {
            String msgBody = null;
            try {
                msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                OrderDto orderDto = JSON.parseObject(msgBody, OrderDto.class);
                log.info("Consume order paid msg. msgId: {}, orderCode:{}, reConsumeTimes:{}", messageExt.getMsgId(), orderDto.getOrderCode(), messageExt.getReconsumeTimes());
                if (orderDto.getStatus() != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                boolean signPaidOrder = AUTO_RENEW_LIST.contains(orderDto.getAutoRenew());
                if (!signPaidOrder) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                if (BooleanUtils.isFalse(checkPayCenterDutBind)) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                Integer renewType = orderDto.getRenewType();
                if (renewType == null) {
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                //主动消费失败，并指定延迟16s再处理，避免查询支付中心绑定关系生成延迟
                int reConsumeTimes = messageExt.getReconsumeTimes();
                if (reConsumeTimes == 0) {
                    consumeConcurrentlyContext.setDelayLevelWhenNextConsume(signPaidMsgDelayLevel);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }

                Integer dutType = null;
                Integer payChannel = null;
                AgreementNoInfo agreementNoInfo = agreementNoInfoDao.getById(renewType);
                if (agreementNoInfo != null) {
                    payChannel = agreementNoInfo.getPayChannel();
                    dutType = agreementNoInfo.getDutType();
                } else {
                    payChannel = autoRenewDutTypeDao.getPayChannelByDutType(renewType);
                    dutType = renewType;
                }
                checkDutBind(orderDto.getUserId(), dutType, payChannel, orderDto.getOrderCode());
            } catch (Exception e) {
                log.error("process order paid msg error. msgId:{}, msgBody:{}", messageExt.getMsgId(), msgBody, e);
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void checkDutBind(Long userId, Integer dutType, Integer payChannel, String orderCode) {
        List<UserAgreementSimpleInfoResult> userAutoRenewAgreementList = autoRenewClient.getUserAutoRenewAgreementList(userId);
        if (userAutoRenewAgreementList == null) {
            return;
        }
        UserAgreementSimpleInfoResult autoRenewUser = userAutoRenewAgreementList.stream()
            .filter(userAgreementSimpleInfoResult -> Objects.equals(dutType, userAgreementSimpleInfoResult.getDutType()))
            .findAny().orElse(null);
        if (autoRenewUser == null) {
            return;
        }
        AccountResponse accountResponse = payCenterDutAccountClient.queryBindInfo(userId, dutType);
        if (accountResponse == null) {
            return;
        }
        boolean bind = accountResponse.alreadyBind(dutType);
        if (!bind) {
            String payChannelStr = payChannel != null ? payChannel.toString() : "unknown";
            log.info("pay center no bind, userId:{}, payChannel:{}, dutType:{}, orderCode:{}", userId, payChannel, dutType, orderCode);
            EagleMonitor.counterInc(new EagleParam("miss_pay_center_dut_bind").tag("payChannel", payChannelStr));
        }
    }

}
