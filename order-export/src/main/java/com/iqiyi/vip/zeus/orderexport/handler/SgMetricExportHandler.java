package com.iqiyi.vip.zeus.orderexport.handler;

import com.google.common.collect.Lists;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEvent;
import com.iqiyi.vip.zeus.orderexport.util.LruLocalCache;

/**
 * @Author: Lin Peihui
 * @Date: 2023/2/23
 */
@Slf4j
@Component
@Profile("sg")
public class SgMetricExportHandler extends AbstractMetricExportHandler implements MetricExportHandler {
    @Resource
    private LruLocalCache lruLocalCache;


    @Override
    protected boolean hasHandled(OrderDto order) {
        String cacheKey = generateKey(order.getOrderCode(), order.getStatus());
        boolean isExist = lruLocalCache.isExist(cacheKey);
        if (isExist) {
            log.info("Order:{} has been handled. status:{}, notifyResult:{}",
                order.getOrderCode(), order.getStatus(), order.getNotifyResult());
            return true;
        }
        return false;
    }

    @Override
    protected boolean needDoSelfTest() {
        return false;
    }

    @Override
    protected void countExtraMetrics(CanalEvent<OrderDto> event, Tag payChannelTag) {
        countFromAppVersion(event.getRowAfter());
    }

    /**
     * 记录订单的fr_version信息，分状态.暂时
     */
    private void countFromAppVersion(OrderDto order) {
        Tag statusTag = new ImmutableTag("status", String.valueOf(order.getStatus()));
        String frVersion = order.getFrVersion();
        String version = parseVersionFromFrVersion(frVersion);
        Tag versionTag = new ImmutableTag("version", version);
        List<Tag> orderTotal = Lists.newLinkedList();
        orderTotal.add(versionTag);
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_version_status_total", orderTotal).increment();
    }

    /**
     * 解析version信息
     */
    private static String parseVersionFromFrVersion(String frVersion) {
        if (StringUtils.isBlank(frVersion)) {
            return StringUtils.EMPTY;
        }
        try {
            frVersion = URLDecoder.decode(frVersion, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.warn("parse fr_version failed.info={}", frVersion);
        }
        frVersion = frVersion.replace(" ", "");
        String v = "v";
        for (String part : frVersion.split("&")) {
            String[] subparts = part.split("=", 2);
            if (StringUtils.equals(subparts[0], v)) {
                return StringUtils.trimToEmpty(subparts[1]);
            }
        }
        return StringUtils.EMPTY;
    }

}
