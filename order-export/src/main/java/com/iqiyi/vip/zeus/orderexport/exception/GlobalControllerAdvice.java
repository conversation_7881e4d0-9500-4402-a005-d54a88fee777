package com.iqiyi.vip.zeus.orderexport.exception;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.component.advice.vo.HttpLogVo;
import com.iqiyi.vip.component.util.PayUtils;
import com.iqiyi.vip.component.util.RequestUtils;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.eagleclient.CodeEnum;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: kongwenqiang
 * DateTime: 2017/10/20 下午5:22
 * Mail:<EMAIL>
 * Description: desc
 */
@ControllerAdvice
@Slf4j
public class GlobalControllerAdvice {

    private static final Logger HTTP_TRACE_LOG = LoggerFactory.getLogger(System.getProperty("app.access.log", "APP_ACCESS_LOG"));

    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Map handle404(HttpServletRequest request, Exception exception) {
        Map<String, String> res = Maps.newHashMap();
        res.put("code", "404");
        res.put("message", "资源不存在!");
        recordHttpTrace(request, res);
        return res;
    }

    @ResponseBody
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public BaseResponse<String> handle500(HttpServletRequest request, Exception exception) {
        log.error("error occurred ", exception);
        BaseResponse<String> webResult = BaseResponse.create(CodeEnum.SYSTEM_ERROR);
        recordHttpTrace(request, webResult);
        return webResult;
    }

    @ResponseBody
    @ExceptionHandler(BizException.class)
    public BaseResponse<String> bizException(HttpServletRequest request, BizException exception) {
        log.error("bizException. code: {}, msg: {}", exception.getCode(), exception.getMessage());
        BaseResponse<String> webResult = BaseResponse.create(exception.getCode(), exception.getMessage());
        recordHttpTrace(request, webResult);
        return webResult;
    }

    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public BaseResponse<String> illegalArgumentException(HttpServletRequest request, IllegalArgumentException exception) {
        log.error("illegalArgumentException. msg: {}", exception.getMessage(), exception);
        BaseResponse<String> webResult = BaseResponse.createParamError(exception.getMessage());
        recordHttpTrace(request, webResult);
        return webResult;
    }

    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    @ResponseBody
    public BaseResponse<String> methodArgumentNotValidException(HttpServletRequest servletRequest, MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        return buildControllerParamErrorMsg(fieldErrors, exception, servletRequest);
    }

    @ExceptionHandler(value = {ConstraintViolationException.class})
    @ResponseBody
    public BaseResponse<String> constraintViolationException(HttpServletRequest servletRequest, ConstraintViolationException exception) {
        log.error("request param illegal occurred: uri={}, parameters={}", servletRequest.getRequestURI(), JSON.toJSONString(servletRequest.getParameterMap()));
        BaseResponse<String> webResult = BaseResponse.createParamError(exception.getMessage());
        recordHttpTrace(servletRequest, webResult);
        return webResult;
    }

    @ExceptionHandler(value = {BindException.class})
    @ResponseBody
    public BaseResponse<String> bindException(HttpServletRequest servletRequest, BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        return buildControllerParamErrorMsg(fieldErrors, exception, servletRequest);
    }

    private BaseResponse<String> buildControllerParamErrorMsg(List<FieldError> fieldErrors, Exception exception, HttpServletRequest servletRequest) {
        String errorMsg = null;
        if (CollectionUtils.isNotEmpty(fieldErrors)) {
            List<String> paramErrorMsgs = fieldErrors.stream().map(FieldError::getDefaultMessage).collect(Collectors.toList());
            errorMsg = String.join("; ", paramErrorMsgs);
        }
        errorMsg = errorMsg != null ? errorMsg : exception.getMessage();
        log.error("request param illegal occurred: uri={}, parameters={}", servletRequest.getRequestURI(), JSON.toJSONString(servletRequest.getParameterMap()));
        BaseResponse<String> webResult = BaseResponse.createParamError(errorMsg);
        recordHttpTrace(servletRequest, webResult);
        return webResult;
    }

    private void recordHttpTrace(HttpServletRequest request, Object response) {
        Map params = request.getParameterMap();
        params = PayUtils.genMapByRequestParas(params);
        HttpLogVo httpLogVo = new HttpLogVo();
        httpLogVo.setClientIp(RequestUtils.getRemoteAddr(request));
        httpLogVo.setLocalIp(RequestUtils.getLocalIP());
        httpLogVo.setHttpUrl(request.getRequestURL().toString());
        httpLogVo.setMethod(request.getMethod());
        httpLogVo.setParams(params);
        httpLogVo.setTime(new Date());
        httpLogVo.setCost(1L);
        httpLogVo.setResponse(JacksonUtils.toJsonString(response));
        HTTP_TRACE_LOG.info(httpLogVo.toJSONString());
    }

    /**
     * 去除字符串前后空格
     * @param binder
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(String.class, new StringTrimmerEditor(true));
    }

}
