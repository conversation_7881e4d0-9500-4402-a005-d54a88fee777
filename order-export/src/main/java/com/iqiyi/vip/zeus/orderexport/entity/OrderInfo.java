package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Objects;

import com.iqiyi.vip.order.dal.model.Order;

/**
 * <AUTHOR>
 * @date 2024/5/20 16:24
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderInfo {

    private Long id;
    private String orderCode;
    private Integer status;
    private Integer realFee;
    private String notifyResult;
    private Timestamp startTime;
    private Timestamp deadline;
    private Integer renewType;

    public OrderInfo(Order order) {
        if (Objects.isNull(order)) {
            return;
        }
        this.id = order.getId();
        this.orderCode = order.getOrderCode();
        this.status = order.getStatus();
        this.realFee = order.getRealFee();
        this.notifyResult = order.getNotifyResult();
        this.startTime = order.getStartTime();
        this.deadline = order.getDeadline();
    }

}
