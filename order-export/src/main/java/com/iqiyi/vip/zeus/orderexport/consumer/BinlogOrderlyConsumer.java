package com.iqiyi.vip.zeus.orderexport.consumer;

import com.google.common.base.Charsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeOrderlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerOrderly;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.orderexport.component.impl.MessageHandlerRegistry;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;
import com.iqiyi.vip.zeus.orderexport.mysqlio.handler.CanalEventHandler;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;
import com.iqiyi.vip.zeus.orderexport.util.OrderMsgUtils;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BinlogOrderlyConsumer implements MessageListenerOrderly {

    @Resource
    private MessageHandlerRegistry messageHandlerRegistry;

    @Override
    public ConsumeOrderlyStatus consumeMessage(List<MessageExt> messageExtList, ConsumeOrderlyContext consumeOrderlyContext) {
        List<String> msgIds = messageExtList.stream().map(MessageExt::getMsgId).collect(Collectors.toList());
        try {
            StopWatch stopWatch = StopWatch.createStarted();

            //过滤压测订单
            List<MessageExt> filteredMsgList = new ArrayList<>();
            for (MessageExt msg : messageExtList) {
                if (!OrderMsgUtils.isPressureOrderMsg(msg)) {
                    filteredMsgList.add(msg);
                }
            }
            if (CollectionUtils.isEmpty(filteredMsgList)) {
                return ConsumeOrderlyStatus.SUCCESS;
            }

            for (MessageExt messageExt : filteredMsgList) {
                stopWatch.suspend();
                stopWatch.resume();
                try {
                    consumeOneMsg(messageExt);
                } catch (Exception e) {
                    log.error("BinlogOrderlyConsumer consumeOneMsg occurred exception, topic:{}, msgId: {}", messageExt.getTopic(), messageExt.getMsgId(), e);
                    continue;
                }
                log.info("[topic:{}] binlog msg handler finished msgId:{} cost time:{}ms", messageExt.getTopic(), messageExt.getMsgId(), stopWatch.getTime());
            }
        } catch (Exception e) {
            log.error("BinlogOrderlyConsumer consumeMessage occurred exception, topic:{}, msgIds: {}", messageExtList.get(0).getTopic(), msgIds, e);
        }

        return ConsumeOrderlyStatus.SUCCESS;
    }

    private void consumeOneMsg(MessageExt message) {
        if (message.getReconsumeTimes() > 3) {
            log.warn("[topic:{}] msgId:{} consume times:{}", message.getTopic(), message.getMsgId(), message.getReconsumeTimes());
        }
        String messageBody = new String(message.getBody(), Charsets.UTF_8);
        MapCanalEvent mapCanalEvent = JacksonUtils.parseObject(messageBody, MapCanalEvent.class);
        if (mapCanalEvent == null || CanalEventUtil.isDelete(mapCanalEvent.getEventType())) {
            return;
        }
        CanalEventHandler eventHandler = messageHandlerRegistry.findSuitableHandler(mapCanalEvent.getTableName());
        if (eventHandler == null || !eventHandler.accept(mapCanalEvent)) {
            log.warn("Can not found suitable CanalEventHandler for this event :{}", mapCanalEvent);
            return;
        }

        eventHandler.handCanalEvent(mapCanalEvent);
    }

}
