package com.iqiyi.vip.zeus.orderexport.job;

import io.micrometer.core.instrument.Metrics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;

import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;

/**
 * @author: guojing
 * @date: 2024/10/14 20:01
 */
@Slf4j
@Component
public class QiyiJobHealthJob extends IJobHandler {

    private static final String HEALTH_API = "/health";

    @Value("${qiyi.job.domain:http://qiyi-job-admin.qiyi.domain}")
    private String qiyiJobDomain;

    @Resource
    @Qualifier("restTemplate")
    RestTemplate restTemplate;

    @Job("qiyiJobHealthJob")
    @Override
    public void execute() throws Exception {
        Long jobId = JobHelper.getJobId();
        String jobParam = JobHelper.getJobParam();
        log.info("---------- Start execute QiyiJobHealthJob, jobId:{}, jobParam:{} ----------", jobId, jobParam);
        JobHelper.log("---------- Start execute QiyiJobHealthJob, jobId:{} ----------", jobId);
        StopWatch stopWatch = StopWatch.createStarted();
        String healthUrl = qiyiJobDomain + HEALTH_API;
        ResponseEntity<String> responseEntity = null;
        try {
            responseEntity = restTemplate.getForEntity(healthUrl, String.class, Collections.emptyMap());
        } catch (RestClientException e) {
            log.error("---------- QiyiJobHealthJob failed, jobId:{}, cost:{}ms. ----------", JobHelper.getJobId(), stopWatch.getTime(), e);
            JobHelper.log("---------- QiyiJobHealthJob failed, jobId:{}, cost:{}ms, errorMsg:{}. ----------", JobHelper.getJobId(), stopWatch.getTime(), e.getMessage());
            JobHelper.handleFail();
            return;
        }
        if ("ok".equals(responseEntity.getBody())) {
            Metrics.counter("qiyi_job_health_total").increment();
            log.info("---------- QiyiJobHealthJob finished, jobId:{}, cost:{}ms. ----------", JobHelper.getJobId(), stopWatch.getTime());
            JobHelper.log("---------- QiyiJobHealthJob finished, jobId:{}, cost:{}ms. ----------", JobHelper.getJobId(), stopWatch.getTime());
            JobHelper.handleSuccess();
        } else {
            log.error("---------- QiyiJobHealthJob failed, jobId:{}, cost:{}ms, responseEntity:{}. ----------", JobHelper.getJobId(), stopWatch.getTime(), responseEntity);
            JobHelper.log("---------- QiyiJobHealthJob failed, jobId:{}, cost:{}ms, responseEntity:{}. ----------", JobHelper.getJobId(), stopWatch.getTime(), responseEntity);
            JobHelper.handleFail();
        }
    }
}
