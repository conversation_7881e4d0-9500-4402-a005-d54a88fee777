package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringPackage;

/**
 * <AUTHOR>
 * @date 2023/5/3 20:35
 */
public interface ExpiringPackageDao {

    @Select("SELECT id,name,store_code,version,level_id,priority,valid_start_time,valid_end_time,operator,update_time "
        + "FROM store_package_config "
        + "where status=1 and uid_list is null and create_channel=3 and level_id=5 "
        + "and valid_start_time<now() "
        + "and valid_end_time>DATE_ADD(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day) "
        + "union all "
        + "SELECT id,name,platform_code,version,level_id,priority,valid_start_time,valid_end_time,operator,update_time "
        + "FROM store_package_config "
        + "where status=1 and uid_list is null and create_channel=1 "
        + "and valid_start_time<now() "
        + "and valid_end_time>DATE_ADD(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<ExpiringPackage> queryExpiringPackage(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

    @Select("SELECT id,name,store_code,version,level_id,priority,valid_start_time,valid_end_time,operator,update_time "
        + "FROM store_package_config "
        + "where status=1 and uid_list is null and create_channel=3 and level_id=5 "
        + "and valid_end_time>concat(year(now()),#{rangeLeft})"
        + "and valid_end_time<concat(year(now())+1,#{rangeRight}) "
        + "union all "
        + "SELECT id,name,platform_code,version,level_id,priority,valid_start_time,valid_end_time,operator,update_time "
        + "FROM store_package_config "
        + "where status=1 and uid_list is null and create_channel=1 "
        + "and valid_end_time>concat(year(now()),#{rangeLeft}) "
        + "and valid_end_time<concat(year(now())+1,#{rangeRight})")
    List<ExpiringPackage> querySpecialExpiringPackage(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);
}
