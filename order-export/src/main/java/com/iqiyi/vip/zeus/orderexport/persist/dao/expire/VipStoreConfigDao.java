package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringRule;
import com.iqiyi.vip.zeus.orderexport.entity.VipStoreConfig;

/**
 * <AUTHOR>
 * @date 2024/4/8 20:36
 */
public interface VipStoreConfigDao {
    @Select("select * from qiyue_vip_store_config "
        + "where status =1 "
        + "and valid_start_time<now() "
        + "and valid_end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<VipStoreConfig> queryExpiringData(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);

}
