package com.iqiyi.vip.zeus.orderexport.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.handler.OrderDelayHandler;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/23
 */
@Slf4j
@Component
public class OrderDelayMsgConsumer  extends BaseRMQConsumer {
    @Resource
    private OrderDelayHandler orderDelayHandler;

    @Override
    protected ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt messageExt : list) {
            String msgBody = null;
            try {
                msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                JSONObject msgJson = JSON.parseObject(msgBody);
                String orderCode = msgJson.getString("orderCode");
                Integer retriedCount = msgJson.getInteger("retriedCount");
                log.info("Consume delay msg. msgId: {}, orderCode:{}", messageExt.getMsgId(), orderCode);
                if (retriedCount == null) {
                    //权益未秒级开通的订单量监控统计
                    orderDelayHandler.monitorOpenVipRightTooSlow(orderCode);
                } else {
                    //权益开通失败的监控告警
                    orderDelayHandler.monitorOpenVipRightFailed(orderCode, retriedCount);
                }
            } catch (Exception e) {
                log.error("Consume delay msg error. msgId:{}, msgBody:{}", messageExt.getMsgId(), msgBody, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
