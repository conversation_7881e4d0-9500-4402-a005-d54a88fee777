package com.iqiyi.vip.zeus.orderexport.persist.dao.present.impl;

import com.iqiyi.vip.zeus.orderexport.entity.PresentOrder;
import com.iqiyi.vip.zeus.orderexport.entity.PresentRecord;
import com.iqiyi.vip.zeus.orderexport.persist.dao.present.VipPresentDao;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
@Repository
public class VipPresentDaoImpl implements VipPresentDao {

    @Resource
    SqlSessionTemplate presentSqlSessionTemplate;

    @Override
    public List<PresentOrder> selectRecentPresentOrders(String tableNo, String startPayTime, String endPayTime) {
        return presentSqlSessionTemplate.getMapper(VipPresentDao.class).selectRecentPresentOrders(tableNo, startPayTime, endPayTime);
    }

    @Override
    public List<PresentRecord> selectRecentPresentRecords(String tableNo, String startTime, String endTime) {
        return presentSqlSessionTemplate.getMapper(VipPresentDao.class).selectRecentPresentRecords(tableNo, startTime, endTime);
    }
}
