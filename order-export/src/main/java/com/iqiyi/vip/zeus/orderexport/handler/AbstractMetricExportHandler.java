package com.iqiyi.vip.zeus.orderexport.handler;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.component.impl.PaymentInfoService;
import com.iqiyi.vip.zeus.orderexport.entity.Product;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEvent;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.ProductDao;
import com.iqiyi.vip.zeus.orderexport.util.OrderCloneMapper;
import com.iqiyi.vip.zeus.orderexport.util.OrderMsgUtils;

/**
 * @Author: Lin Peihui
 * @Date: 2020/11/24
 */
@Slf4j
public abstract class AbstractMetricExportHandler {
    @Value("${intl.flag:false}")
    private Boolean isIntlEnv;

    @Resource
    private ProductDao productDao;

    @Resource
    private PaymentInfoService paymentInfoService;
    @Resource
    private AlterService alterService;
    @Value("${payTimeDiffMin:5}")
    private Integer payTimeDiffMin;

    private static final ImmutableSet<Integer> NEGATIVE_ORDER_STATUS_SET = ImmutableSet.of(5, 6, 11, 12);
    private static final Long FUN_ORDER_PRODUCT_ID = 26249L;
    private static final Long VIP_GOLD_PRODUCT_ID = 4L;

    public void handle(CanalEvent<OrderDto> event) {
        OrderDto order = event.getRowAfter();
        Timestamp payTime = order.getPayTime();
        //过滤测试单
        if (order.getType() != null && order.getType() == -1) {
            return;
        }
        //忽略支付时间在一天前的订单
        if (payTime != null && payTime.toLocalDateTime().isBefore(LocalDateTime.now().minusDays(1))) {
            return;
        }
        //已经处理过，直接返回
        if (hasHandled(order)) {
            return;
        }
        //过滤压测单
        if (!OrderMsgUtils.needDealWith(order)) {
            return;
        }
        //上报指标
        PaymentTypeDTO paymentTypeDTO = paymentInfoService.getPaymentType(order.getPayType());
        Tag payChannelTag = null;
        if (paymentTypeDTO != null) {
            log.info("rpc 调用后结果：id={}", paymentTypeDTO.getPayChannel());
            payChannelTag = new ImmutableTag("payChannel", String.valueOf(paymentTypeDTO.getPayChannel()));
        }
        countCommonMetrics(event, payChannelTag);
        countExtraMetrics(event, payChannelTag);
    }

    protected String generateKey(String orderCode, Integer status) {
        return orderCode + "_" + status;
    }

    protected abstract boolean hasHandled(OrderDto order);

    /**
     * 是否需要自测程序的准确性
     * @return
     */
    protected abstract boolean needDoSelfTest();

    protected abstract void countExtraMetrics(CanalEvent<OrderDto> event, Tag payChannelTag);

    private void countCommonMetrics(CanalEvent<OrderDto> event, Tag payChannelTag) {
        OrderDto order = event.getRowAfter();
        Tag statusTag = new ImmutableTag("status", String.valueOf(order.getStatus()));
        Tag payTypeTag = new ImmutableTag("payType", String.valueOf(order.getPayType()));
        Tag platformTag = new ImmutableTag("platform", String.valueOf(order.getPlatform()));
        Tag gatewayTag = new ImmutableTag("gateway", String.valueOf(order.getGateway()));
        Tag autoRenewTag = new ImmutableTag("autoRenew", String.valueOf(order.getAutoRenew()));
        Tag amountTag = new ImmutableTag("amount", String.valueOf(order.getAmount()));
        Product product = productDao.getById(order.getProductId());
        Tag vipTypeTag = new ImmutableTag("vipType", String.valueOf(product.getSubType()));

        countStatus(statusTag);
        countPayType(payTypeTag, statusTag);
        countProductAmount(product, order, statusTag);
        countAutoRenewAndAmount(order, autoRenewTag, amountTag, statusTag);
        countAgreementMode(order, autoRenewTag, amountTag, statusTag);
        countPayChannelRelatedInfo(order, payChannelTag, autoRenewTag, statusTag);
        countRefundWay(order, statusTag);
        countPartnerRefund(order, statusTag);
        countVipRightTransfer(order, statusTag);
        countPlatform(platformTag, statusTag);
        countVipType(order, vipTypeTag, statusTag);
        countGateway(gatewayTag, statusTag);
        countFromCasher(order, statusTag);

        countIllegalPayTime(order);
        selfTestIllegalPayTime(order);
    }


    private void countPayType(Tag payTypeTag, Tag statusTag) {
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payTypeTag);
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_payType_status_total", orderTotal).increment();
    }

    private void countStatus(Tag statusTag) {
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_status_total", orderTotal).increment();
    }

    private void countAutoRenewAndAmount(OrderDto order, Tag autoRenewTag, Tag amountTag, Tag statusTag) {
        if (order.getAutoRenew() == null || order.getAutoRenew() == 0) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(autoRenewTag);
        orderTotal.add(amountTag);
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_autoRenew_amount_status_total", orderTotal).increment();
    }

    private void countAgreementMode(OrderDto order, Tag autoRenewTag, Tag amountTag, Tag statusTag) {
        if (order.getAutoRenew() == null || order.getAutoRenew() == 0 || order.getRenewType() == null || order.getRenewType() < 20000) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(autoRenewTag);
        orderTotal.add(amountTag);
        orderTotal.add(statusTag);
        orderTotal.add(new ImmutableTag("renewType", String.valueOf(order.getRenewType())));
        Metrics.counter("huiyuan_order_agreement_mode_total", orderTotal).increment();
    }

    private void countPayChannelRelatedInfo(OrderDto order, Tag payChannelTag, Tag autoRenewTag, Tag statusTag) {
        Tag productTypeTag = new ImmutableTag("productType", String.valueOf(order.getProductType()));
        String partnerFlag = StringUtils.isNotBlank(order.getPartner()) ? "1" : "0";
        Tag partnerFlagTag = new ImmutableTag("partnerFlag", partnerFlag);
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payChannelTag);
        orderTotal.add(autoRenewTag);
        orderTotal.add(productTypeTag);
        orderTotal.add(statusTag);
        orderTotal.add(partnerFlagTag);
        Metrics.counter("huiyuan_order_payChannel_related_total", orderTotal).increment();
    }

    private void countRefundWay(OrderDto order, Tag statusTag) {
        if (!NEGATIVE_ORDER_STATUS_SET.contains(order.getStatus())) {
            return;
        }
        Tag refundWayTag = new ImmutableTag("refundWay", String.valueOf(order.getRefundWay()));
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(refundWayTag);
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_refundWay_status_total", orderTotal).increment();
    }

    private void countPlatform(Tag platformTag, Tag statusTag) {
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(platformTag);
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_platform_status_total", orderTotal).increment();
    }

    private void countVipType(OrderDto order, Tag vipTypeTag, Tag statusTag) {
        String autoRenew = "0";
        if (order.getAutoRenew() != null && order.getAutoRenew() == 2) {
            autoRenew = "1";
        }
        Tag autoRenewTag = new ImmutableTag("autoRenew", autoRenew);
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(vipTypeTag);
        orderTotal.add(statusTag);
        orderTotal.add(autoRenewTag);
        Metrics.counter("huiyuan_order_vipType_status_total", orderTotal).increment();
    }

    private void countGateway(Tag gatewayTag, Tag statusTag) {
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(gatewayTag);
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_gateway_status_total", orderTotal).increment();
    }

    private void countPartnerRefund(OrderDto order, Tag statusTag) {
        if (!NEGATIVE_ORDER_STATUS_SET.contains(order.getStatus())) {
            return;
        }
        if (StringUtils.isBlank(order.getPartner())) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_partner_refund_total", orderTotal).increment();
    }

    //TODO 修改统计逻辑
    private void countVipRightTransfer(OrderDto order, Tag statusTag) {
        if (!NEGATIVE_ORDER_STATUS_SET.contains(order.getStatus())) {
            return;
        }
        String refundPurpose = order.getRefundPurpose();
        if (StringUtils.isBlank(refundPurpose) && !"VIP_RIGHT_TRANSFER".equals(refundPurpose)) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(statusTag);
        Metrics.counter("huiyuan_order_vipRight_transfer_total", orderTotal).increment();
    }

    private void countProductAmount(Product product, OrderDto order, Tag statusTag) {
        if (!product.isMonthPeriodUnit() && !product.isYearPeriodUnit()) {
            return;
        }
        Tag amountTag = null;
        if (product.isMonthPeriodUnit()) {
            if (order.getAmount() == 1) {
                amountTag = new ImmutableTag("amount", "1");
            } else if (order.getAmount() == 3) {
                amountTag = new ImmutableTag("amount", "3");
            } else if (order.getAmount() == 12) {
                amountTag = new ImmutableTag("amount", "12");
            }
        }
        if (product.isYearPeriodUnit()) {
            amountTag = new ImmutableTag("amount", "12");
        }
        if (amountTag != null) {
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(statusTag);
            orderTotal.add(amountTag);
            Metrics.counter("huiyuan_order_amount_status_total", orderTotal).increment();
        }
    }

    private void countFromCasher(OrderDto order, Tag statusTag) {
        boolean fromCasher = order.fromCasher();
        if (!fromCasher) {
            return;
        }
        Tag fromCahserTag = new ImmutableTag("fromCasher", "1");
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(statusTag);
        orderTotal.add(fromCahserTag);
        Metrics.counter("huiyuan_order_fromCasher_status_total", orderTotal).increment();
    }

    private void countIllegalPayTime(OrderDto order) {
        if (isValidPayTime(order)) {
            return;
        }
        Tag orderTag = new ImmutableTag("order", "1");
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(orderTag);
//        Metrics.
        Metrics.counter("huiyuan_order_illegal_payTime", orderTotal).increment();
        String summary = "支付时间小于创建时间";
        if (isIntlEnv) {
            summary = "国际站-支付时间小于创建时间";
        }
        String alterContext = String.format("%s。orderCode:%s, payTime:%s, createTime:%s",
            summary, order.getOrderCode(), order.getPayTime(), order.getCreateTime());
        alterService.sendHotChat(summary, alterContext, "");
        log.info("Illegal pay time. orderCode:{}", order.getOrderCode());
    }

    private boolean isValidPayTime(OrderDto order) {
        if (order.getPayTime() == null) {
            return true;
        }
        //过滤fun会员侧同步过来的订单
        if(order.getProductId().equals(FUN_ORDER_PRODUCT_ID)) {
            return true;
        }
        long time1 = order.getCreateTime().getTime();
        long time2 = order.getPayTime().getTime();
        return (time1 - time2) / 1000 / 60 <= payTimeDiffMin;
    }

    private void selfTestIllegalPayTime(OrderDto originOrder) {
        if (!needDoSelfTest()) {
            return;
        }
        OrderDto order = OrderCloneMapper.INSTANCE.deepClone(originOrder);
        LocalDateTime now = LocalDateTime.now();
        Timestamp payTime = Timestamp.valueOf(now);
        Timestamp createTime = Timestamp.valueOf(now.plusMinutes(5));
        order.setPayTime(payTime);
        order.setCreateTime(createTime);
        order.setProductId(VIP_GOLD_PRODUCT_ID);
        boolean isValid;
        try {
            isValid = isValidPayTime(order);
        } catch (Exception e) {
            log.error("Self test illegal pay time throw exception. orderCode:{}", order.getOrderCode(), e);
            isValid = true;
        }
        if (isValid) {
            String alterContext = String.format("逻辑自检失败-支付时间校验。orderCode:%s", order.getOrderCode());
            alterService.sendHotChat("玄武-逻辑自检-支付时间", alterContext, "");
            log.info("Self test illegal pay time failed. orderCode:{}", order.getOrderCode());
        }
    }

}
