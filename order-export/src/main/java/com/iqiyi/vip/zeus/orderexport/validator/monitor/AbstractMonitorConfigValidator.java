package com.iqiyi.vip.zeus.orderexport.validator.monitor;

import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MonitorConditionOperator;
import com.iqiyi.vip.zeus.core.enums.PrometheusMetricType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQueryCondition;
import com.iqiyi.vip.zeus.core.service.MetricTemplateService;
import com.iqiyi.vip.zeus.eagleclient.constants.PrometheusConstants;
import com.iqiyi.vip.zeus.orderexport.param.MonitorCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.param.TianyanMonitorSaveParam;

/**
 * @author: guojing
 * @date: 2023/12/18 14:47
 */
public abstract class AbstractMonitorConfigValidator {

    @Resource
    private MetricTemplateService metricTemplateService;

    protected abstract DataSourceType getDataSourceType();

    public abstract void checkConfig(MonitorCreateOrUpdateParam createOrUpdateParam);

    public abstract void checkConfig(TianyanMonitorSaveParam createOrUpdateParam);

    public MetricTemplate checkSingleQuery(ZeusMonitorQuery query) {
        Integer metricTmpId = query.getMetricTmpId();
        MetricTemplate metricTemplate = metricTemplateService.getById(metricTmpId);
        if (metricTemplate == null || metricTemplate.invalid()) {
            throw BizException.newParamException("指标模版不存在, id:" + metricTmpId);
        }
        if (!Objects.equals(metricTemplate.getDatasourceType(), getDataSourceType().getValue())) {
            throw BizException.newParamException("选择的指标模版不支持当前数据源");
        }
        if (metricTemplate.isNeedGroupBy() && StringUtils.isBlank(query.getGroupBy())) {
            throw BizException.newParamException("请指定展示维度字段");
        }
        if (metricTemplate.isNeedTimeFilter() && StringUtils.isBlank(query.getTimeFilter())) {
            throw BizException.newParamException("请指定时间范围字段");
        }
        for (ZeusMonitorQueryCondition condition : query.getConditions()) {
            MonitorConditionOperator conditionOperator = MonitorConditionOperator.parseValue(condition.getOperator());
            if (conditionOperator == null) {
                throw BizException.newParamException("不支持的监控条件操作符");
            }
        }
        if (Objects.equals(metricTemplate.getMetricType(), PrometheusMetricType.Counter.getValue())
            && !StringUtils.endsWith(query.getSource(), PrometheusConstants.METRIC_TYPE_COUNTER_SUFFIX)) {
            query.setSource(query.getSource() + PrometheusConstants.METRIC_TYPE_COUNTER_SUFFIX);
        }
        return metricTemplate;
    }

}
