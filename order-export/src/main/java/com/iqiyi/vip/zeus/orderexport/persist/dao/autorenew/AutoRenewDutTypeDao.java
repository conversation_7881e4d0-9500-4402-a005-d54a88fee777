package com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.AutoRenewDutType;

/**
 * <AUTHOR>
 * @date 2021/8/17 15:22
 */
public interface AutoRenewDutTypeDao {

    /**
     * 查询所有validEndTime不为null的有效记录
     * @return
     */
    List<AutoRenewDutType> selectValidEndTimeNotNullRecords();

    Integer getPayChannelByDutType(Integer dutType);

    Integer getVipTypeByDutType(Integer dutType);

}
