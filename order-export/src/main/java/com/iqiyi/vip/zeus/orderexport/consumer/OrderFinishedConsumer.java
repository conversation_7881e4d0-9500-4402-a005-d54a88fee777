package com.iqiyi.vip.zeus.orderexport.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.orderexport.handler.OrderFinishedHandler;

/**
 * @Author: <PERSON>
 * @Date: 2022/08/16
 */
@Slf4j
@Component
public class OrderFinishedConsumer extends BaseRMQConsumer {
    @Resource
    private OrderFinishedHandler orderFinishedHandler;

    @Override
    protected ConsumeConcurrentlyStatus doConsume(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt messageExt : list) {
            String msgBody = null;
            try {
                msgBody = new String(messageExt.getBody(), StandardCharsets.UTF_8);
                Map<String, String> orderMap = JSON.parseObject(msgBody, Map.class);
                String orderCode = orderMap.get("orderCode");
                log.info("Consume order finished msg. msgId: {}, orderCode:{}", messageExt.getMsgId(), orderCode);
                orderFinishedHandler.handle(orderMap);
            } catch (Exception e) {
                log.error("process order finished msg error. msgId:{}, msgBody:{}", messageExt.getMsgId(), msgBody, e);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

}
