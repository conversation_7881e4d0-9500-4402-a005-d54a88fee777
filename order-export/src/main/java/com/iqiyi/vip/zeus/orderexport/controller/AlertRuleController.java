package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.AlertRuleOperator;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.KeyValuePair;
import com.iqiyi.vip.zeus.core.model.ZeusAlertRule;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.req.AlertRuleSearchParam;
import com.iqiyi.vip.zeus.core.service.ZeusAlertRuleService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.param.AlertRuleCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.validator.alertrule.ZeusAlertRuleValidator;
import com.iqiyi.vip.zeus.orderexport.validator.result.AlertRuleCreateParamCheckResult;
import com.iqiyi.vip.zeus.orderexport.validator.result.AlertRuleUpdateParamCheckResult;

/**
 * @author: guojing
 * @date: 2023/12/2 18:19
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/alertRule")
@Api(tags = "宙斯告警规则相关接口")
public class AlertRuleController {

    @Resource
    private ZeusAlertRuleService zeusAlertRuleService;
    @Resource
    private ZeusAlertRuleValidator zeusAlertRuleValidator;

    /**
     * 告警规则条件操作符枚举信息
     */
    @ApiOperation(value = "告警规则条件操作符枚举列表")
    @GetMapping(value = "/conditionOperatorTypes")
    public BaseResponse<List<KeyValuePair<String, String>>> conditionOperatorTypes() {
        List<KeyValuePair<String, String>> typeList = Arrays.stream(AlertRuleOperator.values())
            .map(type -> new KeyValuePair<>(type.name(), type.getDesc()))
            .collect(Collectors.toList());
        return BaseResponse.createSuccessList(typeList);
    }

    /**
     * 创建告警规则
     * @param createParam
     * @return 告警规则id
     */
    @ApiOperation(value = "创建告警规则")
    @PostMapping(value = "/create")
    public BaseResponse<Integer> create(@Validated @RequestBody AlertRuleCreateOrUpdateParam createParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        AlertRuleCreateParamCheckResult alertRuleCreateParamCheckResult = zeusAlertRuleValidator.checkCreateParam(createParam);
        ZeusMonitor zeusMonitor = alertRuleCreateParamCheckResult.getZeusMonitor();
        ZeusAlertRule createAlertRuleData = createParam.toZeusAlertRule();
        createAlertRuleData.setTeamCode(realTeam.getTeamCode());
        createAlertRuleData.setCreateUser(currentUser.getOaAccount());
        createAlertRuleData.setUpdateUser(currentUser.getOaAccount());
        return BaseResponse.createSuccess(zeusAlertRuleService.create(createAlertRuleData, zeusMonitor, alertRuleCreateParamCheckResult.getDashboardWithMeta()));
    }

    /**
     * 更新告警规则
     * @param updateParam
     */
    @ApiOperation(value = "更新告警规则")
    @PostMapping(value = "/update")
    public BaseResponse<Boolean> update(@Validated @RequestBody AlertRuleCreateOrUpdateParam updateParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        AlertRuleUpdateParamCheckResult checkResult = zeusAlertRuleValidator.checkUpdateParam(updateParam, realTeam);
        ZeusAlertRule updateAlertRuleData = updateParam.toZeusAlertRule();
        updateAlertRuleData.setUpdateUser(currentUser.getOaAccount());
        return BaseResponse.createSuccess(zeusAlertRuleService.update(updateAlertRuleData, checkResult.getZeusMonitor(), checkResult.getDashboardWithMeta()));
    }

    /**
     * 删除告警规则
     * @param id
     */
    @ApiOperation(value = "删除告警规则")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "告警规则id", dataType = "Integer", required = true, paramType = "query")})
    @PostMapping(value = "/delete")
    public BaseResponse<Boolean> delete(@NotNull(message = "告警规则ID不能为空") Integer id) {
        ZeusAlertRule zeusAlertRule = zeusAlertRuleService.getById(id);
        if (zeusAlertRule == null) {
            throw BizException.newParamException("告警规则不存在");
        }
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        AuthorityTeamBasic realTeam = currentUser.getRealTeam();
        if (!Objects.equals(zeusAlertRule.getTeamCode(), realTeam.getTeamCode())) {
            throw BizException.newParamException("无权操作此告警规则");
        }
        return BaseResponse.createSuccess(zeusAlertRuleService.delete(zeusAlertRule));
    }

    @ApiOperation(value = "根据id查询告警规则详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "告警规则id", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getById")
    public BaseResponse<ZeusAlertRule> getById(@NotNull(message = "告警规则ID不能为空") Integer id) {
        return BaseResponse.createSuccess(zeusAlertRuleService.getFriendlyById(id));
    }

    @ApiOperation(value = "根据监控id查询告警规则详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "monitorId", value = "监控id", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getByMonitorId")
    public BaseResponse<ZeusAlertRule> getByMonitorId(@NotNull(message = "监控ID不能为空") Integer monitorId) {
        return BaseResponse.createSuccess(zeusAlertRuleService.getFriendlyById(monitorId));
    }

    /**
     * 搜索告警规则
     */
    @ApiOperation(value = "搜索告警规则")
    @GetMapping(value = "/search")
    public BaseResponse<List<ZeusAlertRule>> search(@Validated AlertRuleSearchParam searchParam) {
        return BaseResponse.createSuccessList(zeusAlertRuleService.search(searchParam));
    }

}
