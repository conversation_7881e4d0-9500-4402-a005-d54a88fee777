package com.iqiyi.vip.zeus.orderexport.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.entity.ExpiringData;
import com.iqiyi.vip.zeus.orderexport.model.ExpiringDataReq;
import com.iqiyi.vip.zeus.orderexport.persist.dao.ExpiringDataDao;
import com.iqiyi.vip.zeus.orderexport.util.CloudConfigUtil;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailComponent;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailHeader;
import com.iqiyi.vip.zeus.orderexport.util.mail.TableMailContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: Lin Peihui
 * @Date: 2023/3/6
 */
@Slf4j
@Component
@Profile("!sg")
public class ExpiringDataMonitorJob extends IJobHandler {
    @Resource
    private Map<String, ExpiringDataDao> expiringDataDaoMap = new HashMap<>();
    @Resource
    private AlterService alterService;
    @Resource
    MailComponent mailComponent;

    private static final String[] DEFAULT_ADDRESSEES = new String[]{"<EMAIL>"};

    @Job("expiringDataMonitorJob")
    @Override
    public void execute() {
        StopWatch stopWatch = StopWatch.createStarted();
        String param = JobHelper.getJobParam();
        Long jobId = JobHelper.getJobId();
        log.info("---------- Start execute expiringDataMonitorJob[{}] ----------", jobId);
        JobHelper.log("---------- Start execute expiringDataMonitorJob[{0}] ----------", jobId);
        List<ExpiringDataReq> expiringDataReqList = CloudConfigUtil.getExpiringDataReqList();
        if(CollectionUtils.isEmpty(expiringDataReqList)) {
            log.info("expiringDataReqList is empty. jobId:{}", jobId);
            return;
        }
        for (ExpiringDataReq req : expiringDataReqList) {
            try {
                List<ExpiringData> expiringDataList = getExpiringDataDao(req.getDatabase()).queryExpiringData(req);
                if (!CollectionUtils.isEmpty(expiringDataList)) {
                    //发送邮件
                    String[] addressees = StringUtils.isNotEmpty(param) ? param.split(",") : DEFAULT_ADDRESSEES;
                    sendMail(req, expiringDataList, addressees);

                    if (CloudConfigUtil.sendExpiringDataToHotChat()) {
                        //发送热聊
                        String alterContext = String.format("配置数据即将过期\n 模块:%s\n 详情:%s", req.getModule(), JSON.toJSON(expiringDataList));
                        alterService.sendHotChat("玄武-配置数据即将过期", alterContext,"");
                    }
                }
            } catch (Exception ex) {
                log.error("run each query error. req:{}", JSON.toJSON(req), ex);
            }
        }
        log.info("---------- expiringDataMonitorJob finished[{}]. cost:{}ms. ----------", jobId, stopWatch
            .getTime());
        JobHelper.log("---------- expiringDataMonitorJob finished[{0}], cost:{2}ms. ----------", jobId, stopWatch
            .getTime());
    }

    private ExpiringDataDao getExpiringDataDao(String database) {
        if ("store".equals(database.trim())) {
            return expiringDataDaoMap.get("storeExpiringDataDaoImpl");
        }
        if ("order".equals(database.trim())) {
            return expiringDataDaoMap.get("orderExpiringDataDaoImpl");
        }
        if ("payResult".equals(database.trim())) {
            return expiringDataDaoMap.get("payResultExpiringDataDaoImpl");
        }
        return null;
    }

    private void sendMail(ExpiringDataReq req, List<ExpiringData> expiringDataList, String[] addressees) {
        if (CollectionUtils.isEmpty(expiringDataList)) {
            return;
        }
        List<List<Object>> tableContents = Lists.newArrayList();
        for (ExpiringData expiringData : expiringDataList) {
            List<Object> oneRowData = Lists.newArrayList(
                String.valueOf(expiringData.getId()),
                expiringData.getDesc(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime()
            );
            tableContents.add(oneRowData);
        }

        MailHeader mailHeader = new MailHeader();
        mailHeader.setTos(addressees);
        mailHeader.setTitle(String.format("【重要提醒】交易配置数据即将到期-%s", req.getModule()));
        mailHeader.setNeedTitlePrefix(false);

        String tableComment = "下列数据即将到期，请产品和运营同学确认是否需要调整策略！！！";
        List<String> tableTitles = Lists.newArrayList("id", "描述", "开始时间", "结束时间");
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        log.info("[ExpiringDataMonitorJob] start send email");
        JobHelper.log("[ExpiringDataMonitorJob] start send email");
        try {
            mailComponent.sendMail(mailHeader, Collections.singletonList(mailContent));
            log.info("[ExpiringDataMonitorJob] send email finished, addressees:{}", Arrays.toString(addressees));
            JobHelper.log("[ExpiringDataMonitorJob] send email finished, addressees:{0}", Arrays.toString(addressees));
        } catch (Exception e) {
            log.error("[ExpiringDataMonitorJob] send mail occurred exception", e);
            JobHelper.log("[ExpiringDataMonitorJob] send mail occurred exception", e);
        }
    }

}
