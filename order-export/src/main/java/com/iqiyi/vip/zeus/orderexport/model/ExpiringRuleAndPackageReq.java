package com.iqiyi.vip.zeus.orderexport.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/27 15:36
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ExpiringRuleAndPackageReq {

    /**
     * 模块描述
     */
    private String module;
    /**
     * 数据库
     */
    private String database;
    /**
     * 表名
     */
    private String tableName;
}
