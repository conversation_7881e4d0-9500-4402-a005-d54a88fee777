package com.iqiyi.vip.zeus.orderexport.config;

import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.constant.MQConstant;
import com.iqiyi.vip.zeus.orderexport.consumer.AutoRenewAsyncTaskConsumer;
import com.iqiyi.vip.zeus.orderexport.consumer.BinlogOrderlyConsumer;
import com.iqiyi.vip.zeus.orderexport.consumer.OrderBinlogConsumer;
import com.iqiyi.vip.zeus.orderexport.consumer.OrderDelayMsgConsumer;
import com.iqiyi.vip.zeus.orderexport.consumer.OrderFinishedConsumer;
import com.iqiyi.vip.zeus.orderexport.consumer.OrderPaidConsumer;

/**
 * @Author: <PERSON>
 * @Date: 2020/11/23
 */
@Profile({"prod", "sg"})
@Configuration
public class RMQConfig {

    @Value("${mysql.order.binlog.rmq.namesrvaddr}")
    private String orderBinlogRmqNamesrvAddr;
    @Value("${autorenew.binlog.rmq.namesrvaddr}")
    private String autorenewBinlogRmqNameSrvAddr;
    @Value("${trade.order.rmq.namesrvaddr}")
    private String tradeOrderRmqNamesrvAddr;

    @Value("${mysql.order.binlog.rmq.consumer.token}")
    private String orderBinlogRmqConsumerToken;
    @Value("${autorenew.async.task.binlog.rmq.consumer.token}")
    private String autorenewAsyncTaskBinlogRmqConsumerToken;
    @Value("${autorenew.async.task.rmq.consumer.token}")
    private String autorenewAsyncTaskRmqConsumerToken;
    @Value("${autorenew.binlog.rmq.consumer.token}")
    private String autorenewBinlogRmqConsumerToken;
    @Value("${trade.order.rmq.paid.consumer.token}")
    private String tradeOrderRmqPaidConsumerToken;
    @Value("${trade.order.rmq.finished.consumer.token}")
    private String tradeOrderRmqFinishedConsumerToken;
    @Value("${trade.order.rmq.monitor.producer.token}")
    private String tradeOrderRmqMonitorProducerToken;
    @Value("${trade.order.rmq.monitor.consumer.token}")
    private String tradeOrderRmqMonitorConsumerToken;

    @Resource
    private OrderDelayMsgConsumer orderDelayMsgConsumer;
    @Resource
    private OrderPaidConsumer orderPaidConsumer;
    @Resource
    private OrderFinishedConsumer orderFinishedConsumer;
    @Resource
    private OrderBinlogConsumer orderBinlogConsumer;
    @Resource
    private BinlogOrderlyConsumer binlogOrderlyConsumer;
    @Resource
    private AutoRenewAsyncTaskConsumer autoRenewAsyncTaskConsumer;

    @Bean(name = "orderMonitorDelayProducer", initMethod = "start", destroyMethod = "shutdown")

    public DefaultMQProducer orderMonitorDelayProducer() {
        DefaultMQProducer producer = new DefaultMQProducer(MQConstant.TRADE_ORDER_MONITOR_DELAY_PRODUCER_GROUP);
        producer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        producer.setToken(tradeOrderRmqMonitorProducerToken);
        return producer;
    }

    @Bean(name = "orderMonitorDelayMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer orderMonitorDelayMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.TRADE_ORDER_MONITOR_DELAY_CONSUMER_GROUP);
        consumer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        consumer.setToken(tradeOrderRmqMonitorConsumerToken);
        consumer.subscribe(MQConstant.TRADE_ORDER_MONITOR_DELAY_TOPIC, "*");
        consumer.registerMessageListener(orderDelayMsgConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "orderBinlogMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer orderBinlogMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.MYSQL_ORDER_BINLOG_CONSUMER_GROUP);
        consumer.setNamesrvAddr(orderBinlogRmqNamesrvAddr);
        consumer.setToken(orderBinlogRmqConsumerToken);
        consumer.subscribe(MQConstant.MYSQL_ORDER_BINLOG_TOPIC, "*");
        consumer.registerMessageListener(orderBinlogConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "orderPaidMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer orderPaidMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.TRADE_ORDER_PAID_CONSUMER_GROUP);
        consumer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        consumer.setToken(tradeOrderRmqPaidConsumerToken);
        consumer.subscribe(MQConstant.TRADE_ORDER_PAID_TOPIC, "*");
        consumer.registerMessageListener(orderPaidConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "orderFinishedMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer orderFinishedMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.TRADE_ORDER_FINISHED_CONSUMER_GROUP);
        consumer.setNamesrvAddr(tradeOrderRmqNamesrvAddr);
        consumer.setToken(tradeOrderRmqFinishedConsumerToken);
        consumer.subscribe(MQConstant.TRADE_ORDER_FINISHED_TOPIC, "*");
        consumer.registerMessageListener(orderFinishedConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "autorenewAsyncTaskBinlogMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer autorenewAsyncTaskBinlogMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.AUTORENEW_ASYNC_TASK_BINLOG_CONSUMER_GROUP);
        consumer.setNamesrvAddr(autorenewBinlogRmqNameSrvAddr);
        consumer.setToken(autorenewAsyncTaskBinlogRmqConsumerToken);
        consumer.subscribe(MQConstant.AUTORENEW_ASYNC_TASK_BINLOG_TOPIC, "*");
        consumer.registerMessageListener(binlogOrderlyConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "autorenewAsyncTaskMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer autorenewAsyncTaskMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.AUTORENEW_ASYNC_TASK_CONSUMER_GROUP);
        consumer.setNamesrvAddr(autorenewBinlogRmqNameSrvAddr);
        consumer.setToken(autorenewAsyncTaskRmqConsumerToken);
        consumer.subscribe(MQConstant.AUTORENEW_ASYNC_TASK_TOPIC, "*");
        consumer.registerMessageListener(autoRenewAsyncTaskConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

    @Bean(name = "autorenewBinlogMsgConsumer", initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer autorenewBinlogMsgConsumer() throws Exception {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(MQConstant.AUTORENEW_BINLOG_CONSUMER_GROUP);
        consumer.setNamesrvAddr(autorenewBinlogRmqNameSrvAddr);
        consumer.setToken(autorenewBinlogRmqConsumerToken);
        consumer.subscribe(MQConstant.AUTORENEW_BINLOG_TOPIC, "*");
        consumer.registerMessageListener(binlogOrderlyConsumer);
        consumer.setConsumeThreadMin(10);
        consumer.setConsumeThreadMax(32);
        return consumer;
    }

}
