package com.iqiyi.vip.zeus.orderexport.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.zeus.orderexport.client.CommodityClient;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.component.impl.OrderCouponService;
import com.iqiyi.vip.zeus.orderexport.component.impl.PromotionService;
import com.iqiyi.vip.zeus.orderexport.entity.ErrorPriceInfo;
import com.iqiyi.vip.zeus.orderexport.entity.LowPriceInfo;
import com.iqiyi.vip.zeus.orderexport.entity.Product;
import com.iqiyi.vip.zeus.orderexport.entity.RaisePrice;
import com.iqiyi.vip.zeus.orderexport.entity.UserPoints;
import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;
import com.iqiyi.vip.zeus.orderexport.enums.OrderTypeEnum;
import com.iqiyi.vip.zeus.orderexport.enums.ProductChargeTypeEnum;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.model.OrderReferDto;
import com.iqiyi.vip.zeus.orderexport.model.UserOrderTooOftenInfo;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEvent;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.ProductDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.TidbOrderDao;

/**
 * @Author: Lin Peihui
 * @Date: 2020/11/24
 */
@Slf4j
@Component
@Profile("!sg")
public class MainLandMetricExportHandler extends AbstractMetricExportHandler implements MetricExportHandler {

    @Resource
    private ProductDao productDao;
    @Resource
    private CloudConfig cloudConfig;
    @Resource
    private OrderCouponService orderCouponService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private PromotionService promotionService;
    @Resource
    private AlterService alterService;
    @Resource
    private CommodityClient commodityClient;
    @Resource
    private TidbOrderDao tidbOrderDao;

    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    private static final String DO_SELF_TEST_REDIS_KEY = "doSelfTest";
    private static final String USER_ORDER_COUNT_LIMIT_CONFIG_KEY = "userOrderCountLimit";
    private static final Integer USER_ORDER_COUNT_LIMIT_DEFAULT_VALUE = 50;
    /**
     * TV 平台
     */
    private final static Long TV_PLATFORM = 805L;
    /**
     * TV 订单监控忽略的支付类型
     */
    private static final ImmutableSet<Integer> TV_ORDER_IGNORE_PAY_TYPES = ImmutableSet.of(307, 407, 381);
    private static final ImmutableSet<Integer> LOW_PRICE_MONITOR_EXCLUDE_PAY_TYPE = ImmutableSet.of(6, 98, 99, 100, 303, 304, 305, 307, 377);
    private static final ImmutableSet<Integer> LOW_PRICE_MONITOR_EXCLUDE_TYPE = ImmutableSet.of(-1, 11, 12);
    private static final ImmutableSet<String> LOW_PRICE_MONITOR_ORDER_TYPE = ImmutableSet.of("MAIN_PROD", "SUB_PROD");
    private static final ImmutableSet<String> FREE_ORDER_ACT_CODE_SET = ImmutableSet
        .of("vip_present", "presentGold", "presentDiamond", "presentPlatinum", "presentIqyPlatinum", "presentGoldsub", "presentDiamondsub", "presentGoldsub", "JLSP_UNLOCKVIP", "vip_partner_present", "daoju_activity");
    private static final ImmutableSet<Integer> ORDER_TOO_OFTEN_EXCLUDE_TYPE = ImmutableSet.of(349, 377, 305);
    private static final ImmutableSet<String> TRADE_ORDER_TYPE = ImmutableSet.of("TRADE", "DEFAULT");
    private static final ImmutableSet<Integer> ORDER_STATUS_SET = ImmutableSet.of(1);

    private static final ImmutableSet<Integer> TYPE_SET = ImmutableSet.of(-3);


    @Override
    protected boolean hasHandled(OrderDto order) {
        String cacheKey = generateKey(order.getOrderCode(), order.getStatus());
        Boolean isExist = redisTemplate.opsForValue().setIfAbsent(cacheKey, "1");
        if (isExist == null) {
            log.error("Set cache error. Order:{}. status:{}, notifyResult:{}",
                order.getOrderCode(), order.getStatus(), order.getNotifyResult());
            return false;
        }
        if (!isExist) {
            log.info("Order:{} has been handled. status:{}, notifyResult:{}",
                order.getOrderCode(), order.getStatus(), order.getNotifyResult());
            return true;
        }
        redisTemplate.expire(cacheKey, 24, TimeUnit.HOURS);
        return false;
    }

    @Override
    protected boolean needDoSelfTest() {
        Integer intervalCount = cloudConfig.getIntProperty("doTest_interval_count", -1);
        if (intervalCount == -1) {
            return false;
        }
        Long count = redisTemplate.opsForValue().increment(DO_SELF_TEST_REDIS_KEY);
        if (count == 1L) {
            redisTemplate.expire(DO_SELF_TEST_REDIS_KEY, 24, TimeUnit.HOURS);
        }
        return count % intervalCount == 0;
    }

    @Override
    protected void countExtraMetrics(CanalEvent<OrderDto> event, Tag payChannelTag) {
        OrderDto order = event.getRowAfter();
        Product product = productDao.getById(order.getProductId());

        countLowPrice(order);
        selfTestLowPrice();

        countTradeCode(order);

        countCouponReused(order);
        selfTestCouponReused();

        countUserOrderTooOften(order);
        selfTestUserOrderTooOften();

        countAllDimensionLimit(order);

        countIllegalUpdateTime(event);
        selfTestIllegalUpdateTime();

        countOrderStatusFlowError(event, product);
        selfTestOrderStatusFlowError(product);

        countTvUuidMiss(order);

        countSkuConsistency(order);
        selfTestSkuConsistency();

        countPriceDiff(order, payChannelTag);

        countFrVersionTooLong(order, payChannelTag);

        countReferTooLong(order, payChannelTag);

        countReturnUrlTooLong(order, payChannelTag);

        countIllegalSkuAmount(order);

        Boolean raisePriceSwitch = cloudConfig.getBooleanProperty("raisePriceSwitch", false);
        if (raisePriceSwitch) {
            countPriceRange(order);
        }
    }

    private void countTradeCode(OrderDto order) {
        if (StringUtils.isBlank(order.getTradeCode())) {
            return;
        }
        if (!ORDER_STATUS_SET.contains(order.getStatus())) {
            return;
        }
        if (TYPE_SET.contains(order.getType())) {
            return;
        }
        if (StringUtils.isBlank(order.getOrderType()) || TRADE_ORDER_TYPE.contains(order.getOrderType())) {
            Tag tradeTag = new ImmutableTag("tradeCode", String.valueOf(order.getTradeCode()));
            String key = "tradeCode_".concat(order.getTradeCode());
            Boolean isExist = redisTemplate.opsForValue()
                .setIfAbsent(key, String.valueOf(order.getTradeCode()), 12, TimeUnit.HOURS);
            if (isExist != null && !isExist) {
                log.info("has been create.,orderCode:{},tradeCode:{}",
                    order.getOrderCode(), order.getTradeCode());
                List<Tag> tags = Arrays.asList(tradeTag);
                Metrics.counter("repeat_create_order", tags).increment();
            }
        }
    }

    /**
     * 代金券重复使用
     */
    private void countCouponReused(OrderDto order) {
        boolean isCouponReused = orderCouponService.isCouponReused(order, false);
        if (isCouponReused) {
            Tag couponReusedTag = new ImmutableTag("couponReused", "1");
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(couponReusedTag);
            Metrics.counter("huiyuan_order_coupon_reused", orderTotal).increment();
            String alterContext = String.format("orderCode:%s, userId:%d", order.getOrderCode(), order.getUserId());
            alterService.sendHotChat("玄武-代金券重复使用", alterContext, "");
            log.warn("Find coupon reused. orderCode:{}", order.getOrderCode());
        }
    }

    /**
     * 代金券重复使用
     */
    public void selfTestCouponReused() {
        if (!needDoSelfTest()) {
            return;
        }
        boolean isCouponReused;
        OrderDto order = OrderDto.builder().orderCode("202304201556542738430035650002").status(1).notifyResult(null).couponFee(500).build();
        try {
            isCouponReused = orderCouponService.isCouponReused(order, true);
        } catch (Exception e) {
            log.error("Self test coupon reused throw exception. orderCode:{}", order.getOrderCode(), e);
            isCouponReused = false;
        }
        if (!isCouponReused) {
            String alterContext = String.format("逻辑自检失败-代金券重复使用。orderCode:%s", order.getOrderCode());
            alterService.sendHotChat("玄武-逻辑自检-代金券重复使用", alterContext, "");
            log.info("Self test coupon reused failed. orderCode:{}", order.getOrderCode());
        }
    }

    /**
     * 上报短时间内频繁下单的用户
     */
    public void countUserOrderTooOften(OrderDto order) {
        UserOrderTooOftenInfo info = userOrderTooOften(order);
        if (info != null) {
            Tag userIdTag = new ImmutableTag("userId", String.valueOf(order.getUserId()));
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(userIdTag);
            Metrics.counter("huiyuan_order_user_order_too_often", orderTotal).increment();
            String alterContext = String.format("uId:%d, count:%d.\n toB订单量:%d, toC订单量:%d (激活码订单量:%d)",
                order.getUserId(), info.getActualCount(), info.getPartnerOrderCount(), info.getNotPartnerOrderCount(), info.getExpCount());
            alterService.sendHotChat("玄武-用户频繁下单", alterContext, "");
            log.info("User order too often. uid:{}, count:{}", order.getUserId(), info.getRedisCount());
        }
    }

    private void selfTestUserOrderTooOften() {
        if (!needDoSelfTest()) {
            return;
        }
        boolean selfTestUserOrderTooOften = cloudConfig.getBooleanProperty("selfTestUserOrderTooOften", true);
        if (!selfTestUserOrderTooOften) {
            return;
        }

        UserOrderTooOftenInfo info;
        OrderDto order = OrderDto.builder().orderCode("selfTest").userId(1L).status(1)
            .orderType(null).refer("{}").payType(64).build();
        try {
            Integer userOrderCountLimit = cloudConfig.getIntProperty(USER_ORDER_COUNT_LIMIT_CONFIG_KEY, USER_ORDER_COUNT_LIMIT_DEFAULT_VALUE);
            int count = userOrderCountLimit - 1;
            RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(
                "user_order_cn_" + order.getUserId(), Objects.requireNonNull(redisTemplate.getConnectionFactory()));
            redisAtomicInteger.set(count);
            redisAtomicInteger.expire(5, TimeUnit.SECONDS);
            info = userOrderTooOften(order);
        } catch (Exception e) {
            log.error("Self test user order too often throw exception. orderCode:{}", order.getOrderCode(), e);
            info = null;
        }
        if (info == null) {
            String alterContext = String.format("逻辑自检失败-用户频繁下单。orderCode:%s", order.getOrderCode());
            alterService.sendHotChat("玄武-逻辑自检-用户频繁下单", alterContext, "");
            log.info("Self test user order too often failed. orderCode:{}", order.getOrderCode());
        }
    }

    public UserOrderTooOftenInfo userOrderTooOften(OrderDto order) {
        //不统计 非已支付订单
        if (order.getStatus() != 1) {
            return null;
        }
        if (StringUtils.isNotBlank(order.getOrderType()) && !"TRADE".equals(order.getOrderType())) {
            return null;
        }
        if (ORDER_TOO_OFTEN_EXCLUDE_TYPE.contains(order.getPayType())) {
            return null;
        }
        if (isFreeActCode(order)) {
            return null;
        }
        Product product = productDao.getById(order.getProductId());
        if (product.getChargeType() == ProductChargeTypeEnum.PRODUCT_CHARGE_TYP_MARKETING.getValue()) {
            return null;
        }
        int redisCount;
        int expRedisCount;
        Long userId = order.getUserId();
        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(
            "user_order_cn_" + userId, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
        RedisAtomicInteger expRedisAtomicInteger = new RedisAtomicInteger(
            "user_order_cn_exp_" + userId, Objects.requireNonNull(redisTemplate.getConnectionFactory()));
        if (StringUtils.isBlank(order.getPartner())) {
            redisCount = redisAtomicInteger.addAndGet(1);
            //激活码订单
            if (order.getPayType() == 6) {
                expRedisCount = expRedisAtomicInteger.addAndGet(1);
                if (expRedisCount == 1L) {
                    expRedisAtomicInteger.expire(24, TimeUnit.HOURS);
                }
            }
        } else {
            redisCount = redisAtomicInteger.addAndGet(1000);
        }
        if (redisCount == 1L || redisCount == 1000L) {
            redisAtomicInteger.expire(24, TimeUnit.HOURS);
        }
        Integer notPartnerOrderCount = redisCount % 1000;
        Integer partnerOrderCount = redisCount / 1000;
        Integer actualCount = notPartnerOrderCount + partnerOrderCount;
        Integer userOrderCountLimit = cloudConfig.getIntProperty(USER_ORDER_COUNT_LIMIT_CONFIG_KEY, USER_ORDER_COUNT_LIMIT_DEFAULT_VALUE);
        if (!actualCount.equals(userOrderCountLimit)) {
            return null;
        }
        return UserOrderTooOftenInfo.builder().notPartnerOrderCount(notPartnerOrderCount)
            .partnerOrderCount(partnerOrderCount).actualCount(actualCount).expCount(expRedisAtomicInteger.get()).build();
    }

    /**
     * 统计全维度限购
     */
    private void countAllDimensionLimit(OrderDto order) {
        if (order.getStatus() != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
            return;
        }
        boolean hasTaken = promotionService.hasTakenPartInFavorAct(order);
        if (!hasTaken) {
            return;
        }
        Tag countTag = new ImmutableTag("count", "1");
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(countTag);
        Metrics.counter("huiyuan_order_all_dimension_limit", orderTotal).increment();
        log.info("All dimension limit. orderCode:{}, uid:{}", order.getOrderCode(), order.getUserId());
    }

    private void countIllegalUpdateTime(CanalEvent<OrderDto> event) {
        if (isIllegalUpdateTime(event)) {
            Tag orderTag = new ImmutableTag("order", "1");
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(orderTag);
            Metrics.counter("huiyuan_order_illegal_updateTime", orderTotal).increment();
            String alterContext = String.format("MQ消息的时间戳比订单更新时间大5分钟。orderCode:%s, msgTime:%s, updateTime:%s",
                event.getRowAfter().getOrderCode(), event.getTimestamp(), event.getRowAfter().getUpdateTime());
            alterService.sendHotChat("玄武-MQ消息的时间戳", alterContext, "");
            log.info("Illegal update time. orderCode:{}", event.getRowAfter().getOrderCode());
        }
    }

    private void selfTestIllegalUpdateTime() {
        if (!needDoSelfTest()) {
            return;
        }
        CanalEvent<OrderDto> testedEvent = new CanalEvent<>();
        boolean isIllegalUpdateTime;
        try {
            testedEvent.setEventType("UPDATE");
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            testedEvent.setTimestamp(String.valueOf(timestamp.getTime()));

            LocalDateTime localDateTime = timestamp.toLocalDateTime();
            LocalDateTime orderUpdateTime = localDateTime.minusMinutes(10);
            ZonedDateTime zonedDateTime = orderUpdateTime.atZone(ZoneId.systemDefault());
            OrderDto beforeOrder = OrderDto.builder().orderCode("selfTest").updateTime(Timestamp.from(zonedDateTime.toInstant())).build();
            OrderDto afterOrder = OrderDto.builder().orderCode("selfTest").updateTime(Timestamp.from(zonedDateTime.toInstant())).build();
            testedEvent.setRowBefore(beforeOrder);
            testedEvent.setRowAfter(afterOrder);

            isIllegalUpdateTime = isIllegalUpdateTime(testedEvent);
        } catch (Exception e) {
            log.error("Self test illegal updateTime throw exception. orderCode:{}", testedEvent.getRowAfter().getOrderCode(), e);
            isIllegalUpdateTime = false;
        }
        if (!isIllegalUpdateTime) {
            String alterContext = String.format("逻辑自检失败-订单的更新时间有误。orderCode:%s", testedEvent.getRowAfter().getOrderCode());
            alterService.sendHotChat("玄武-逻辑自检-单的更新时间有误", alterContext, "");
            log.info("Self test illegal updateTime failed. orderCode:{}", testedEvent.getRowAfter().getOrderCode());
        }
    }

    private boolean isIllegalUpdateTime(CanalEvent<OrderDto> event) {
        if (!"UPDATE".equals(event.getEventType())) {
            return false;
        }
        OrderDto beforeOrder = event.getRowBefore();
        OrderDto afterOrder = event.getRowAfter();
        Long beforeUpdateTimeLong = beforeOrder.getUpdateTime().getTime();
        Long afterUpdateTimeLong = afterOrder.getUpdateTime().getTime();
        Long messageTimeStampLong = Long.parseLong(String.valueOf(event.getTimestamp()));
        int minutes = (int) ((messageTimeStampLong - afterUpdateTimeLong) / (1000 * 60));
        if (beforeUpdateTimeLong.equals(afterUpdateTimeLong) && minutes >= 5) {
            return true;
        }
        return false;
    }

    private void countOrderStatusFlowError(CanalEvent<OrderDto> event, Product product) {
        if (!"UPDATE".equals(event.getEventType())) {
            return;
        }
        OrderDto beforeOrder = event.getRowBefore();
        OrderDto afterOrder = event.getRowAfter();
        boolean flowError = statusFlowError(beforeOrder.getStatus(), afterOrder, product);
        if (flowError) {
            Tag orderTag = new ImmutableTag("order", "1");
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(orderTag);
            Metrics.counter("huiyuan_order_status_flow_error", orderTotal).increment();
            String alterContext = String
                .format("订单状态流转有误。orderCode:%s, before:%d, after:%d", afterOrder.getOrderCode(), beforeOrder.getStatus(), afterOrder.getStatus());
            alterService.sendHotChat("玄武-订单状态流转有误", alterContext, "");
            log.info("Status flow error. orderCode:{}", afterOrder.getOrderCode());
        }
    }

    /**
     * 代金券重复使用
     */
    public void selfTestOrderStatusFlowError(Product product) {
        if (!needDoSelfTest()) {
            return;
        }
        CanalEvent<OrderDto> testedEvent = new CanalEvent<>();
        boolean orderStatusFlowError;
        try {
            OrderDto beforeOrder = OrderDto.builder().orderCode("selfTest")
                .status(OrderStatusEnum.PRE_PAID_NO_RIGHTS.getStatus()).build();
            OrderDto afterOrder = OrderDto.builder().orderCode("selfTest")
                .status(OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus()).build();
            testedEvent.setRowBefore(beforeOrder);
            testedEvent.setRowAfter(afterOrder);
            orderStatusFlowError = statusFlowError(beforeOrder.getStatus(), afterOrder, product);
        } catch (Exception e) {
            log.error("Self test order status flow error throw exception. orderCode:{}", testedEvent.getRowAfter().getOrderCode(), e);
            orderStatusFlowError = false;
        }
        if (!orderStatusFlowError) {
            String alterContext = String.format("逻辑自检失败-订单状态流转错误。orderCode:%s", testedEvent.getRowAfter().getOrderCode());
            alterService.sendHotChat("玄武-逻辑自检-订单状态流转错误", alterContext, "");
            log.info("Self test order status flow error failed. orderCode:{}", testedEvent.getRowAfter().getOrderCode());
        }
    }

    private boolean statusFlowError(Integer statusBefore, OrderDto afterOrder, Product product) {
        //TODO 等待产品将福利商品刷回来后再放开
//        if (product != null && product.getPrePaid() != null && product.getPrePaid() == 1) {
//            if (statusBefore == OrderStatusEnum.ORDER_STATUS_PROCESSING.getStatus()
//                && statusAfter == OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
//                return true;
//            }
//        }
        Integer statusAfter = afterOrder.getStatus();
//        if (statusBefore == OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()
//                && statusAfter == OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus()
//                && !"A00001".equals(afterOrder.getNotifyResult())) {
//            return true;
//        }
        if (statusBefore == OrderStatusEnum.PRE_PAID_NO_RIGHTS.getStatus()
            && statusAfter == OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus()) {
            return true;
        }
        return false;
    }

    /**
     * 监控TV订单fr_version中uuid缺失的情况
     */
    private void countTvUuidMiss(OrderDto order) {
        if (!TV_PLATFORM.equals(order.getPlatform())) {
            return;
        }
        if (TV_ORDER_IGNORE_PAY_TYPES.contains(order.getPayType())) {
            return;
        }
        if (StringUtils.isNotBlank(order.getRefer()) && order.getRefer().contains("vip_present")) {
            return;
        }
        if (order.getAutoRenew() == null || order.getAutoRenew() == 2) {
            return;
        }
        if (StringUtils.isNotBlank(order.getFrVersion()) && order.getFrVersion().contains("uuid=")) {
            return;
        }
        Tag orderTag = new ImmutableTag("order", "1");
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(orderTag);
        Metrics.counter("huiyuan_order_tv_uuid_miss", orderTotal).increment();
        log.info("Tv uuid miss. orderCode:{}", order.getOrderCode());
    }

    /**
     * 校验 订单上skuId里的pid 与 订单pd 值是否一致
     */
    private void countSkuConsistency(OrderDto order) {
        if (!isSkuConsistency(order)) {
            Tag orderTag = new ImmutableTag("order", "1");
            List<Tag> orderTotal = Lists.newArrayList();
            orderTotal.add(orderTag);
            Metrics.counter("huiyuan_order_sku_consistency", orderTotal).increment();
            String alterContext = String.format("outPid:%d, orderCode:%s", order.getProductId(), order.getOrderCode());
            alterService.sendHotChat("玄武-sku里的pid与外层pid不一致", alterContext, "");
            log.info("sku包的pid 与 外层pid 不一致。outPid:{}, orderCode:{}", order.getProductId(), order.getOrderCode());
        }
    }

    public void selfTestSkuConsistency() {
        if (!needDoSelfTest()) {
            return;
        }
        boolean isSkuConsistency;
        OrderDto order = OrderDto.builder().orderCode("selfTest")
            .status(OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()).skuId("sku_177079571906272349").productId(4L).build();
        try {
            isSkuConsistency = isSkuConsistency(order);
        } catch (Exception e) {
            log.error("Self test sku consistency throw exception. orderCode:{}", order.getOrderCode(), e);
            isSkuConsistency = false;
        }
        if (!isSkuConsistency) {
            String alterContext = String.format("逻辑自检失败-校验 订单skuId里的pid 与 订单pid 的一致性。orderCode:%s", order.getOrderCode());
            alterService.sendHotChat("玄武-sku里的pid与外层pid不一致", alterContext, "");
            log.info("Self test sku consistency failed. orderCode:{}", order.getOrderCode());
        }
    }

    private boolean isSkuConsistency(OrderDto order) {
        if (StringUtils.isBlank(order.getSkuId())) {
            return true;
        }
        if (order.getStatus() != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()) {
            return true;
        }
        Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(order.getSkuId());
        if (!skuResponseOptional.isPresent()) {
            log.error("Query commodity by sku is not present. skuId:{}, orderCode:{}", order.getSkuId(), order.getOrderCode());
            return true;
        }
        Long productIdInSku = null;
        QuerySkuResponse skuResponse = skuResponseOptional.get();
        Map<String, Object> specAttributes = skuResponse.getSpecAttributes();
        if (Objects.nonNull(specAttributes.get("productId")) && !"".equals(specAttributes.get("productId"))) {
            productIdInSku = Long.parseLong(specAttributes.get("productId").toString());
        }
        if (productIdInSku == null) {
            log.error("Product id in sku is null. orderCode:{}, skuId:{}", order.getOrderCode(), order.getSkuId());
            return true;
        }
        if (order.getProductId().equals(productIdInSku)) {
            return true;
        }
        //对外合作加价购交易单写死的pid
        if (1712694 == productIdInSku && 22490 == order.getProductId()) {
            return true;
        }
        return false;
    }

    private boolean isFreeActCode(OrderDto order) {
        Optional<OrderReferDto> referDtoOptional = order.extractReferDto();
        if (!referDtoOptional.isPresent()) {
            return false;
        }
        String actCode = referDtoOptional.get().getActCode();
        if (StringUtils.isBlank(actCode)) {
            return false;
        }
        if (FREE_ORDER_ACT_CODE_SET.contains(actCode.trim())) {
            return true;
        }
        if (actCode.trim().startsWith("git_card_")) {
            return true;
        }
        if (StringUtils.isNotBlank(order.getTradeCode()) && order.getTradeCode().startsWith("vip-benefit_")) {
            return true;
        }
        return false;
    }

    private void countPriceRange(OrderDto order) {
        Integer productId = order.getProductId().intValue();
        if (order.getStatus() != 1) {
            return;
        }
        String priceConfig = cloudConfig.getProperty("raisePrice", "");
        if (StringUtils.isBlank(priceConfig)) {
            return;
        }
        List<RaisePrice> raisePriceList = GSON.fromJson(priceConfig,
            new TypeToken<List<RaisePrice>>() {
            }.getType());
        for (RaisePrice raisePrice : raisePriceList) {
            if (!productId.equals(raisePrice.getPid())) {
                continue;
            }
            if (!order.getAmount().equals(raisePrice.getAmount())) {
                continue;
            }
            if (order.getAutoRenew() == null || order.getAutoRenew() == 0) {
                if (raisePrice.getAutoRenew() != 0) {
                    continue;
                }
            }
            if (order.getAutoRenew() != null && (order.getAutoRenew() == 1 || order.getAutoRenew() == 3)) {
                if (raisePrice.getAutoRenew() != 1) {
                    continue;
                }
            }
            if (order.getAutoRenew() != null && order.getAutoRenew() == 2) {
                if (raisePrice.getAutoRenew() != 2) {
                    continue;
                }
            }
            String isNewPrice = "0";
            if (order.getRealFee() > raisePrice.getRealFeeSeparator()) {
                isNewPrice = "1";
            }
            buildAndIncrementRaisePriceMetrics(order, isNewPrice);
        }
    }

    private void buildAndIncrementRaisePriceMetrics(OrderDto order, String isNewPrice) {
        Tag productTag = new ImmutableTag("pid", String.valueOf(order.getProductId()));
        Tag amountTag = new ImmutableTag("amount", String.valueOf(order.getAmount()));
        Tag isNewPriceTag = new ImmutableTag("isNewPrice", isNewPrice);
        Tag autoRenewTag = new ImmutableTag("autoRenew", String.valueOf(order.getAutoRenew()));
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(productTag);
        orderTotal.add(amountTag);
        orderTotal.add(isNewPriceTag);
        orderTotal.add(autoRenewTag);
        Metrics.counter("huiyuan_order_raise_price", orderTotal).increment();
        log.info("Raise price. orderCode:{}, realFee:{}", order.getOrderCode(), order.getRealFee());
    }

    private void countLowPrice(OrderDto order) {
        LowPriceInfo lowPriceInfo = getLowPriceInfo(order);
        if (lowPriceInfo != null) {
            buildAndIncrementLowPriceMetrics(order, lowPriceInfo);
        }
    }

    private void selfTestLowPrice() {
        if (!needDoSelfTest()) {
            return;
        }
        OrderDto order = OrderDto.builder().orderCode("202301181831316457130031050002").status(1).chargeType(1).payType(384)
            .type(1).orderType("MAIN_PROD").productId(4L).amount(12).autoRenew(3).realFee(1).build();
        LowPriceInfo lowPriceInfo;
        try {
            lowPriceInfo = getLowPriceInfo(order);
        } catch (Exception e) {
            log.error("Self test low price throw exception. orderCode:{}", order.getOrderCode(), e);
            lowPriceInfo = null;
        }
        if (lowPriceInfo == null) {
            String alterContext = String.format("逻辑自检失败-低价单监控。orderCode:%s", order.getOrderCode());
            alterService.sendHotChat("玄武-逻辑自检-低价单", alterContext, "");
            log.info("Self test low price time failed. orderCode:{}", order.getOrderCode());
        }
    }

    /**
     * 是否是低价单
     */
    private LowPriceInfo getLowPriceInfo(OrderDto order) {
        Integer productId = order.getProductId().intValue();
        if (order.getStatus() != 1
            || order.getChargeType() != 1
            || StringUtils.isNotBlank(order.getPartner())
            || LOW_PRICE_MONITOR_EXCLUDE_PAY_TYPE.contains(order.getPayType())
            || LOW_PRICE_MONITOR_EXCLUDE_TYPE.contains(order.getType())
            || (StringUtils.isNotBlank(order.getOrderType()) && !LOW_PRICE_MONITOR_ORDER_TYPE.contains(order.getOrderType()))
            || (order.getAutoRenew() != null && order.getAutoRenew() == 2)) {
            return null;
        }
        if (isFreeActCode(order)) {
            return null;
        }

        lowPriceMulti(order);

        //监控错误价格
        monitorErrorPrice(order);

        String lowPriceLimitConfig = cloudConfig.getProperty("lowPriceLimit", "");
        if (StringUtils.isBlank(lowPriceLimitConfig)) {
            return null;
        }
        List<LowPriceInfo> lowPriceInfoList = GSON.fromJson(lowPriceLimitConfig,
            new TypeToken<List<LowPriceInfo>>() {
            }.getType());
        for (LowPriceInfo lowPriceInfo : lowPriceInfoList) {
            if (!productId.equals(lowPriceInfo.getPid())) {
                continue;
            }
            if (!order.getAmount().equals(lowPriceInfo.getAmount())) {
                continue;
            }
            if (order.getRealFee() < lowPriceInfo.getUpperRealFee()) {
                return lowPriceInfo;
            }
        }
        return null;
    }

    private void monitorErrorPrice(OrderDto order) {
        if (StringUtils.isBlank(order.getSkuId())) {
            return;
        }
        String errorPriceLimitConfig = cloudConfig.getProperty("errorPriceLimit", "");
        if (StringUtils.isBlank(errorPriceLimitConfig)) {
            return;
        }
        List<ErrorPriceInfo> errorPriceInfoList = GSON.fromJson(errorPriceLimitConfig,
            new TypeToken<List<ErrorPriceInfo>>() {
            }.getType());
        for (ErrorPriceInfo errorPriceInfo : errorPriceInfoList) {
            if (!order.getSkuId().equals(errorPriceInfo.getSkuId())) {
                continue;
            }
            if (order.getRealFee().equals(errorPriceInfo.getErrorFee())) {
                String alterContext = String
                    .format("实付价格错误。orderCode:%s,userId:%s,skuId:%s,realFee:%d", order.getOrderCode(), order.getUserId(), order.getSkuId(), order
                        .getRealFee());
                log.info(alterContext);
                alterService.sendHotChat("玄武-实付价格错误", alterContext, "");
            }
        }
    }

    private void lowPriceMulti(OrderDto order) {
        String lowPriceLimitConfig = cloudConfig.getProperty("multi_buy_Limit_pid", "");
        log.info("lowPriceMulti lowPriceLimitConfig:{}", lowPriceLimitConfig);
        Integer lowPriceLimit = cloudConfig.getIntProperty("multi_buy_Limit_price", null);

        if (StringUtils.isBlank(lowPriceLimitConfig) || Objects.isNull(lowPriceLimit)) {
            return;
        }

        if (!Splitter.on(",").trimResults().splitToList(lowPriceLimitConfig).contains(String.valueOf(order.getProductId()))) {
            log.info("lowPriceMulti pid not match. pid:{}", order.getProductId());
            return;
        }

        if (order.getAmount() != 12) {
            log.info("lowPriceMulti amount not match. amount:{}", order.getAmount());
            return;
        }

        if (order.getRealFee() == 0 || order.getRealFee() > lowPriceLimit) {
            return;
        }

        String key = "multi_buy_v1".concat(String.valueOf(order.getUserId())).concat(String.valueOf(order.getProductId()));
        Long count = redisTemplate.opsForValue().increment(key);
        if (Objects.nonNull(count) && count.intValue() <= 2) {
            redisTemplate.expire(key, 168, TimeUnit.HOURS);
        } else if (Objects.nonNull(count) && count.intValue() > 2) {
            String alterContext = String
                .format("orderCode:%s,userId:%s,productId:%s,count:%s", order.getOrderCode(), order.getUserId(), order
                    .getProductId(), count);
            log.info(alterContext);
            alterService.sendHotChat("玄武-双十一低价(同一用户参与多次)", alterContext, "");
        }
    }

    private void buildAndIncrementLowPriceMetrics(OrderDto order, LowPriceInfo lowPriceInfo) {
        Tag productTag = new ImmutableTag("productId", String.valueOf(order.getProductId()));
        Tag amountTag = new ImmutableTag("amount", String.valueOf(order.getAmount()));
        Tag upperRealFeeTag = new ImmutableTag("upperRealFee", String.valueOf(lowPriceInfo.getUpperRealFee()));
        Integer fee = order.getFee();
        Integer coupon = order.getCouponFee() == null ? 0 : order.getCouponFee();
        Integer redPacket = 0, pointDiscount = 0;
        Optional<OrderReferDto> referDtoOptional = order.extractReferDto();
        if (referDtoOptional.isPresent()) {
            OrderReferDto referDto = referDtoOptional.get();
            Map<String, Object> property = referDto.getBusinessProperty();
            if (property != null) {
                redPacket = (Integer) property.getOrDefault("redPacketPrice", 0);
                String pointsDiscountStr = (String) property.getOrDefault("pointsDiscount", "");
                if (StringUtils.isNotBlank(pointsDiscountStr)) {
                    List<UserPoints> userPointsList = JSON.parseArray(pointsDiscountStr, UserPoints.class);
                    pointDiscount = userPointsList.get(0).getDiscountAmount();
                }
            }
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(productTag);
        orderTotal.add(amountTag);
        orderTotal.add(upperRealFeeTag);
        Metrics.counter("huiyuan_order_low_price", orderTotal).increment();
        String alterContext = String.format("%s。\norderCode:%s,\nuid:%d\n实付:%d, 阈值:%d。价格服务返回的价格:%d, 红包:%d, 代金券:%d, 积分/金币立减:%d",
            order.getName(), order.getOrderCode(), order.getUserId(), order.getRealFee(), lowPriceInfo
                .getUpperRealFee(), fee, redPacket, coupon, pointDiscount);

        alterService.sendHotChat("玄武-低价订单", alterContext, "");
        log.warn("Find low price order. orderCode:{}, realFee:{}", order.getOrderCode(), order.getRealFee());
    }

    /**
     * 比对收银台系统及下单系统不一致的价格
     */
    private void countPriceDiff(OrderDto order, Tag payChannelTag) {
        if (StringUtils.isNotBlank(order.getOrderType()) && !TRADE_ORDER_TYPE.contains(order.getOrderType())) {
            return;
        }
        if (order.getStatus() != OrderStatusEnum.ORDER_STATUS_PROCESSING.getStatus()) {
            return;
        }
        //排除扫码支付
        if (StringUtils.isNotBlank(order.getFrVersion()) && order.getFrVersion().contains("paymentQuick")) {
            return;
        }
        String storePriceActCode = "", redPacketBatchCode = "";
        String orderActCode = order.getMainActCode();
        Optional<OrderReferDto> referDtoOptional = order.extractReferDto();
        if (referDtoOptional.isPresent()) {
            OrderReferDto referDto = referDtoOptional.get();

            Map<String, Object> property = referDto.getBusinessProperty();
            if (property != null) {
                String orderExtStr = String.valueOf(property.getOrDefault("orderExt", ""));
                if (StringUtils.isBlank(orderExtStr)) {
                    return;
                }
                JSONObject orderExtJsonObj = JSONObject.parseObject(orderExtStr);
                storePriceActCode = orderExtJsonObj.getString("priceActCode");
                orderActCode = String.valueOf(property.getOrDefault("priceActCodeActual", ""));
                redPacketBatchCode = String.valueOf(property.getOrDefault("redPacketBatchCode", ""));
            }
        }
        String orderPriceActCode = StringUtils.isNotBlank(orderActCode) ? orderActCode : redPacketBatchCode;
        if (StringUtils.isBlank(storePriceActCode) || StringUtils.isBlank(orderPriceActCode)) {
            return;
        }
        if (StringUtils.equals(storePriceActCode, orderPriceActCode)) {
            return;
        }

        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payChannelTag);
        Metrics.counter("huiyuan_order_diff_price_total", orderTotal).increment();
        String alterContext = String.format("%s\norderCode: %s\nuid: %d\nrealFee: %d\n收银台命中code: %s\n订单命中code: %s",
            order.getName(), order.getOrderCode(), order.getUserId(), order.getRealFee(), storePriceActCode, orderPriceActCode);
        String receiveUsers = cloudConfig.getProperty("test_receiveUsers", "linpeihui");
        alterService.sendHotChat("玄武-价格不一致", alterContext, receiveUsers);
        log.warn("Find diff price. orderCode:{}, realFee:{}, storePriceActCode:{}, orderPriceActCode:{}",
            order.getOrderCode(), order.getRealFee(), storePriceActCode, orderPriceActCode);
    }

    private void countFrVersionTooLong(OrderDto order, Tag payChannelTag) {
        if (StringUtils.isBlank(order.getFrVersion())) {
            return;
        }
        Integer limit = cloudConfig.getIntProperty("frVersion_length_limit", 1024);
        if (order.getFrVersion().length() <= limit) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payChannelTag);
        Metrics.counter("huiyuan_order_frVersion_too_long_total", orderTotal).increment();
        log.warn("FrVersion too long. orderCode:{}, frVersion:{}", order.getOrderCode(), order.getFrVersion());
    }

    private void countReferTooLong(OrderDto order, Tag payChannelTag) {
        if (StringUtils.isBlank(order.getRefer())) {
            return;
        }
        Integer limit = cloudConfig.getIntProperty("refer_length_limit", 2048);
        if (order.getRefer().length() <= limit) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payChannelTag);
        Metrics.counter("huiyuan_order_refer_too_long_total", orderTotal).increment();
        log.warn("Refer too long. orderCode:{}, refer:{}", order.getOrderCode(), order.getRefer());
    }

    private void countReturnUrlTooLong(OrderDto order, Tag payChannelTag) {
        if (StringUtils.isBlank(order.getRefer())) {
            return;
        }
        Integer limit = cloudConfig.getIntProperty("returnUrl_length_limit", 450);
        if (StringUtils.isBlank(order.getReturnUrl()) || order.getReturnUrl().length() <= limit) {
            return;
        }
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(payChannelTag);
        Metrics.counter("huiyuan_order_returnUrl_too_long_total", orderTotal).increment();
        log.warn("ReturnUrl too long. orderCode:{}, returnUrl:{}", order.getOrderCode(), order.getReturnUrl());
    }

    private void countIllegalSkuAmount(OrderDto order) {
        if (order.getSkuAmount() == null) {
            return;
        }
        //过滤非支付的订单
        if (order.getStatus() != OrderStatusEnum.ORDER_STATUS_DELIVERED.getStatus()
            && order.getStatus() != OrderStatusEnum.PRE_PAID_NO_RIGHTS.getStatus()) {
            return;
        }
        if (order.getOrderType() != null && OrderTypeEnum.TRADE.name().equals(order.getOrderType())) {
            return;
        }
        if (StringUtils.isNotBlank(order.getPartner())) {
            return;
        }
        Product product = productDao.getById(order.getProductId());
        if (product.getChargeType() != ProductChargeTypeEnum.PRODUCT_CHARGE_TYP_VIP.getValue()) {
            return;
        }
        if (product.isDayPeriodUnit()) {
            return;
        }
        if (order.getSkuAmount() == 1) {
            return;
        }
        Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(order.getSkuId());
        if (!skuResponseOptional.isPresent()) {
            log.error("Query commodity by sku is not present. skuId:{}, orderCode:{}", order.getSkuId(), order.getOrderCode());
            return;
        }
        QuerySkuResponse skuResponse = skuResponseOptional.get();
        Map<String, Object> specAttributes = skuResponse.getSpecAttributes();
        Object supportUpdate = specAttributes.get("supportUpdate");
        Object timeLengthType = specAttributes.get("timeLengthType");
        //过滤升级全部
        if (Objects.equals(supportUpdate, "1") && Objects.equals(timeLengthType, "1")) {
            return;
        }
        Tag skuAmountTag = new ImmutableTag("skuAmount", String.valueOf(order.getSkuAmount()));
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(skuAmountTag);
        Metrics.counter("huiyuan_order_illegal_skuAmount_total", orderTotal).increment();
        String alterContext = String.format("%s\norderCode: %s\nuid: %d\nskuAmount: %d\n",
            order.getName(), order.getOrderCode(), order.getUserId(), order.getSkuAmount());
        String receiveUsers = cloudConfig.getProperty("test_receiveUsers", "linpeihui");
        alterService.sendHotChat("玄武-skuAmount不为1", alterContext, receiveUsers);
        log.warn("SkuAmount is illegal. orderCode:{}, name:{}, skuAmount:{}, productId:{}", order.getOrderCode(), order.getName(), order.getSkuAmount(), product.getId());
    }

}
