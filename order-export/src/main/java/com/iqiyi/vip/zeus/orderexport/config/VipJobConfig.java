package com.iqiyi.vip.zeus.orderexport.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * Created at: 2021-02-05
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class VipJobConfig {

//    @Value("${vip.job.admin.addresses}")
//    private String adminAddresses;
//    @Value("${vip.job.executor.appname}")
//    private String executorAppName;
//    @Value("${vip.job.executor.port}")
//    private int executorPort;
//    @Value("${vip.job.executor.logpath}")
//    private String executorLogPath;
//    @Value("${vip.job.accessToken}")
//    private String accessToken;
//    @Value("${vip.job.access.way}")
//    private String accessWay;
//    @Value("${vip.job.executor.switch}")
//    private String executorSwitch;
//    @Value("${vip.job.qae.api.access.key}")
//    private String qaeApiAccessKey;
//    @Value("${vip.job.qae.api.url}")
//    private String qaeApiUrl;
//    @Value("${vip.job.qae.app.id}")
//    private String appId;
//    @Value("${vip.job.qae.switch}")
//    private String qaeSwitch;

//    /**
//     * 配置执行器
//     */
//    @Bean(initMethod="start",destroyMethod="destroy")
//    public JobExecutor jobExecutor() {
//        JobExecutor jobExe = new JobExecutor();
//        jobExe.setAdminAddresses(adminAddresses);
//        jobExe.setAppName(executorAppName);
//        jobExe.setPort(executorPort);
//        jobExe.setLogPath(executorLogPath);
//        jobExe.setAccessToken(accessToken);
//        jobExe.setAccessWay(accessWay);
//        jobExe.setExecutorSwitch(executorSwitch);
//        return jobExe;
//    }

//    /**
//     * 配置注册器，虚机接入不需要配置
//     */
//    @Bean(initMethod="start",destroyMethod="destroy")
//    public QaeRegister qaeRegister() {
//        QaeRegister qaeRegister = new QaeRegister();
//        qaeRegister.setAppId(appId);
//        qaeRegister.setQaeApiUrl(qaeApiUrl);
//        qaeRegister.setQaeApiAccessKey(qaeApiAccessKey);
//        qaeRegister.setQaeSwitch(qaeSwitch);
//        qaeRegister.setJobExecutor(jobExecutor());
//        qaeRegister.setQaeSwitch(qaeSwitch);
//        qaeRegister.setRestTemplate(restTemplate());
//        return qaeRegister;
//    }

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(200);
        requestFactory.setReadTimeout(3000);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    private List<HttpMessageConverter<?>> transConverters(List<HttpMessageConverter<?>> oldMessageConverters){
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        for (HttpMessageConverter<?> converter: oldMessageConverters) {
            if (converter instanceof StringHttpMessageConverter) {
                messageConverters.add(stringHttpMessageConverter());
            } else {
                messageConverters.add(converter);
            }
        }
        messageConverters.add(jacksonHttpMessageConverter());
        return messageConverters;
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        return new StringHttpMessageConverter(StandardCharsets.UTF_8);
    }

    @Bean
    public MappingJackson2HttpMessageConverter jacksonHttpMessageConverter(){
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.parseMediaType(MediaType.APPLICATION_JSON_VALUE + ";UTF-8"));
        supportedMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE + ";UTF-8"));
        jacksonConverter.setSupportedMediaTypes(supportedMediaTypes);
        return jacksonConverter;
    }

}