package com.iqiyi.vip.zeus.orderexport.validator.monitor;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.Map;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;

/**
 * Created at: 2022-06-20
 *
 * <AUTHOR>
 */
@Slf4j
@Lazy(value = false)
@Profile("!sg")
@Component
public class MonitorConfigValidatorFactory implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private static final Map<DataSourceType, AbstractMonitorConfigValidator> monitorConfigValidatorMap = Maps.newEnumMap(DataSourceType.class);

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("MonitorConfigValidatorFactory-init start!");
        applicationContext.getBeansOfType(AbstractMonitorConfigValidator.class).values()
            .forEach(validator -> {
                log.info("MonitorConfigValidatorFactory-init:register handler {}  for DatasourceType:{}",
                    validator.getClass().getSimpleName(), validator.getDataSourceType());
                monitorConfigValidatorMap.put(validator.getDataSourceType(), validator);
            });
        log.info("MonitorConfigValidatorFactory-init end!");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public AbstractMonitorConfigValidator getValidator(DataSourceType type) {
        return monitorConfigValidatorMap.get(type);
    }

}
