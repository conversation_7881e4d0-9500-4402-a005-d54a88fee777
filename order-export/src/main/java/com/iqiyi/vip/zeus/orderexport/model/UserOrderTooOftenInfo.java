package com.iqiyi.vip.zeus.orderexport.model;

import lombok.Builder;
import lombok.Data;

/**
 * @Author: <PERSON>
 * @Date: 2023/3/24
 */
@Builder
@Data
public class UserOrderTooOftenInfo {

    /**
     * 非对外合作订单量
     */
    private Integer notPartnerOrderCount;
    /**
     * 对外合作订单量
     */
    private Integer partnerOrderCount;
    /**
     * 总订单量
     */
    private Integer actualCount;
    /**
     * 激活码订单量
     */
    private Integer expCount;
    /**
     * 缓存中的计数
     */
    private Integer redisCount;
}
