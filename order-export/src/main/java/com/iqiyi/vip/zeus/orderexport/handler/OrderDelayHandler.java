package com.iqiyi.vip.zeus.orderexport.handler;

import com.google.common.collect.Lists;
import com.iqiyi.vip.order.dal.OrderRepository;
import com.iqiyi.vip.order.dal.model.Order;
import com.iqiyi.vip.zeus.orderexport.entity.OrderInfo;
import com.iqiyi.vip.zeus.orderexport.enums.OrderStatusEnum;
import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;

import static com.iqiyi.vip.zeus.core.constants.OrderRightConstants.*;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/16
 */
@Slf4j
@Component
public class OrderDelayHandler {

    @Resource
    private OrderPaidHandler orderPaidHandler;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Value("${intl.flag:false}")
    private Boolean isIntlEnv;

    public void monitorOpenVipRightFailed(String orderCode, Integer retriedCount) {
        OrderInfo order = getOrderInfo(orderCode);
        if (order == null) {
            log.error("Order information not found for orderCode: {}", orderCode);
            return;
        }
        if (order.getStatus() == OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus()) {
            return;
        }
        if ("A00000".equals(order.getNotifyResult()) || "A00001".equals(order.getNotifyResult())) {
            checkAndRecordRecoveredOrder(orderCode);
            return;
        }
        countVipRightOpenFailed(order.getOrderCode(), order.getNotifyResult());
        orderPaidHandler.sendDelayMsg(orderCode, ++retriedCount);
    }

    /**
     * 记录权益开通失败订单
     *
     * @param orderCode 订单编号
     * @param notifyResult 通知结果
     */
    private void countVipRightOpenFailed(String orderCode, String notifyResult) {
        log.info("Recording VIP right open failure for order: {}, notifyResult: {}", orderCode, notifyResult);

        String script = "local exists = redis.call('sismember', KEYS[2], ARGV[1]); " +
            "if exists == 0 then " +
            "    local newElement = ARGV[1] .. ':' .. ARGV[2]; " +
            "    redis.call('rpush', KEYS[1], newElement); " +
            "    redis.call('sadd', KEYS[2], ARGV[1]); " +
            "    redis.call('set', KEYS[3], ARGV[3]); " +
            "    return 2; " + // 新增插入
            "else " +
            "    local elements = redis.call('lrange', KEYS[1], 0, -1); " +
            "    local found = false; " +
            "    local newElement = ARGV[1] .. ':' .. ARGV[2]; " +
            "    for i, element in ipairs(elements) do " +
            "        local currentOrderCode = element:match('^(.-):'); " +
            "        if currentOrderCode == ARGV[1] then " +
            "            found = true; " +
            "            if element ~= newElement then " +
            "                redis.call('lrem', KEYS[1], 1, element); " +
            "                redis.call('rpush', KEYS[1], newElement); " +
            "                redis.call('set', KEYS[3], ARGV[3]); " +
            "                return 1; " + // 更新
            "            end " +
            "            return 0; " + // 无操作
            "        end " +
            "    end " +
            "    if not found then " +
            "        redis.call('srem', KEYS[2], ARGV[1]); " +
            "        redis.call('rpush', KEYS[1], newElement); " +
            "        redis.call('sadd', KEYS[2], ARGV[1]); " +
            "        redis.call('set', KEYS[3], ARGV[3]); " +
            "        return 3; " + // 删除并插入
            "    end " +
            "end " +
            "return 0;"; // 无操作

        Long currentTimeMillis = System.currentTimeMillis();
        notifyResult = StringUtils.isBlank(notifyResult) ? "" : notifyResult.trim(); // 清理notifyResult
        String finalNotifyResult = notifyResult;
        Long result = stringRedisTemplate.execute((RedisCallback<Long>) connection -> {
            byte[] scriptBytes = script.getBytes(StandardCharsets.UTF_8);
            return connection.eval(scriptBytes, ReturnType.INTEGER, 3,
                RIGHT_FAILED_ORDERS_REDIS_KEY.getBytes(),
                RIGHT_FAILED_ORDERS_SET_REDIS_KEY.getBytes(),
                RIGHT_FAILED_TIMESTAMP_KEY.getBytes(),
                orderCode.getBytes(),
                finalNotifyResult.getBytes(),
                currentTimeMillis.toString().getBytes());
        });

        if (result != null) {
            switch (result.intValue()) {
                case 1:
                    log.info("Updated VIP right open failure for order: {}, new notifyResult: {}", orderCode, notifyResult);
                    break;
                case 2:
                    log.info("Inserted new VIP right open failure for order: {}, notifyResult: {}", orderCode, notifyResult);
                    break;
                case 3:
                    log.info("Removed and inserted new VIP right open failure for order: {}, notifyResult: {}", orderCode, notifyResult);
                    break;
                default:
                    log.info("No operation needed for order: {}, notifyResult: {}", orderCode, notifyResult);
                    break;
            }
        }
    }


    /**
     * 检查并记录已恢复的订单
     *
     * @param orderCode 订单编号
     */
    private void checkAndRecordRecoveredOrder(String orderCode) {
        Long currentTimeMillis = System.currentTimeMillis();

        String script = "local existsInFailed = redis.call('sismember', KEYS[2], ARGV[1]); " +
            "if existsInFailed == 1 then " +
            "    redis.call('srem', KEYS[2], ARGV[1]); " +
            "    local existsInRecovered = redis.call('sismember', KEYS[3], ARGV[1]); " +
            "    if existsInRecovered == 0 then " +
            "        redis.call('rpush', KEYS[1], ARGV[1]); " +
            "        redis.call('sadd', KEYS[3], ARGV[1]); " +
            "        redis.call('set', KEYS[4], ARGV[2]); " +
            "    end " +
            "    return 1; " +
            "else " +
            "    return 0; " +
            "end";

        Long result = stringRedisTemplate.execute((RedisCallback<Long>) connection -> {
            byte[] scriptBytes = script.getBytes(StandardCharsets.UTF_8);
            return connection.eval(scriptBytes, ReturnType.INTEGER, 4,
                RIGHT_RECOVERED_ORDERS_REDIS_KEY.getBytes(),
                RIGHT_FAILED_ORDERS_SET_REDIS_KEY.getBytes(),
                RIGHT_RECOVERED_ORDERS_SET_REDIS_KEY.getBytes(),
                RIGHT_RECOVERED_TIMESTAMP_KEY.getBytes(),
                orderCode.getBytes(),
                currentTimeMillis.toString().getBytes());
        });

        if (Long.valueOf(1).equals(result)) {
            log.info("Recorded VIP right recovered for order: {}", orderCode);
        }
    }

    private OrderInfo getOrderInfo(String orderCode) {
        Order detailedOrder = orderRepository.findByOrderCode(orderCode);
        return new OrderInfo(detailedOrder);
    }

    public void monitorOpenVipRightTooSlow(String orderCode) {
        OrderInfo order = getOrderInfo(orderCode);
        if ("A00000".equals(order.getNotifyResult()) || "A00001".equals(order.getNotifyResult())) {
            return;
        }
        if (order.getStatus() == OrderStatusEnum.ORDER_STATUS_CANCLE.getStatus()) {
            return;
        }
        countOpenVipRightTooSlow(order.getOrderCode(), order.getNotifyResult());
    }

    private void countOpenVipRightTooSlow(String orderCode, String notifyResult) {
        Tag notifyTag;
        if (StringUtils.isNotBlank(notifyResult)) {
            return;
        }
        notifyTag = new ImmutableTag("notifyResult", "");
        List<Tag> orderTotal = Lists.newArrayList();
        orderTotal.add(notifyTag);
        Metrics.counter("huiyuan_order_open_vip_right_too_slow", orderTotal).increment();
        log.info("Open vip right too slow. orderCode:{}", orderCode);
    }
}
