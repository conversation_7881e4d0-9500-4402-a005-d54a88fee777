package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.core.service.ZeusAlertNotificationService;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertLevel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertNoticeType;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertReceiver;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertService;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: guojing
 * @date: 2023/12/20 11:38
 */
@Profile("!sg")
@Slf4j
@RestController
@RequestMapping("/zeus/alertNotification")
@Api(tags = "告警通知相关接口")
public class AlertNotificationController {

    @Resource
    private ZeusAlertNotificationService alertNotificationService;

    /**
     * 告警接收人列表
     */
    @ApiOperation(value = "告警接收人列表")
    @GetMapping(value = "/alertReceivers")
    public BaseResponse<List<AlertReceiver>> alertReceivers() {
        return BaseResponse.createSuccessList(alertNotificationService.alertReceivers());
    }

    /**
     * 告警级别列表
     */
    @ApiOperation(value = "告警级别列表")
    @GetMapping(value = "/alertLevels")
    public BaseResponse<List<AlertLevel>> alertLevels() {
        return BaseResponse.createSuccessList(alertNotificationService.alertLevels());
    }

    /**
     * 告警通知类型列表
     */
    @ApiOperation(value = "告警通知类型列表")
    @GetMapping(value = "/alertNoticeTypes")
    public BaseResponse<List<AlertNoticeType>> alertNoticeTypes() {
        return BaseResponse.createSuccessList(alertNotificationService.alertNoticeTypes());
    }

    /**
     * 告警服务列表
     */
    @ApiOperation(value = "告警服务列表")
    @GetMapping(value = "/alertServices")
    public BaseResponse<List<AlertService>> alertServices() {
        return BaseResponse.createSuccessList(alertNotificationService.alertServices());
    }

}
