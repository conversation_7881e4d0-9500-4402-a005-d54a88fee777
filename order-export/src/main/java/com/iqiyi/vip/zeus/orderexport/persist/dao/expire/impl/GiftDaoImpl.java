package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Gift;
import com.iqiyi.vip.zeus.orderexport.entity.PayChannelMarketing;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.GiftDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.PayChannelMarketingDao;

/**
 * <AUTHOR>
 * @date 2024/4/10 9:15
 */
@Repository
@Profile("!sg")
public class GiftDaoImpl implements GiftDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<Gift> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(GiftDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
