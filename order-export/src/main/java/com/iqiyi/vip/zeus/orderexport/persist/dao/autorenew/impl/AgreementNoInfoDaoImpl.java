package com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.entity.AgreementNoInfo;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AgreementNoInfoDao;

/**
 * @author: guojing
 * @date: 2024/12/2 14:24
 */
@Repository
public class AgreementNoInfoDaoImpl implements AgreementNoInfoDao {

    @Resource
    SqlSessionTemplate autorenewSqlSessionTemplate;

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getById", cacheType= CacheType.LOCAL)
    @Override
    public AgreementNoInfo getById(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }

        return autorenewSqlSessionTemplate.getMapper(AgreementNoInfoDao.class).getById(agreementNo);
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "AgreementNoInfo_getVipTypeById", cacheType= CacheType.LOCAL)
    @Override
    public Integer getVipTypeById(Integer agreementNo) {
        return autorenewSqlSessionTemplate.getMapper(AgreementNoInfoDao.class).getVipTypeById(agreementNo);
    }
}
