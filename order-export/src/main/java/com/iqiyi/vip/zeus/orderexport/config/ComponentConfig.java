package com.iqiyi.vip.zeus.orderexport.config;

import com.iqiyi.kit.http.client.spring.ApacheRestTemplateFactoryBean;
import com.iqiyi.trade.order.service.client.OrderRepositoryClient;
import com.qiyi.vip.commons.component.VipInfoBatchQueryCloudApi;
import com.qiyi.vip.service.AgreementClient;
import com.qiyi.vip.service.PaymentTypeClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.client.RestTemplate;

/**
 * @author: guojing
 * @date: 2023/11/15 13:45
 */
@Slf4j
@Configuration
public class ComponentConfig {

    @Value("${pay.info.sign.key}")
    private String payInfoSignKey;

    @Value("${pay.info.cloud.url}")
    private String payInfoCloudUrl;
    @Value("${pay.info.qsm.url}")
    private String payInfoQsmUrl;

    @Value("${order.system.signKey}")
    private String signKey;


    @Bean(name = "paymentTypeCloudClient")
    public PaymentTypeClient paymentTypeCloudClient() {
        PaymentTypeClient paymentTypeClient = new PaymentTypeClient();
        paymentTypeClient.setChannel("xuanwu");
        paymentTypeClient.setEnableSign(true);
        paymentTypeClient.setSignKey(payInfoSignKey);
        paymentTypeClient.setRootUrl(payInfoCloudUrl);
        paymentTypeClient.setRestTemplate(lbRestTemplate());
        return paymentTypeClient;
    }

    @Value("${batch.query.vip.info.url:http://vip-info-server-online/internal/batch/vip_users}")
    private String cloudBatchQueryVipUserUrl;

    @Value("${batch.query.vip.info.timeout:500}")
    private int vipInfoBatchQueryTimeout;

    @Bean(name = "paymentTypeClient")
    public PaymentTypeClient paymentTypeClient() {
        PaymentTypeClient paymentTypeClient = new PaymentTypeClient();
        paymentTypeClient.setChannel("xuanwu");
        paymentTypeClient.setEnableSign(true);
        paymentTypeClient.setSignKey(payInfoSignKey);
        paymentTypeClient.setRootUrl(payInfoQsmUrl);
        paymentTypeClient.setRestTemplate(fastRestTemplate());
        return paymentTypeClient;
    }

    @Bean(name = "agreementCloudClient")
    public AgreementClient agreementCloudClient() {
        AgreementClient agreementClient = new AgreementClient();
        agreementClient.setChannel("xuanwu");
        agreementClient.setEnableSign(true);
        agreementClient.setSignKey(payInfoSignKey);
        agreementClient.setRootUrl(payInfoCloudUrl);
        agreementClient.setRestTemplate(lbRestTemplate());
        return agreementClient;
    }

    @Bean(name = "agreementClient")
    public AgreementClient agreementClient() {
        AgreementClient agreementClient = new AgreementClient();
        agreementClient.setChannel("xuanwu");
        agreementClient.setEnableSign(true);
        agreementClient.setSignKey(payInfoSignKey);
        agreementClient.setRootUrl(payInfoQsmUrl);
        agreementClient.setRestTemplate(fastRestTemplate());
        return agreementClient;
    }

    @LoadBalanced
    @Bean(name = "lbRestTemplate")
    public RestTemplate lbRestTemplate() {
        ApacheRestTemplateFactoryBean apacheRestTemplateFactoryBean = new ApacheRestTemplateFactoryBean();
        apacheRestTemplateFactoryBean.setEnableMonitor(true);
        apacheRestTemplateFactoryBean.setConnectTimeoutInMillis(1000);
        apacheRestTemplateFactoryBean.setReadTimeoutInMillis(1000);
        apacheRestTemplateFactoryBean.setMaxPoolSize(50);
        try {
            return apacheRestTemplateFactoryBean.getObject();
        } catch (Exception e) {
            log.error("init restTemplate config error", e);
        }
        return null;
    }

    @Bean(name = "fastRestTemplate")
    public RestTemplate fastRestTemplate() {
        ApacheRestTemplateFactoryBean apacheRestTemplateFactoryBean = new ApacheRestTemplateFactoryBean();
        apacheRestTemplateFactoryBean.setEnableMonitor(true);
        apacheRestTemplateFactoryBean.setConnectTimeoutInMillis(1000);
        apacheRestTemplateFactoryBean.setReadTimeoutInMillis(1000);
        apacheRestTemplateFactoryBean.setMaxPoolSize(50);
        try {
            return apacheRestTemplateFactoryBean.getObject();
        } catch (Exception e) {
            log.error("init restTemplate config error", e);
        }
        return null;
    }

    @Bean(name = "autoRenewRestTemplate")
    @LoadBalanced
    public RestTemplate autoRenewRestTemplate() {
        ApacheRestTemplateFactoryBean apacheRestTemplateFactoryBean = new ApacheRestTemplateFactoryBean();
        apacheRestTemplateFactoryBean.setEnableMonitor(true);
        apacheRestTemplateFactoryBean.setConnectTimeoutInMillis(500);
        apacheRestTemplateFactoryBean.setReadTimeoutInMillis(3000);
        apacheRestTemplateFactoryBean.setMaxPoolSize(50);
        try {
            return apacheRestTemplateFactoryBean.getObject();
        } catch (Exception e) {
            log.error("init autoRenewRestTemplate config error", e);
        }
        return null;
    }

    @Bean
    public VipInfoBatchQueryCloudApi vipInfoBatchQueryCloudApi() {
        VipInfoBatchQueryCloudApi vipInfoBatchQueryCloudApi = new VipInfoBatchQueryCloudApi();
        vipInfoBatchQueryCloudApi.setCloudBatchQueryVipUserUrl(cloudBatchQueryVipUserUrl);
        vipInfoBatchQueryCloudApi.setCloudRestTemplate(lbRestTemplate());
        vipInfoBatchQueryCloudApi.setFallbackTimeOut(vipInfoBatchQueryTimeout);
        return vipInfoBatchQueryCloudApi;
    }

    @Lazy(false)
    @Bean
    OrderRepositoryClient orderRepositoryClient() {
        OrderRepositoryClient orderRepositoryClient = new OrderRepositoryClient();
        orderRepositoryClient.setEnableSign(true);
        orderRepositoryClient.setSignKey(signKey);
        return orderRepositoryClient;
    }


}
