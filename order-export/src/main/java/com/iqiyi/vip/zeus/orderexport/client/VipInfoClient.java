package com.iqiyi.vip.zeus.orderexport.client;

import com.qiyi.vip.commons.component.VipInfoBatchQueryCloudApi;
import com.qiyi.vip.commons.component.dto.BatchQueryVipInfoReq;
import com.qiyi.vip.commons.component.dto.VipInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: guojing
 * @date: 2025/5/29 14:36
 */
@Slf4j
@Component
public class VipInfoClient {

    public static final String PLATFORM_DEFAULT_CODE = "b6c13e26323c537d";

    private static final String VIP_INFOS = "/internal/users/vip/vip_infos";

    @Resource
    private VipInfoBatchQueryCloudApi vipInfoBatchQueryCloudApi;

    public Map<Integer, VipInfo> getUserMultiVipInfo(Long userId, List<Long> vipTypes) {
        BatchQueryVipInfoReq.BatchQueryVipInfoReqBuilder paramBuilder = BatchQueryVipInfoReq.builder()
            .uid(userId)
            .platform(PLATFORM_DEFAULT_CODE)
            .vipTypeList(vipTypes);
        List<VipInfo> vipInfos = vipInfoBatchQueryCloudApi.queryMultiVipInfoFromBossWithCircuitBreaker(paramBuilder.build());
        if (CollectionUtils.isEmpty(vipInfos)) {
            return Collections.emptyMap();
        }
        return vipInfos.stream()
            .collect(Collectors.toMap(VipInfo::getVipType, vipInfo -> vipInfo));
    }

}
