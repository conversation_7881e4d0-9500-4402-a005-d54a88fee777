package com.iqiyi.vip.zeus.orderexport.util;

import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.solar.config.client.CloudConfig;
import com.iqiyi.vip.zeus.orderexport.model.ExpiringDataReq;
import com.iqiyi.vip.zeus.orderexport.model.ExpiringRuleAndPackageReq;

/**
 * <AUTHOR>
 * @date 2021/8/17 17:43
 */
@Lazy(false)
@Component
public class CloudConfigUtil implements ApplicationContextAware, InitializingBean {

    private static ApplicationContext context;
    private static CloudConfig cloudConfig;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        cloudConfig = context.getBean("cloudConfig", CloudConfig.class);
    }

    /**
     * dutType到期前几天提醒
     */
    public static List<Integer> remindDaysBeforeDutTypeExpire() {
        String daysStr = cloudConfig.getProperty("remind.days.before.dutType.expire", "7,15,30,90,180");
        return Arrays.stream(daysStr.split(",")).map(Integer::valueOf).collect(Collectors.toList());
    }

    /**
     * 需要监控即将过期数据的配置
     */
    public static List<ExpiringDataReq> getExpiringDataReqList() {
        String ExpiringDataReqListStr = cloudConfig.getProperty("expiring_data_req_list", "[{\"module\": \"安卓套餐\",\"database\": \"store\",\"tableName\": \"qiyue_mobile_product_timelength\"}]");
        return JSON.parseArray(ExpiringDataReqListStr, ExpiringDataReq.class);
    }

    public static List<ExpiringRuleAndPackageReq> getExpiringRuleAndPackageReqList() {
        String ExpiringRuleAndPolicyReqListStr = cloudConfig.getProperty("expiringRuleAndPackageReqList", null);
        return JSON.parseArray(ExpiringRuleAndPolicyReqListStr, ExpiringRuleAndPackageReq.class);
    }

    /**
     * 是否需要将即将过期数据告警发送到热聊
     */
    public static boolean sendExpiringDataToHotChat() {
        return cloudConfig.getBooleanProperty("send_expiring_data_to_hot_chat", false);
    }

    /**
     * 是否有权限操作指标模版
     * @param oaAccount
     */
    public static boolean canNoOperateMetricTemplate(String oaAccount) {
        String oaAccountListStr = cloudConfig.getProperty("can.create.or.update.metric.template", "zhouguojing,guoyang04,felixsun");
        List<String> oaAccountList = Arrays.stream(oaAccountListStr.split(",")).collect(Collectors.toList());
        return !oaAccountList.contains(oaAccount);
    }

    /**
     * 获取一笔订单权益未开通的监控次数
     * @return
     */
    public static Integer getRightMonitorRetryCount() {
        return cloudConfig.getIntProperty("rightMonitorRetryCount", 60);
    }

    /**
     * 获取一笔订单权益未开通的通知次数
     * @return
     */
    public static Integer getRightMonitorNotifyCount() {
        return cloudConfig.getIntProperty("rightMonitorNotifyCount", 1);
    }

    /**
     * 清空redis缓存的等待时间-权益开通失败及恢复的订单信息
     * @return
     */
    public static Integer clearVipRightNotifyWaitMinute() {
        return cloudConfig.getIntProperty("clearVipRightNotifyWaitMinute", 10);
    }

}
