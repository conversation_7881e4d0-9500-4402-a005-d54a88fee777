package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.PrometheusMetricType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.core.req.MetricTemplateSearchParam;
import com.iqiyi.vip.zeus.core.service.MetricTemplateService;
import com.iqiyi.vip.zeus.core.service.ZeusMonitorService;
import com.iqiyi.vip.zeus.core.utils.SQLParseUtils;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.param.MetricTemplateCreateOrUpdateParam;
import com.iqiyi.vip.zeus.orderexport.util.CloudConfigUtil;

/**
 * @author: guojing
 * @date: 2024/1/22 14:33
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/metricTemplate")
@Api(tags = "指标模版相关接口")
public class MetricTemplateController {

    @Resource
    private MetricTemplateService metricTemplateService;
    @Resource
    private ZeusMonitorService zeusMonitorService;

    /**
     * 创建指标模版
     */
    @ApiOperation(value = "创建指标模版")
    @PostMapping(value = "/create")
    public BaseResponse<Integer> create(@Validated @RequestBody MetricTemplateCreateOrUpdateParam createParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        if (CloudConfigUtil.canNoOperateMetricTemplate(currentUser.getOaAccount())) {
            throw BizException.newParamException("您无权创建指标模版");
        }
        if (createParam.getId() != null) {
            throw BizException.newParamException("无需指定指标模版id");
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(createParam.getDatasourceType());
        if (dataSourceType == null) {
            throw BizException.newParamException("数据源类型不支持");
        }
        if (dataSourceType == DataSourceType.MySQL && !SQLParseUtils.isSelectSQL(createParam.getContent())) {
            throw BizException.newParamException("仅支持SELECT语句");
        }
        if (dataSourceType == DataSourceType.Prometheus) {
            if (StringUtils.isBlank(createParam.getMetricType())) {
                createParam.setMetricType(PrometheusMetricType.Counter.getValue());
            } else {
                PrometheusMetricType prometheusMetricType = PrometheusMetricType.findByValue(createParam.getMetricType());
                if (prometheusMetricType == null) {
                    throw BizException.newParamException("metricType不能为空或取值不合法");
                }
            }
        }
        MetricTemplate sameNameMetricTemplate = metricTemplateService.getByName(createParam.getName());
        if (sameNameMetricTemplate != null) {
            throw BizException.newParamException("指标模版名称已存在，请更换");
        }
        MetricTemplate createParamData = createParam.toMetricTemplate();
        if (createParamData.getType() == null) {
            throw BizException.newParamException("指标模版内容不合法，无法匹配到模版类型");
        }

        createParamData.setCreateUser(currentUser.getOaAccount());
        createParamData.setUpdateUser(currentUser.getOaAccount());
        Integer metricTmpId = metricTemplateService.create(createParamData);
        return BaseResponse.createSuccess(metricTmpId);
    }

    /**
     * 更新指标模版
     */
    @ApiOperation(value = "更新指标模版")
    @PostMapping(value = "/update")
    public BaseResponse<Boolean> update(@Validated @RequestBody MetricTemplateCreateOrUpdateParam updateParam) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        if (CloudConfigUtil.canNoOperateMetricTemplate(currentUser.getOaAccount())) {
            throw BizException.newParamException("您无权更新指标模版");
        }
        if (updateParam.getId() == null) {
            throw BizException.newParamException("指标模版id不能为空");
        }
        MetricTemplate metricTemplateFromDB = metricTemplateService.getById(updateParam.getId());
        if (!metricTemplateFromDB.getDatasourceType().equals(updateParam.getDatasourceType())) {
            throw BizException.newParamException("数据源类型不能修改");
        }
        DataSourceType dataSourceType = DataSourceType.parseValue(updateParam.getDatasourceType());
        if (dataSourceType == DataSourceType.MySQL && !SQLParseUtils.isSelectSQL(updateParam.getContent())) {
            throw BizException.newParamException("仅支持SELECT语句");
        }
        if (!metricTemplateFromDB.getName().equals(updateParam.getName())) {
            MetricTemplate sameNameMetricTemplate = metricTemplateService.getByName(updateParam.getName());
            if (sameNameMetricTemplate != null) {
                throw BizException.newParamException("指标模版名称已存在，请更换");
            }
        }
        MetricTemplate updateParamData = updateParam.toMetricTemplate();
        if (updateParamData.getType() == null) {
            throw BizException.newParamException("指标模版内容不合法，无法匹配到模版类型");
        }
        updateParamData.setUpdateUser(currentUser.getOaAccount());
        boolean updated = metricTemplateService.update(updateParamData);
        return BaseResponse.createSuccess(updated);
    }

    /**
     * 删除指标模版
     */
    @ApiOperation(value = "删除指标模版")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "指标模版ID", dataType = "Integer", required = true, paramType = "query")})
    @PostMapping(value = "/delete")
    public BaseResponse<Boolean> delete(@NotNull(message = "指标模版ID不能为空") Integer id) {
        AuthorityUser currentUser = RequestContextHolder.getCurrentUser();
        if (CloudConfigUtil.canNoOperateMetricTemplate(currentUser.getOaAccount())) {
            throw BizException.newParamException("您无权删除指标模版");
        }
        return BaseResponse.createSuccess(metricTemplateService.delete(id));
    }

    @ApiOperation(value = "根据id查询监控详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "指标模版ID", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getDetailById")
    public BaseResponse<MetricTemplate> getById(@NotNull(message = "指标模版ID不能为空") Integer id) {
        return BaseResponse.createSuccess(metricTemplateService.getById(id));
    }

    @ApiOperation(value = "搜索指标模版列表")
    @GetMapping(value = "/search")
    public BaseResponse<List<MetricTemplate>> search(@Validated MetricTemplateSearchParam searchParam) {
        List<MetricTemplate> allResult = metricTemplateService.listAll();
        if (CollectionUtils.isEmpty(allResult)) {
            return BaseResponse.createSuccess(Collections.emptyList());
        }
        List<MetricTemplate> filteredResult = allResult.stream().filter(metricTemplate -> {
            boolean datasourceTypeMatch =
                StringUtils.isBlank(searchParam.getDatasourceType()) || metricTemplate.getDatasourceType()
                    .equals(searchParam.getDatasourceType());
            boolean nameMatch = StringUtils.isBlank(searchParam.getNameLike()) || metricTemplate.getName().contains(searchParam.getNameLike());
            return datasourceTypeMatch && nameMatch;
        }).collect(Collectors.toList());
        return BaseResponse.createSuccessList(filteredResult);
    }

}
