package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.VipStoreConfig;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.VipStoreConfigDao;

/**
 * <AUTHOR>
 * @date 2024/4/8 20:39
 */
@Repository
@Profile("!sg")
public class VipStoreConfigDaoImpl implements VipStoreConfigDao {
    @Resource
    private SqlSessionTemplate storeSqlSessionTemplate;

    @Override
    public List<VipStoreConfig> queryExpiringData(String rangeLeft, String rangeRight) {
        return storeSqlSessionTemplate.getMapper(VipStoreConfigDao.class).queryExpiringData(rangeLeft, rangeRight);
    }
}
