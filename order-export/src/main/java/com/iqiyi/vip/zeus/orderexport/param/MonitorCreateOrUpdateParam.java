package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;

/**
 * @author: guojing
 * @date: 2023/12/20 11:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("创建或更新监控参数模型")
public class MonitorCreateOrUpdateParam {

    /**
     * 宙斯监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer id;
    /**
     * 监控名称
     */
    @ApiModelProperty(value = "监控名称")
    @NotBlank(message = "监控名称不能为空")
    private String name;
    /**
     * 监控种类
     * @see com.iqiyi.vip.zeus.core.enums.MonitorCategory
     */
    @ApiModelProperty(value = "监控种类")
    @NotNull(message = "监控种类不能为空")
    private Integer category;
    /**
     * 宙斯数据源id
     */
    @ApiModelProperty(value = "监控数据源id")
    @NotNull(message = "宙斯数据源ID不能为空")
    private Integer datasourceId;
    /**
     * 监控查询配置信息
     */
    @Valid
    @ApiModelProperty(value = "监控查询配置信息")
    @NotEmpty(message = "监控查询配置信息不能为空")
    private List<ZeusMonitorQuery> query;
    /**
     * 鹰眼Dashboard uid
     */
    @ApiModelProperty(value = "监控DashboardUid")
    private String dashboardUid;
    /**
     * 鹰眼监控面板id，dashboard下唯一
     */
    @ApiModelProperty(value = "监控PanelID")
    private Integer panelId;
    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
    private Map<String, Object> extraData;
    /**
     * 告警规则
     */
    @ApiModelProperty(value = "告警规则")
    private AlertRuleCreateOrUpdateParam alertRuleParam;

    public ZeusMonitor toZeusMonitor() {
        return ZeusMonitor.builder()
            .id(id)
            .name(name)
            .category(category)
            .datasourceId(datasourceId)
            .query(query)
            .dashboardUid(dashboardUid)
            .panelId(panelId)
            .extraData(extraData)
            .build();
    }

}
