package com.iqiyi.vip.zeus.orderexport.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderPresentInfo {

    private Long userId;
    /**
     * 订单完成消息id
     */
    private String msgId;
    /**
     * 原订单号
     */
    private String orderCode;
    /**
     * 赠送订单号
     */
    private String presentOrderCode;
    /**
     * 退款订单号
     */
    private String refundOrderCode;
    /**
     * 购买的会员类型
     */
    private String buyVipType;
    /**
     * 赠送的会员类型
     */
    private String presentVipType;
    /**
     * 订单支付时间
     */
    private Timestamp payTime;
    /**
     * 赠送状态
     */
    private Integer presentStatus;

}
