package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.component.GuardItemDataQuery;
import com.iqiyi.vip.zeus.core.component.GuardItemDataQueryFactory;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardMonitor;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.service.OrderGuardMonitorService;
import com.iqiyi.vip.zeus.core.service.guard.GuardDatasourceService;
import com.iqiyi.vip.zeus.orderexport.entity.OrderGuardianRowData;
import com.iqiyi.vip.zeus.orderexport.param.OrderGuardianDataQueryParam;

@Validated
@RequestMapping("/zeus/orderGuardian")
@RestController
@Api(tags = "订单稽核相关接口")
public class OrderGuardianController {

    @Value("${bailing.order.detail:http://bailing.qsm.qiyi.middle/order/query?env=prod}")
    private String orderDetailUrl;
    @Value("${bailing.user.order.detail:http://bailing.qsm.qiyi.middle/order/query?env=prod&excludePresent=true&isVip=true&isValid=true&pageSize=100&page=1&isPageTurn=true}")
    private String userOrderDetailUrl;

    @Resource
    private OrderGuardMonitorService orderGuardMonitorService;
    @Resource
    private GuardDatasourceService guardDatasourceService;
    @Resource
    private GuardItemDataQueryFactory guardItemDataQueryFactory;

    @ApiOperation(value = "查询监控项指定日期的详细数据信息")
    @GetMapping("/queryDetailData")
    public ModelAndView queryDetailData(@Validated OrderGuardianDataQueryParam param) {
        OrderGuardMonitor orderGuardMonitor = orderGuardMonitorService.getById(param.getMonitorId());
        if (orderGuardMonitor == null) {
            throw BizException.newParamException("未查询到监控信息");
        }
        GuardDatasource datasource = guardDatasourceService.getById(orderGuardMonitor.getDatasourceId());
        if (datasource == null) {
            throw BizException.newParamException("未查询到数据源信息");
        }
        GuardDatasourceType datasourceType = GuardDatasourceType.parseValue(datasource.getType());
        if (datasourceType == null) {
            throw BizException.newParamException("未知数据源类型");
        }
        GuardItemDataQuery guardItemDataQuery = guardItemDataQueryFactory.getQueryFactory(datasourceType.getQueryType());
        if (guardItemDataQuery == null) {
            throw BizException.newSystemException("未找到数据源类型对应的查询实现类");
        }
        OrderGuardianDetailData detailData = guardItemDataQuery.queryDetailData(datasource, orderGuardMonitor.getDetailSql(), param.getQueryDay());
        if (detailData == null) {
            throw BizException.newSystemException("未查询到明细数据");
        }

        int displayMaxRowsPerGroup = param.getDisplayMaxRowsPerGroup() != null ? param.getDisplayMaxRowsPerGroup() : 50;
        List<LinkedHashMap<String, Object>> queryResult = detailData.getQueryResult();
        List<String> columnNames = CollectionUtils.isNotEmpty(queryResult) ? new ArrayList<>(queryResult.get(0).keySet()) : new ArrayList<>();
        if (CollectionUtils.isEmpty(columnNames)) {
            ModelAndView modelAndView = new ModelAndView("order-guardian-detail-data");
            modelAndView.addObject("monitorName", orderGuardMonitor.getName());
            modelAndView.addObject("querySql", orderGuardMonitor.getQuerySql());
            modelAndView.addObject("detailSql", detailData.getDetailSql());
            modelAndView.addObject("columnNames", Collections.emptyList());
            modelAndView.addObject("rowDataList", Collections.emptyList());
            return modelAndView;
        }

        String firstColumnName = columnNames.get(0);
        boolean firstColumnIsPassportId = Objects.equals(firstColumnName, "passport_id");
        // 新增逻辑：将queryResult按照passport_id聚合，并存储除passport_id外的其他字段，然后按照value列表的元素个数倒序排列
        Map<String, List<LinkedHashMap<String, Object>>> passportIdToOrdersMap = queryResult.stream()
            .collect(Collectors.groupingBy(rowData -> MapUtils.getString(rowData, firstColumnName),
                LinkedHashMap::new,
                Collectors.mapping(rowData -> rowData, Collectors.toList())))
            .entrySet().stream()
            .sorted(Comparator.comparingInt((Map.Entry<String, List<LinkedHashMap<String, Object>>> entry) -> entry.getValue().size()).reversed())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        List<OrderGuardianRowData> rowDataList = passportIdToOrdersMap.entrySet().stream()
            .map(entry -> {
                String firstColumnValue = entry.getKey();
                List<LinkedHashMap<String, Object>> entryValue = entry.getValue();
                String firstColumnText = getFirstColumnText(firstColumnIsPassportId, firstColumnValue, entryValue, displayMaxRowsPerGroup);
                List<List<String>> orderInfoList = new ArrayList<>();
                for (LinkedHashMap<String, Object> otherFields : entryValue) {
                    if (orderInfoList.size() >= displayMaxRowsPerGroup) {
                        break;
                    }
                    List<String> otherFieldValues = new ArrayList<>();
                    for (Entry<String, Object> fieldEntry : otherFields.entrySet()) {
                        String columnKey = fieldEntry.getKey();
                        if (Objects.equals(columnKey, firstColumnName)) {
                            continue;
                        }
                        if (StringUtils.contains(columnKey, "order_code")) {
                            String orderCode = MapUtils.getString(otherFields, columnKey);
                            if (StringUtils.isNotBlank(orderCode)) {
                                otherFieldValues.add(String.format("<a href='%s&orderCode=%s' target='_blank'>%s</a>", orderDetailUrl, orderCode, orderCode));
                            } else {
                                otherFieldValues.add("");
                            }
                            continue;
                        }
                        otherFieldValues.add(MapUtils.getString(otherFields, columnKey));
                    }
                    orderInfoList.add(otherFieldValues);
                }
                return new OrderGuardianRowData(firstColumnText, orderInfoList);
            })
            .collect(Collectors.toList());

        ModelAndView modelAndView = new ModelAndView("order-guardian-detail-data");
        modelAndView.addObject("monitorName", orderGuardMonitor.getName());
        modelAndView.addObject("querySql", orderGuardMonitor.getQuerySql());
        modelAndView.addObject("detailSql", detailData.getDetailSql());
        modelAndView.addObject("columnNames", columnNames);
        modelAndView.addObject("rowDataList", rowDataList);
        return modelAndView;
    }

    private String getFirstColumnText(boolean firstColumnIsPassportId, String firstColumnValue, List<LinkedHashMap<String, Object>> entryValue, int displayMaxRowsPerGroup) {
        if (!firstColumnIsPassportId) {
            return firstColumnValue;
        }
        int totalCount = entryValue.size();
        String link = String.format("<a href='%s&userId=%s' target='_blank'>%s(%d)</a>", userOrderDetailUrl, firstColumnValue, firstColumnValue, totalCount);
        return totalCount > displayMaxRowsPerGroup ? String.format("%s<br>仅展示%d条点击Uid查看全部", link, displayMaxRowsPerGroup) : link;
    }
}