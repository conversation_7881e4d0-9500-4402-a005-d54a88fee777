package com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.AutoRenewDutType;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;

/**
 * <AUTHOR>
 * @date 2021/8/17 17:10
 */
@Repository
public class AutoRenewDutTypeDaoImpl implements AutoRenewDutTypeDao {

    @Resource
    SqlSessionTemplate autorenewSqlSessionTemplate;

    @Override
    public List<AutoRenewDutType> selectValidEndTimeNotNullRecords() {
        return autorenewSqlSessionTemplate.getMapper(AutoRenewDutTypeDao.class).selectValidEndTimeNotNullRecords();
    }

    @Cacheable(value = "AutorenewDutType", key = "'getPayChannelByDutType_' + #dutType")
    @Override
    public Integer getPayChannelByDutType(Integer dutType) {
        if (dutType == null) {
            return null;
        }
        return autorenewSqlSessionTemplate.getMapper(AutoRenewDutTypeDao.class).getPayChannelByDutType(dutType);
    }

    @Cacheable(value = "AutorenewDutType", key = "'getVipTypeByDutType_' + #dutType")
    @Override
    public Integer getVipTypeByDutType(Integer dutType) {
        if (dutType == null) {
            return null;
        }
        return autorenewSqlSessionTemplate.getMapper(AutoRenewDutTypeDao.class).getVipTypeByDutType(dutType);
    }
}
