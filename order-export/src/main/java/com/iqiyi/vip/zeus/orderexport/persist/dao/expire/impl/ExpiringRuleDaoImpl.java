package com.iqiyi.vip.zeus.orderexport.persist.dao.expire.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.ExpiringRule;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.ExpiringRuleDao;

/**
 * <AUTHOR>
 * @date 2023/5/3 20:50
 */
@Repository
@Profile("!sg")
public class ExpiringRuleDaoImpl implements ExpiringRuleDao {

    @Resource
    private SqlSessionTemplate orderSqlSessionTemplate;

    @Override
    public List<ExpiringRule> queryExpiringRule(String rangeLeft, String rangeRight) {
        return orderSqlSessionTemplate.getMapper(ExpiringRuleDao.class).queryExpiringRule(rangeLeft, rangeRight);
    }

    @Override
    public List<ExpiringRule> querySpecialExpiringRule(String rangeLeft, String rangeRight) {
        return orderSqlSessionTemplate.getMapper(ExpiringRuleDao.class).querySpecialExpiringRule(rangeLeft, rangeRight);
    }
}
