package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.enums.MonitorConditionOperator;
import com.iqiyi.vip.zeus.core.exception.BizException;
import com.iqiyi.vip.zeus.core.model.ColumnInfo;
import com.iqiyi.vip.zeus.core.model.DatasourceTableSchema;
import com.iqiyi.vip.zeus.core.model.KeyValuePair;
import com.iqiyi.vip.zeus.core.model.OperatorInfo;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.req.PrometheusMetricLabelValueQueryParam;
import com.iqiyi.vip.zeus.core.service.DatasourceSchemaService;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: guojing
 * @date: 2024/1/29 11:20
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/monitor/config/metadata")
@Api(tags = "宙斯监控配置元数据接口")
public class MonitorConfigMetadataController {

    @Resource
    private DatasourceSchemaService datasourceSchemaService;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;

    /**
     * 监控条件操作符枚举列表
     */
    @ApiOperation(value = "监控条件操作符枚举列表，按数据源类型分组")
    @GetMapping(value = "/conditionOperatorList")
    public BaseResponse<List<KeyValuePair<String, List<OperatorInfo>>>> conditionOperatorList() {
        List<KeyValuePair<String, List<OperatorInfo>>> result = new ArrayList<>();
        for (DataSourceType type : DataSourceType.values()) {
            List<OperatorInfo> collect = MonitorConditionOperator.supportOperatorOf(type).stream()
                .map(operator -> new OperatorInfo(operator.getValue(), operator.getDesc(), operator.getValueCount()))
                .collect(Collectors.toList());
            result.add(new KeyValuePair<>(type.name(), collect));
        }
        return BaseResponse.createSuccessList(result);
    }

    /**
     * 获取数据源表和字段schema信息
     * @param datasourceId
     */
    @ApiOperation(value = "获取数据源表和字段schema信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "datasourceId", value = "数据源id", dataType = "Integer", required = true, paramType = "query")})
    @GetMapping(value = "/getTableSchemaByDatasourceId")
    public BaseResponse<List<DatasourceTableSchema>> getTableSchemaByDatasourceId(@NotNull(message = "数据源ID不能为空") Integer datasourceId) {
        AuthorityTeamBasic realTeam = RequestContextHolder.getCurrentUserRealTeam();
        return BaseResponse.createSuccessList(datasourceSchemaService.getByDatasourceId(realTeam.getTeamCode(), datasourceId));
    }

    /**
     * 获取指定数据源和表下的列信息
     */
    @ApiOperation(value = "获取指定数据源和表下的列信息")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "datasourceId", value = "数据源id", dataType = "Integer", required = true, paramType = "query"),
        @ApiImplicitParam(name = "table", value = "表", dataType = "String", required = true, paramType = "query")
    })
    @GetMapping(value = "/getColumnByDatasourceIdAndTable")
    public BaseResponse<List<ColumnInfo>> getConditionByDatasourceIdAndTable(
        @NotNull(message = "数据源ID不能为空") Integer datasourceId,
        @NotBlank(message = "table参数不能为空") String table
    ) {
        AuthorityTeamBasic realTeam = RequestContextHolder.getCurrentUserRealTeam();
        return BaseResponse.createSuccessList(datasourceSchemaService.getByDatasourceIdAndTable(realTeam.getTeamCode(), datasourceId, table));
    }

    /**
     * 获取prometheus Metric下指定label的取值列表
     */
    @ApiOperation(value = "获取prometheus Metric下指定label的取值列表")
    @GetMapping(value = "/prometheusLabelValues")
    public BaseResponse<List<String>> prometheusLabelValues(@Validated PrometheusMetricLabelValueQueryParam param) {
        ZeusDatasource zeusDatasource = zeusDatasourceService.getById(param.getDatasourceId());
        if (zeusDatasource == null || zeusDatasource.invalid()) {
            throw BizException.newParamException("数据源不存在");
        }
        if (!DataSourceType.Prometheus.getValue().equals(zeusDatasource.getType())) {
            return BaseResponse.createSuccessList(Collections.emptyList());
        }
        AuthorityTeamBasic realTeam = RequestContextHolder.getCurrentUserRealTeam();
        List<String> labelValues = datasourceSchemaService.getPrometheusLabelValues(zeusDatasource.getEagleDatasourceUid(), realTeam.getTeamCode(), param.getMetric(), param.getLabel());
        return BaseResponse.createSuccessList(labelValues);
    }


}
