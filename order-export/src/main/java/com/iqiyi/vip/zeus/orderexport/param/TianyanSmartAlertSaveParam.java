package com.iqiyi.vip.zeus.orderexport.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

import com.iqiyi.vip.zeus.core.model.SmartAlertRule;

/**
 * @author: guojing
 * @date: 2025/3/7 00:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("天眼智能告警创建或更新参数模型")
public class TianyanSmartAlertSaveParam {

    /**
     * 智能告警规则id
     */
    @ApiModelProperty(value = "智能告警规则id")
    private Integer id;
    /**
     * 监控id
     */
    @ApiModelProperty(value = "监控id")
    private Integer monitorId;
    /**
     * 只能告警规则名称
     */
    @ApiModelProperty(value = "智能告警规则名称")
    @NotBlank(message = "智能告警规则名称不能为空")
    private String ruleName;
    /**
     * 持续时间, 默认2m
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     * @see com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit
     */
    @ApiModelProperty(value = "持续时间,默认2m,格式：\"(number)(unit)\", 比如：\"1m\", 或者：\"0\". 合法的单位: sm, h")
    @NotBlank(message = "持续时间不能为空")
    private String duration;
    /**
     * 检测频率, 默认30s
     * 格式："(number)(unit)", 比如："1m", 或者："0". 合法的单位: s, m, h, d, w
     * @see com.iqiyi.vip.zeus.core.enums.OffsetTimeUnit
     */
    @ApiModelProperty(value = "检测频率，默认30s,格式：\"(number)(unit)\", 比如：\"1m\", 或者：\"0\". 合法的单位: s, m, h, d, w")
    @NotBlank(message = "检测频率不能为空")
    private String checkFrequency;
    /**
     * 告警接收人，多个用逗号分隔
     */
    @ApiModelProperty(value = "告警接收人，多个用逗号分隔")
    @NotBlank(message = "告警接收人不能为空")
    private String receivers;

    public SmartAlertRule toSmartAlertRule() {
        return SmartAlertRule.builder()
            .id(id)
            .monitorId(monitorId)
            .ruleName(ruleName)
            .duration(duration)
            .checkFrequency(checkFrequency)
            .receivers(receivers)
            .build();
    }

}
