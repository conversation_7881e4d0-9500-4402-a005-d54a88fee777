package com.iqiyi.vip.zeus.orderexport.component.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.qiyi.vip.api.AgreementServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.SingleResponse;
import com.qiyi.vip.dto.data.AgreementNoInfoDTO;
import com.qiyi.vip.dto.data.AgreementTemplateDTO;
import com.qiyi.vip.dto.data.QueryAgreementNoReqDTO;
import com.qiyi.vip.service.AgreementClient;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: guojing
 * @date: 2024/11/28 14:51
 */
@Component
public class AgreementInfoService {

    @Resource
    private AgreementClient agreementCloudClient;
    @Resource
    private AgreementClient agreementClient;
    @Value("${invokeByQSM:false}")
    private boolean invokeByQSM;

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "agreement_getVipTypeByAgreementNo", cacheType = CacheType.LOCAL)
    public Long getVipTypeByAgreementNo(Integer agreementNo) {
        if (agreementNo == null) {
            return null;
        }
        SingleResponse<AgreementTemplateDTO> response = getAgreementClient().getTemplateByAgreementNo(agreementNo);
        AgreementTemplateDTO responseData = response.getData();
        return responseData != null ? responseData.getVipType() : null;
    }

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "agreement_getVipTypeByDutType", cacheType = CacheType.LOCAL)
    public Long getVipTypeByDutType(Integer dutType) {
        if (dutType == null) {
            return null;
        }
        QueryAgreementNoReqDTO param = QueryAgreementNoReqDTO.builder().dutType(dutType).build();
        MultiResponse<AgreementNoInfoDTO> response = getAgreementClient().getAgreementListByDutType(param);
        List<AgreementNoInfoDTO> responseData = response.getData();
        return CollectionUtils.isNotEmpty(responseData) ? responseData.get(0).getVipType() : null;
    }

    private AgreementServiceI getAgreementClient() {
        return invokeByQSM ? agreementClient : agreementCloudClient;
    }

}
