package com.iqiyi.vip.zeus.orderexport.job;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.zeus.core.component.DatasourceSchemaPuller;
import com.iqiyi.vip.zeus.core.component.DatasourceSchemaPullerFactory;
import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;

/**
 * @author: guojing
 * @date: 2024/2/2 13:46
 */
@Slf4j
@Profile("!sg")
@Component
public class DatasourceSchemaSyncJob extends IJobHandler {

    @Resource
    private ZeusDatasourceService zeusDatasourceService;
    @Resource
    private DatasourceSchemaPullerFactory datasourceSchemaPullerFactory;


    @Job("datasourceSchemaSyncJob")
    @Override
    public void execute() {
        Long jobId = JobHelper.getJobId();
        String jobParam = JobHelper.getJobParam();
        log.info("---------- Start execute DatasourceSchemaSyncJob, jobId:{}, jobParam:{} ----------", jobId, jobParam);
        JobHelper.log("---------- Start execute DatasourceSchemaSyncJob, jobId:{} ----------", jobId);
        StopWatch stopWatch = StopWatch.createStarted();
        List<DataSourceType> dataSourceTypes = Arrays.asList(DataSourceType.values());
        if (StringUtils.isNotBlank(jobParam)) {
            dataSourceTypes = Arrays.stream(jobParam.split(",")).map(DataSourceType::parseValue).filter(Objects::nonNull).collect(Collectors.toList());
        }
        for (DataSourceType dataSourceType : dataSourceTypes) {
            DatasourceSchemaPuller datasourceSchemaPuller = datasourceSchemaPullerFactory.getPuller(dataSourceType);
            if (datasourceSchemaPuller == null) {
                continue;
            }
            stopWatch.split();
            try {
                List<ZeusDatasource> datasourceList = zeusDatasourceService.getByType(dataSourceType);
                datasourceSchemaPuller.pull(datasourceList);
            } catch (Exception e) {
                log.error("---------- DatasourceSchemaSyncJob pull occurred exception, jobId:{}, datasourceType:{}. ----------", JobHelper.getJobId(), dataSourceType.getValue(), e);
                JobHelper.log("---------- DatasourceSchemaSyncJob pull occurred exception, jobId:{}, datasourceType:{}, errorMsg:{}. ----------", JobHelper.getJobId(), dataSourceType.getValue(), e.getMessage());
            }
            long splitTime = stopWatch.getSplitTime();
            log.info("---------- DatasourceSchemaSyncJob, jobId:{}, datasourceType:{}, cost:{}ms. ----------", JobHelper.getJobId(), dataSourceType.getValue(), splitTime);
            JobHelper.log("---------- DatasourceSchemaSyncJob finished, jobId:{}, cost:{}ms. ----------", JobHelper.getJobId(), splitTime);
        }

        log.info("---------- DatasourceSchemaSyncJob finished, jobId:{}, cost:{}ms. ----------", JobHelper.getJobId(), stopWatch.getTime());
        JobHelper.log("---------- DatasourceSchemaSyncJob finished, jobId:{}, cost:{}ms. ----------", JobHelper.getJobId(), stopWatch.getTime());
        JobHelper.handleSuccess();
    }

}
