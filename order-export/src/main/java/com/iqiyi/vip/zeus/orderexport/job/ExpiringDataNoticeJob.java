package com.iqiyi.vip.zeus.orderexport.job;

import com.google.common.collect.Lists;
import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.v.commodity.response.QuerySkuResponse;
import com.iqiyi.vip.zeus.core.enums.ExpiringDataEnum;
import com.iqiyi.vip.zeus.orderexport.client.CommodityClient;
import com.iqiyi.vip.zeus.orderexport.component.AlterService;
import com.iqiyi.vip.zeus.orderexport.entity.*;
import com.iqiyi.vip.zeus.orderexport.persist.dao.expire.*;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.PlatformDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorActivityDao;
import com.iqiyi.vip.zeus.orderexport.util.mail.HtmlStyleUtil;
import com.iqiyi.vip.zeus.orderexport.util.mail.TableMailContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/8 10:37
 */
@Slf4j
@Component
@Profile("!sg")
public class ExpiringDataNoticeJob extends IJobHandler {

    private static final String[] DEFAULT_ADDRESSEES = new String[]{"<EMAIL>"};
    @Value("${rangeLeft}")
    String rangeLeft;
    @Value("${rangeRight}")
    String rangeRight;
    @Resource
    private ExpiringRuleDao expiringRuleDao;
    @Resource
    private FavorActivityDao favorActivityDao;
    @Resource
    private CommodityClient commodityClient;
    @Resource
    private AlterService alterService;
    @Resource
    private VipStoreConfigDao vipStoreConfigDao;
    @Resource
    private PlatformDao platformDao;
    @Resource
    private BasicDao basicDao;
    @Resource
    private StoreSwitchDao storeSwitchDao;
    @Resource
    private PaymentDao paymentDao;
    @Resource
    private LevelDao levelDao;
    @Resource
    private PayInfoDao payInfoDao;
    @Resource
    private PointsDao pointsDao;
    @Resource
    private BundleDao bundleDao;
    @Resource
    private SmartStoreDao smartStoreDao;
    @Resource
    private PayChannelMarketingDao payChannelMarketingDao;
    @Resource
    private PayTypeMarketingDao payTypeMarketingDao;
    @Resource
    private GiftDao giftDao;

    @Override
    @Job("expiringDataNoticeJob")
    public void execute() {
        StopWatch stopWatch = StopWatch.createStarted();
        String param = JobHelper.getJobParam();
        Long jobId = JobHelper.getJobId();
        log.info("---------- Start execute expiringDataMonitorJob[{}] ----------", jobId);
        JobHelper.log("---------- Start execute expiringDataMonitorJob[{0}] ----------", jobId);

        commonTrigger(jobId, param);

        log.info("---------- expiringDataMonitorJob finished[{}]. cost:{}ms. ----------", jobId, stopWatch
            .getTime());
        JobHelper.log("---------- expiringDataMonitorJob finished[{0}], cost:{2}ms. ----------", jobId, stopWatch
            .getTime());
    }

    private void commonTrigger(Long jobId, String param) {
        log.info("commonTrigger start. jobId:{}, param:{}", jobId, param);

        String[] addressees = StringUtils.isNotEmpty(param) ? param.split(",") : DEFAULT_ADDRESSEES;
        List<String> addresseesList = Arrays.asList(addressees);
        String address = addresseesList.stream()
            .map(s -> s.substring(0, s.length() - 9))
            .collect(Collectors.joining(","));

        qiyueRule(addresseesList, address);
        vipStoreConfig(addresseesList, address);
        storeSwitch(addresseesList, address);
        payment(addresseesList, address);
        points(addresseesList, address);
        bundle(addresseesList, address);
        smartStore(addresseesList, address);
        payChannelMarketing(addresseesList, address);
        payTypeMarketing(addresseesList, address);
        gift(addresseesList, address);

        log.info("commonTrigger end. jobId:{}", jobId);
    }

    private void qiyueRule(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("规则id", "限购id", "规则名称", "商品", "货币类型", "定价策略", "售价", "优先级", "开始时间", "结束时间", "操作时间", "操作人", "主活动编码", "旧编码");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：交易管理-价格规则\n"
            + "到期数据：";

        List<ExpiringRule> expiringDataList = expiringRuleDao.queryExpiringRule(rangeLeft, rangeRight);
        List<List<Object>> tableContents = qiyueRuleContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.QIYUE_RULE.getValue());

        Map<String, List<ExpiringRule>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(ExpiringRule::getOperatorName));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = qiyueRuleContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.QIYUE_RULE.getValue());
        });
    }

    private void vipStoreConfig(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("id", "策略类型", "策略名称", "平台", "版本", "收银台", "展示样式", "开始时间", "结束时间", "操作时间", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-收银台路由\n"
            + "到期数据：";

        List<VipStoreConfig> expiringDataList = vipStoreConfigDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = vipStoreConfigContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.STORE_CONFIG.getValue());

        Map<String, List<VipStoreConfig>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(VipStoreConfig::getOperator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = vipStoreConfigContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.STORE_CONFIG.getValue());
        });
    }

    private void storeSwitch(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("id", "开关名称", "开关key", "收银台", "版本", "开关", "优先级", "开始时间", "结束时间", "操作时间", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-收银台开关\n"
            + "到期数据：";

        List<StoreSwitch> expiringDataList = storeSwitchDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = storeSwitchContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.STORE_SWITCH.getValue());

        Map<String, List<StoreSwitch>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(StoreSwitch::getOperatorName));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = storeSwitchContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.STORE_SWITCH.getValue());
        });
    }

    private void payment(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("id", "策略名称", "开始时间", "结束时间", "收银台", "商品", "版本", "数据来源", "活动层级", "优先级", "支付方式", "排列顺序", "默认选中", "支持纯签约", "更新时间", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-收银台支付方式\n"
            + "到期数据：";

        List<Payment> expiringDataList = paymentDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = paymentContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.PAY_TYPE.getValue());

        Map<String, List<Payment>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(Payment::getUpdator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = paymentContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.PAY_TYPE.getValue());
        });
    }

    private void points(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("活动ID", "活动名称", "活动类型", "积分类型", "开始时间", "结束时间", "优先级", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-收银台积分\n"
            + "到期数据：";

        List<Points> expiringDataList = pointsDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = pointsContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.POINTS.getValue());

        Map<String, List<Points>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(Points::getOperator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = pointsContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.POINTS.getValue());
        });
    }

    private void bundle(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("ID", "活动名称", "主商品", "子商品", "平台", "开始时间", "结束时间", "创建人", "活动code");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-收银台加价购\n"
            + "到期数据：";

        List<Bundle> expiringDataList = bundleDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = bundleContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.BUNDLE.getValue());

        Map<String, List<Bundle>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(Bundle::getOperator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = bundleContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.BUNDLE.getValue());
        });
    }

    private void smartStore(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("ID", "策略名称", "页面标题", "收银台", "开始时间", "结束时间", "优先级", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-通用/营销收银台\n"
            + "到期数据：";

        List<SmartStore> expiringDataList = smartStoreDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = smartStoreContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.SMART_STORE.getValue());

        Map<String, List<SmartStore>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(SmartStore::getOperator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = smartStoreContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.SMART_STORE.getValue());
        });
    }

    private void payChannelMarketing(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("活动ID", "活动场景", "活动类型", "活动名称", "商品", "立减金额", "优先级", "开始时间", "结束时间", "操作时间", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：交易管理-支付渠道营销\n"
            + "到期数据：";

        List<PayChannelMarketing> expiringDataList = payChannelMarketingDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = payChannelMarketingContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.PAY_CHANNEL_MARKETING.getValue());

        Map<String, List<PayChannelMarketing>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(PayChannelMarketing::getOperator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = payChannelMarketingContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.PAY_CHANNEL_MARKETING.getValue());
        });
    }

    private void payTypeMarketing(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("活动ID", "活动场景", "活动类型", "活动名称", "商品", "支付方式", "收银台", "立减金额", "优先级", "开始时间", "结束时间", "操作时间", "操作人");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-支付方式营销\n"
            + "到期数据：";

        List<PayTypeMarketing> expiringDataList = payTypeMarketingDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = payTypeMarketingContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.PAY_TYPE_MARKETING.getValue());

        Map<String, List<PayTypeMarketing>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(PayTypeMarketing::getApplicant));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = payTypeMarketingContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.PAY_TYPE_MARKETING.getValue());
        });
    }

    private void gift(List<String> addresseesList, String address) {
        List<String> tableTitles = Lists.newArrayList("活动ID", "策略名称", "赠品名称", "优先级", "平台", "收银台套餐", "开始时间", "结束时间", "操作人", "操作时间");
        String tableComment = "以下数据即将到期，请各位尽快确认是否延期\n"
            + "菜单：收银台管理-收银台置顶管理\n"
            + "到期数据：";

        List<Gift> expiringDataList = giftDao.queryExpiringData(rangeLeft, rangeRight);
        List<List<Object>> tableContents = giftContent(expiringDataList);
        String contentHotChat = tableContents.size() + "条数据即将到期";
        TableMailContent mailContent = new TableMailContent(tableComment, tableTitles, tableContents);
        List<TableMailContent> tableMailContentList = Collections.singletonList(mailContent);
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        if (expiringDataList.isEmpty()) {
            return;
        }
        alterService.sendMailAndHotChat(null, null, address, content, contentHotChat, ExpiringDataEnum.GIFT.getValue());

        Map<String, List<Gift>> expiringDataGroup = expiringDataList.stream()
            .collect(Collectors.groupingBy(Gift::getOperator));
        expiringDataGroup.forEach((operatorName, rules) -> {
            if (addresseesList.contains(operatorName + "@qiyi.com") || rules.isEmpty()) {
                return;
            }
            List<List<Object>> operatorTableContents = giftContent(rules);
            String operatorHotChat = rules.size() + "条数据即将到期";
            TableMailContent operatorMailContent = new TableMailContent(tableComment, tableTitles, operatorTableContents);
            String operatorContent = HtmlStyleUtil.tableHtml(Collections.singletonList(operatorMailContent));
            alterService.sendMailAndHotChat(null, null,
                operatorName, operatorContent, operatorHotChat, ExpiringDataEnum.GIFT.getValue());
        });
    }

    private List<List<Object>> qiyueRuleContent(List<ExpiringRule> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (ExpiringRule expiringData : expiringDataList) {
            String favorId = favorActivityDao.getFavorIdByActCode(expiringData.getActCode()) == null ? ""
                : favorActivityDao.getFavorIdByActCode(expiringData.getActCode()).toString();
            String skuName = "";
            if (expiringData.getSkuId() != null) {
                Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(expiringData.getSkuId());
                if (!skuResponseOptional.isPresent()) {
                    log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", expiringData.getSkuId(), expiringData.getId());
                } else {
                    skuName = skuResponseOptional.get().getSkuName();
                }
            }
            String currencyName = "";
            switch (expiringData.getCurrencyType()) {
                case "CASH":
                    currencyName = "现金";
                    break;
                case "POINTS":
                    currencyName = "积分";
                    break;
                case "CASH_AND_POINTS":
                    currencyName = "现金+积分";
                    break;
            }
            String strategy = "";
            switch (expiringData.getStrategy()) {
                case 1:
                    strategy = "折扣";
                    break;
                case 2:
                    strategy = "定价";
                    break;
                case 3:
                    strategy = "定单价";
                    break;
                case 4:
                    strategy = "立减";
                    break;
            }
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId().toString(),
                favorId,
                expiringData.getName(),
                skuName,
                currencyName,
                strategy,
                expiringData.getStrategyValue(),
                expiringData.getPriority(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getUpdateTime(),
                expiringData.getOperatorName(),
                expiringData.getActCode(),
                expiringData.getOriginActCode()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> vipStoreConfigContent(List<VipStoreConfig> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (VipStoreConfig expiringData : expiringDataList) {
            String strategyName = expiringData.getType().equals(1) ? "兜底策略" : "个性化策略";
            String platformName = platformDao.getByCode(expiringData.getPlatformCode()).getName();
            String storeName = basicDao.getNameByCode(expiringData.getStoreCode());
            String modeName = "";
            switch (expiringData.getMode()) {
                case 1:
                    modeName = "Tab平铺";
                    break;
                case 2:
                    modeName = "多Tab";
                    break;
                case 3:
                    modeName = "大融合";
                    break;
            }
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId().toString(),
                strategyName,
                expiringData.getName(),
                platformName,
                expiringData.getVersion(),
                storeName,
                modeName,
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getUpdateTime(),
                expiringData.getOperator()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> storeSwitchContent(List<StoreSwitch> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (StoreSwitch expiringData : expiringDataList) {
            String storeName = basicDao.getNameByCode(expiringData.getStoreCode());
            String switchValue = "true".equals(expiringData.getValue()) ? "开" : "关";
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId().toString(),
                expiringData.getName(),
                expiringData.getKey(),
                storeName,
                expiringData.getVersion(),
                switchValue,
                expiringData.getPriority(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getUpdateTime(),
                expiringData.getOperatorName()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> paymentContent(List<Payment> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (Payment expiringData : expiringDataList) {
            String storeName = basicDao.getNameByCode(expiringData.getStoreCode());
            String skuName = "";
            if (expiringData.getSkuId() != null) {
                Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(expiringData.getSkuId());
                if (!skuResponseOptional.isPresent()) {
                    log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", expiringData.getSkuId(), expiringData.getId());
                } else {
                    skuName = skuResponseOptional.get().getSkuName();
                }
            }
            String dataSource = "";
            switch (expiringData.getCreateChannel()) {
                case 1:
                    dataSource = "套餐";
                    break;
                case 2:
                    dataSource = "折扣大促";
                    break;
                case 3:
                    dataSource = "AB实验";
                    break;
                case 4:
                    dataSource = "支付方式";
                    break;
            }
            String level = levelDao.getMarkById(expiringData.getLevelId());
            String payType = payInfoDao.queryPayTypeName(expiringData.getPayType());
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId().toString(),
                expiringData.getName(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                storeName,
                skuName,
                expiringData.getVersion(),
                dataSource,
                level,
                expiringData.getPriority(),
                payType,
                expiringData.getSort(),
                expiringData.getRecommend().equals(1) ? "是" : "否",
                expiringData.getSupportSignFree().equals(1) ? "支持" : "不支持",
                expiringData.getUpdateTime(),
                expiringData.getUpdator()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> pointsContent(List<Points> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (Points expiringData : expiringDataList) {
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId(),
                expiringData.getName(),
                expiringData.getActTypeName(),
                expiringData.getPointsPartnerName(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getPriority(),
                expiringData.getOperator()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> bundleContent(List<Bundle> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (Bundle expiringData : expiringDataList) {
            String mainSkuName = "";
            if (expiringData.getMainSkuId() != null) {
                Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(expiringData.getMainSkuId());
                if (!skuResponseOptional.isPresent()) {
                    log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", expiringData.getMainSkuId(), expiringData.getId());
                } else {
                    mainSkuName = skuResponseOptional.get().getSkuName();
                }
            }
            String subSkuName = "";
            if (expiringData.getMainSkuId() != null) {
                Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(expiringData.getAdditionalSkuId());
                if (!skuResponseOptional.isPresent()) {
                    log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", expiringData.getAdditionalSkuId(), expiringData.getId());
                } else {
                    subSkuName = skuResponseOptional.get().getSkuName();
                }
            }
            String platformName = basicDao.getNameByCode(expiringData.getPlatformCode());
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId(),
                expiringData.getActName(),
                mainSkuName,
                subSkuName,
                platformName,
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getOperator(),
                expiringData.getActCode()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> smartStoreContent(List<SmartStore> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (SmartStore expiringData : expiringDataList) {
            String storeName = basicDao.getNameByCode(expiringData.getStoreCode());
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId(),
                expiringData.getName(),
                expiringData.getTitle(),
                storeName,
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getPriority(),
                expiringData.getOperator()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> payChannelMarketingContent(List<PayChannelMarketing> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (PayChannelMarketing expiringData : expiringDataList) {
            String scene = "";
            switch (expiringData.getScenes()) {
                case 1:
                    scene = "支付立减";
                    break;
                case 2:
                    scene = "支付挽留立减";
                    break;
                case 3:
                    scene = "支付有礼";
                    break;
            }
            String type = "";
            switch (expiringData.getType()) {
                case 1:
                    type = "银行卡支付立减";
                    break;
                case 2:
                    type = "微信-银行卡立减";
                    break;
                case 3:
                    type = "支付宝-银行卡立减";
                    break;
                case 4:
                    type = "支付宝-积分立减";
                    break;
                case 5:
                    type = "支付宝-积分挽留立减";
                    break;
            }
            String skuName = "";
            if (expiringData.getSkuIdList() != null) {
                Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(expiringData.getSkuIdList());
                if (!skuResponseOptional.isPresent()) {
                    log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", expiringData.getSkuIdList(), expiringData.getId());
                } else {
                    skuName = skuResponseOptional.get().getSkuName();
                }
            }
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId(),
                scene,
                type,
                expiringData.getName(),
                skuName,
                expiringData.getMinusFee(),
                expiringData.getPriority(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getUpdateTime(),
                expiringData.getOperator()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> payTypeMarketingContent(List<PayTypeMarketing> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (PayTypeMarketing expiringData : expiringDataList) {
            String scene = "";
            switch (expiringData.getScenes()) {
                case 1:
                    scene = "支付立减";
                    break;
                case 2:
                    scene = "支付挽留立减";
                    break;
                case 3:
                    scene = "支付有礼";
                    break;
            }
            String type = "";
            switch (expiringData.getType()) {
                case 1:
                    type = "银行卡支付立减";
                    break;
                case 2:
                    type = "微信-银行卡立减";
                    break;
                case 3:
                    type = "支付宝-银行卡立减";
                    break;
                case 4:
                    type = "支付宝-积分立减";
                    break;
                case 5:
                    type = "支付宝-积分挽留立减";
                    break;
            }
            String skuName = "";
            if (expiringData.getSkuId() != null) {
                Optional<QuerySkuResponse> skuResponseOptional = commodityClient.queryCommodity(expiringData.getSkuId());
                if (!skuResponseOptional.isPresent()) {
                    log.info("Query commodity by sku is not present. skuId:{}, ruleId:{}", expiringData.getSkuId(), expiringData.getId());
                } else {
                    skuName = skuResponseOptional.get().getSkuName();
                }
            }
            String payTypeName = payInfoDao.queryPayTypeName(expiringData.getPayType());
            String storeName = basicDao.getNameByCode(expiringData.getStoreCode());
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId(),
                scene,
                type,
                expiringData.getName(),
                skuName,
                payTypeName,
                storeName,
                expiringData.getMinusFee(),
                expiringData.getPriority(),
                expiringData.getValidStartTime(),
                expiringData.getValidEndTime(),
                expiringData.getUpdateTime(),
                expiringData.getApplicant()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

    private List<List<Object>> giftContent(List<Gift> expiringDataList) {
        List<List<Object>> tableContents = Lists.newArrayList();
        for (Gift expiringData : expiringDataList) {
            String platformName = Arrays.stream(expiringData.getPlatformCode().split(",")).map(basicDao::getNameByCode)
                .collect(Collectors.joining(","));
            String skuType = "";
            switch (expiringData.getSkuType()) {
                case 0:
                    skuType = "不限套餐";
                    break;
                case 1:
                    skuType = "限定套餐";
                    break;
            }
            List<Object> oneRowData = Lists.newArrayList(
                expiringData.getId(),
                expiringData.getName(),
                expiringData.getGiftName(),
                expiringData.getPriority(),
                platformName,
                skuType,
                expiringData.getOperator(),
                expiringData.getOperator()
            );
            tableContents.add(oneRowData);
        }
        return tableContents;
    }

}
