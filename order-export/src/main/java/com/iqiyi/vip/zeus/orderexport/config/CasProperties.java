package com.iqiyi.vip.zeus.orderexport.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

//@Component
@Data
public class CasProperties {

    @Value("${cas.server.host.url}")
    private String casServerUrl;//单点登录服务器地址

    @Value("${cas.server.host.login_url}")
    private String casServerLoginUrl;//单点登录服务方法

    @Value("${cas.server.host.logout_url}")
    private String casServerLogoutUrl;//单点登录退出地址

    @Value("${app.server.host.url}")
    private String appServerUrl;//应用地址

    @Value("${app.login.url}")
    private String appLoginUrl;//应用登录地址

    @Value("${app.logout.url}")
    private String appLogoutUrl;//应用登出地址
    @Value("${app.server.host.url}")
    private String projectUrl;

    @Value("${app.server.home.url}")
    private String homeUrl;
}
