package com.iqiyi.vip.zeus.orderexport.mysqlio.handler;

import com.iqiyi.vip.zeus.orderexport.entity.Platform;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEventUtil;
import com.iqiyi.vip.zeus.orderexport.mysqlio.MapCanalEvent;
import com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao;
import com.iqiyi.vip.zeus.orderexport.persist.dao.order.PlatformDao;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;

import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DutRenewSetLogHandler extends AbstractEventHandler {

    @Resource
    private AutoRenewDutTypeDao autoRenewDutTypeDao;
    @Resource
    private PlatformDao platformDao;

    @Override
    public boolean accept(MapCanalEvent event) {
        boolean superAccept = super.accept(event);
        if (!superAccept) {
            return false;
        }
        return CanalEventUtil.isInsert(event.getEventType());
    }

    @Override
    public void handCanalEvent(MapCanalEvent event) {
        Map<String, Object> rowAfter = event.getRowAfter();
        String agreementType = MapUtils.getString(rowAfter, "agreement_type", "1");
        String agreementNo = MapUtils.getString(rowAfter, "agreement_no", "0");
        String vipType = MapUtils.getString(rowAfter, "vip_type", "1");
        String dutType = MapUtils.getString(rowAfter, "type", "0");
        String amount = MapUtils.getString(rowAfter, "amount", "1");
        String operatorType = MapUtils.getString(rowAfter, "operator", "1");
        Integer payChannel = autoRenewDutTypeDao.getPayChannelByDutType(Integer.valueOf(dutType));
        // 优化：只解析一次 description，避免重复执行
        Map<String, Object> descMap = parseDescription(rowAfter);
        String operateScene = getOperateSceneFromDescMap(descMap);
        String signSource = getSignSourceFromDescMap(descMap);

        Platform platform = getPlatform(rowAfter);
        String platformName = platform != null ? platform.getName() : "default";

        Tag agreementTypeTag = new ImmutableTag("agreementType", agreementType);
        Tag agreementNoTag = new ImmutableTag("agreementNo", agreementNo);
        Tag vipTypeTag = new ImmutableTag("vipType", vipType);
        Tag dutTypeTag = new ImmutableTag("dutType", dutType);
        Tag payChannelTag = new ImmutableTag("payChannel", Objects.toString(payChannel, "0"));
        Tag operateSceneTag = new ImmutableTag("operateScene", Objects.toString(operateScene, "default"));
        Tag amountTag = new ImmutableTag("amount", amount);
        Tag operatorTypeTag = new ImmutableTag("operatorType", operatorType);
        Tag platformNameTag = new ImmutableTag("platformName", platformName);
        Tag signSourceTag = new ImmutableTag("signSource", Objects.toString(signSource, "default"));
        List<Tag> tags = Arrays.asList(agreementTypeTag, agreementNoTag, vipTypeTag, dutTypeTag,
                payChannelTag, operateSceneTag, amountTag, operatorTypeTag, platformNameTag, signSourceTag);
        Metrics.counter("autorenew_set_log_insert_total", tags).increment();
    }

    private Platform getPlatform(Map<String, Object> rowAfter) {
        Long platformId = MapUtils.getLong(rowAfter, "platform", null);
        if (platformId != null) {
            return platformDao.getById(platformId);
        }
        String platformCode = MapUtils.getString(rowAfter, "platform_code", null);
        if (StringUtils.isNoneBlank(platformCode)) {
            return platformDao.getByCode(platformCode);
        }
        return null;
    }

    /**
     * 解析 description 字段，避免重复解析
     * @param rowAfter 行数据
     * @return 解析后的 Map，如果解析失败返回 null
     */
    private Map<String, Object> parseDescription(Map<String, Object> rowAfter) {
        String description = MapUtils.getString(rowAfter, "description", null);
        if (StringUtils.isBlank(description)) {
            return null;
        }
        try {
            return JacksonUtils.parseMap(description);
        } catch (Exception e) {
            log.error("[DutRenewSetLogHandler] parse description error, description:{}", description, e);
            return null;
        }
    }

    /**
     * 从已解析的 descMap 中获取操作场景
     * @param descMap 已解析的描述信息 Map
     * @return 操作场景
     */
    private String getOperateSceneFromDescMap(Map<String, Object> descMap) {
        return MapUtils.getString(descMap, "scene");
    }

    /**
     * 从已解析的 descMap 中获取签约来源
     * @param descMap 已解析的描述信息 Map
     * @return 签约来源
     */
    private String getSignSourceFromDescMap(Map<String, Object> descMap) {
        return MapUtils.getString(descMap, "source");
    }

    @Override
    public String getTableName() {
        return "boss_dut_renew_set_log";
    }
}
