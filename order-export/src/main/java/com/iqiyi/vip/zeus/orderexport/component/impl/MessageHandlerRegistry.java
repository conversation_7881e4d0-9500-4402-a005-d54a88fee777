package com.iqiyi.vip.zeus.orderexport.component.impl;

import com.google.common.collect.Maps;
import com.iqiyi.vip.zeus.orderexport.mysqlio.handler.CanalEventHandler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created at: 2021-09-10
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageHandlerRegistry implements ApplicationContextAware, InitializingBean {

    private static Map<String, CanalEventHandler> handlersRepository = Maps.newHashMap();

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void afterPropertiesSet() {
        Map<String, CanalEventHandler> eventHandlers = this.applicationContext.getBeansOfType(CanalEventHandler.class);
        for (CanalEventHandler eventHandler : eventHandlers.values()) {
            handlersRepository.put(eventHandler.getTableName(), eventHandler);
        }
        log.info("Total Found {} CanalEventHandler", eventHandlers.size());
    }

    public CanalEventHandler findSuitableHandler(String tableName) {
        return handlersRepository.get(tableName);
    }

}
