package com.iqiyi.vip.zeus.orderexport.job;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.IJobHandler;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.zeus.core.component.GuardItemDataQuery;
import com.iqiyi.vip.zeus.core.component.GuardItemDataQueryFactory;
import com.iqiyi.vip.zeus.core.enums.GuardDatasourceType;
import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardMonitor;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.core.service.OrderGuardMonitorService;
import com.iqiyi.vip.zeus.core.service.guard.GuardDatasourceService;
import com.iqiyi.vip.zeus.core.utils.NumberConvertUtils;
import com.iqiyi.vip.zeus.orderexport.component.MailSender;
import com.iqiyi.vip.zeus.orderexport.util.DateUtils;
import com.iqiyi.vip.zeus.orderexport.util.mail.TableMailContent;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

/**
 * @author: guojing
 * @date: 2025/5/22 19:34
 */
@Slf4j
@Profile("!sg")
@Component
public class OrderGuardJob extends IJobHandler {

    private static final String QUERY_DAYS_PARAM = "queryDays";
    private static final String SAME_DAY_QUERY_PARAM = "sameDayQuery";
    private static final String EMAIL_RECEIVERS_PARAM = "emailReceivers";
    private static final int DEFAULT_QUERY_DAYS = 7;

    @ConfigJsonValue("${department.email.map:{\"会员交易开发组\":[\"<EMAIL>\"],\"营销合作开发组\":[\"<EMAIL>\"],\"后台开发组\":[\"<EMAIL>\"],\"付费基础开发组\":[\"<EMAIL>\"]}}")
    private Map<String, List<String>> departmentEmailMap;

    @Resource
    private OrderGuardMonitorService orderGuardMonitorService;
    @Resource
    private GuardItemDataQueryFactory guardItemDataQueryFactory;
    @Resource
    private GuardDatasourceService datasourceService;
    @Resource
    public ThreadPoolTaskExecutor orderGuardMonitorDataQueryExecutor;
    @Resource
    private MailSender mailSender;

    @Job("orderGuardJob")
    @Override
    public void execute() throws Exception {
        String today = CommonDateUtils.todayStr();
        Long jobId = JobHelper.getJobId();
        String jobParam = JobHelper.getJobParam();
        log.info("Start execute OrderGuardJob, jobId:{}, jobParam:{}", jobId, jobParam);
        JobHelper.log("Start execute OrderGuardJob, jobId:{}", jobId);
        StopWatch stopWatch = StopWatch.createStarted();
        Map<String, Object> jobParamMap = parseJobParam(jobId, jobParam);
        // 为了计算周同比，需要往前多查一天
        int queryDays = MapUtils.getInteger(jobParamMap, QUERY_DAYS_PARAM, DEFAULT_QUERY_DAYS) + 1;
        String emailReceiversStr = MapUtils.getString(jobParamMap, EMAIL_RECEIVERS_PARAM);
        Boolean sameDayQuery = MapUtils.getBoolean(jobParamMap, SAME_DAY_QUERY_PARAM, false);
        String endDay = BooleanUtils.isTrue(sameDayQuery) ? today : CommonDateUtils.yesterdayStr();
        String emailTitlePrefix = BooleanUtils.isTrue(sameDayQuery) ? "【订单稽核-今日】" : "【订单稽核】";

        List<OrderGuardMonitor> guardMonitors = orderGuardMonitorService.getAll().stream()
            .filter(g -> g.getId().equals(69))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(guardMonitors)) {
            handleJobSuccess(jobId, jobParam, "guard monitors empty", stopWatch);
            return;
        }


        List<String> dayList = DateUtils.getDateList(queryDays, endDay);
        // 按departmentName对监控项进行分组
        Map<String, List<OrderGuardMonitor>> departmentMonitorMap = guardMonitors.stream()
            .collect(Collectors.groupingBy(OrderGuardMonitor::getDepartmentName, Collectors.toList()));
        for (Map.Entry<String, List<OrderGuardMonitor>> departmentEntry : departmentMonitorMap.entrySet()) {
            String departmentName = departmentEntry.getKey();
            List<OrderGuardMonitor> departmentMonitors = departmentEntry.getValue();
            // 按category对监控项进行分组
            Map<String, List<OrderGuardMonitor>> categoryToMonitorListMap = departmentMonitors.stream()
                .collect(Collectors.groupingBy(OrderGuardMonitor::getCategory, Collectors.toList()));
            
            List<CompletableFuture<TableMailContent>> tableMailContentFutureList = new ArrayList<>();
            for (Map.Entry<String, List<OrderGuardMonitor>> entry : categoryToMonitorListMap.entrySet()) {
                List<OrderGuardMonitor> monitors = entry.getValue();
                CompletableFuture<TableMailContent> mailContentFuture = CompletableFuture.supplyAsync(() -> buildEmailContentList(monitors, dayList), orderGuardMonitorDataQueryExecutor);
                tableMailContentFutureList.add(mailContentFuture);
            }
    
            List<TableMailContent> tableEmailContentList = tableMailContentFutureList.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            String emailTitle = emailTitlePrefix + departmentName + "_监控项日报_" + dayList.get(0);
            String[] departmentAddressees = StringUtils.isBlank(emailReceiversStr)
                ? MapUtils.getObject(departmentEmailMap, departmentName, Collections.emptyList()).toArray(new String[]{})
                : emailReceiversStr.split(",");
            sendMailNew(emailTitle, tableEmailContentList, departmentAddressees);
        }

        handleJobSuccess(jobId, jobParam, "execute finished", stopWatch);
    }

    private TableMailContent buildEmailContentList(List<OrderGuardMonitor> monitors, List<String> dayList) {
        String endDay = dayList.get(0);
        String startDay = dayList.get(dayList.size() - 1);
        List<List<Object>> emailContentList = new ArrayList<>();
        for (OrderGuardMonitor guardMonitor : monitors) {
            if (StringUtils.isBlank(guardMonitor.getQuerySql())) {
                log.warn("querySql empty, id:{}, name: {}", guardMonitor.getId(), guardMonitor.getName());
                continue;
            }
            GuardDatasource datasource = datasourceService.getById(guardMonitor.getDatasourceId());
            if (datasource == null) {
                log.warn("datasource not exists, id: {}, name: {}, datasourceId: {}", guardMonitor.getId(), guardMonitor.getName(), guardMonitor.getDatasourceId());
                continue;
            }
            GuardDatasourceType datasourceType = GuardDatasourceType.parseValue(datasource.getType());
            if (datasourceType == null) {
                log.warn("datasource type error, id: {}, name: {}, datasourceType: {}", guardMonitor.getId(), guardMonitor.getName(), datasource.getType());
                continue;
            }
            GuardItemDataQuery guardItemDataQuery = guardItemDataQueryFactory.getQueryFactory(datasourceType.getQueryType());
            if (guardItemDataQuery == null) {
                log.warn("DataQueryType class not found, id: {}, name: {}, queryType: {}", guardMonitor.getId(), guardMonitor.getName(), datasourceType.getQueryType());
                continue;
            }
            Map<String, OrderGuardianQueryData> queryDataMap = guardItemDataQuery.queryData(datasource, guardMonitor.getQuerySql(), startDay, endDay);

            List<Object> rowDataList = new ArrayList<>();
            rowDataList.add(guardMonitor.getName());
            if (MapUtils.isEmpty(queryDataMap)) {
                rowDataList.addAll(Collections.nCopies(dayList.size() + 2, 0));
                emailContentList.add(rowDataList);
                continue;
            }

            OrderGuardianQueryData firstDayData = queryDataMap.containsKey(endDay) ? MapUtils.getObject(queryDataMap, endDay) : null;
            OrderGuardianQueryData secondDayData = queryDataMap.containsKey(dayList.get(1)) ? MapUtils.getObject(queryDataMap, dayList.get(1)) : null;
            OrderGuardianQueryData lastDayData = queryDataMap.containsKey(startDay) ? MapUtils.getObject(queryDataMap, startDay) : null;
            double firstDayValue = firstDayData != null && firstDayData.getCount() != null ? firstDayData.getCount() : 0;
            String firstDaySecondValue = firstDayData != null && firstDayData.getSecondCount() != null ? firstDayData.getSecondCount() : null;
            double secondDayValue = secondDayData != null && secondDayData.getCount() != null ? secondDayData.getCount() : 0;
            double lastDayValue = lastDayData != null && lastDayData.getCount() != null ? lastDayData.getCount() : 0;
            String firstDayContent = StringUtils.isNotBlank(firstDaySecondValue)
                ? String.format("%s(%s)", NumberConvertUtils.formatDouble(firstDayValue), firstDaySecondValue)
                : NumberConvertUtils.formatDouble(firstDayValue);
            if (!Objects.equals("0", firstDayContent) && StringUtils.isNotBlank(guardMonitor.getDetailSql())) {
                String url = String.format("http://viptrade-xuanwu.qsm.qiyi.middle/zeus/orderGuardian/queryDetailData?monitorId=%s&queryDay=%s", guardMonitor.getId(), endDay);
                firstDayContent = String.format("<a style='text-decoration:none' target='_blank' href='%s'>%s</a>", url, firstDayContent);
            }
            rowDataList.add(firstDayContent);
            rowDataList.add(formatRatio(firstDayValue, secondDayValue));
            rowDataList.add(formatRatio(firstDayValue, lastDayValue));
            for (int i = 1; i < dayList.size(); i++) {
                OrderGuardianQueryData queryData = MapUtils.getObject(queryDataMap, dayList.get(i));
                double firstCount = queryData != null && queryData.getCount() != null ? queryData.getCount() : 0;
                String secondCount = queryData != null ? queryData.getSecondCount() : null;
                String rowContent = secondCount != null
                    ? String.format("%s(%s)", NumberConvertUtils.formatDouble(firstCount), secondCount)
                    : NumberConvertUtils.formatDouble(firstCount);
                if (!Objects.equals("0", rowContent) && StringUtils.isNotBlank(guardMonitor.getDetailSql())) {
                    String url = String.format("http://viptrade-xuanwu.qsm.qiyi.middle/zeus/orderGuardian/queryDetailData?monitorId=%s&queryDay=%s", guardMonitor.getId(), dayList.get(i));
                    rowContent = String.format("<a style='text-decoration:none' target='_blank' href='%s'>%s</a>", url, rowContent);
                }
                rowDataList.add(rowContent);
            }
            emailContentList.add(rowDataList);
        }

        if (CollectionUtils.isEmpty(emailContentList)) {
            return null;
        }
        String tableComment = String.format("%s类监控项", monitors.get(0).getCategory());
        String firstHeaderName = Objects.equals(endDay, CommonDateUtils.todayStr()) ? "今天" : "昨天";
        String firstHeaderDayValue = String.format("<br/><span style='font-size:11px;font-weight: normal;'>%s</span>", endDay.substring(5));
        String dayRatioHeader = String.format("<br/><span style='font-size:11px;font-weight: normal;'>%s</span>", dayList.get(1).substring(5));
        String weekOnWeekRatioHeader = String.format("<br/><span style='font-size:11px;font-weight: normal;'>%s</span>", startDay.substring(5));
        List<String> tableHeaders = Lists.newArrayList("监控项",
            "<div style='text-align:center;'>" + firstHeaderName + firstHeaderDayValue + "</div>",
            "<div style='text-align:center;'>日环比" + dayRatioHeader + "</div>",
            "<div style='text-align:center;'>周同比" + weekOnWeekRatioHeader + "</div>"
        );
        List<String> dayPartList = dayList.subList(1, dayList.size()).stream()
            .map(s -> s.substring(5))
            .collect(Collectors.toList());
        tableHeaders.addAll(dayPartList);
        return new TableMailContent(tableComment, tableHeaders, emailContentList);
    }

    private String formatRatio(double current, double base) {
        double value = calcRatio(current, base);
        String color = value > 0 ? "red" : "green";
        if (value == 0) {
            return "0";
        }
        return String.format("<font color='%s'>%.0f%%</font>", color, value);
    }

    private double calcRatio(double current, double base) {
        if (current == base) {
            return 0;
        }
        if (current == 0) {
            return -100;
        }
        if (base == 0) {
            return 100;
        }
        return (current - base) / base * 100;
    }

    private void handleJobSuccess(Long jobId, String jobParam, String reason, StopWatch stopWatch) {
        String message = "OrderGuardJob finished[" + reason + "], jobId:{}, jobParam:{}, cost:{}ms";
        log.info(message, jobId, jobParam, stopWatch.getTime());
        JobHelper.log(message, jobId, jobParam, stopWatch.getTime());
        JobHelper.handleSuccess();
    }

    private Map<String, Object> parseJobParam(Long jobId, String jobParam) {
        if (StringUtils.isBlank(jobParam)) {
            return Collections.emptyMap();
        }
        try {
            return Stream.of(jobParam.split("&"))
                    .filter(pair -> pair.contains("="))
                    .map(pair -> pair.split("=", 2))
                    .collect(Collectors.toMap(
                            keyValue -> keyValue[0],
                            keyValue -> keyValue[1],
                            (existing, replacement) -> replacement,
                            HashMap::new));
        } catch (Exception e) {
            String errorMsg = "OrderGuardJob parse jobParam has exception";
            log.error("{}, jobId:{}, jobParam:{}", errorMsg, jobId, jobParam, e);
            JobHelper.log("{}, jobId:{}, jobParam:{}", errorMsg, jobId, jobParam);
            JobHelper.handleFail(errorMsg);
            return null;
        }
    }

    private void sendMailNew(String title, List<TableMailContent> tableMailContents, String[] addressees) {
        if (CollectionUtils.isEmpty(tableMailContents)) {
            return;
        }
        JobHelper.log("[OrderGuardJob] start send email");
        boolean sent = mailSender.sendMail(title, tableMailContents, addressees);
        if (sent) {
            JobHelper.log("[OrderGuardJob] send email finished, addressees:{0}", Arrays.toString(addressees));
        } else {
            JobHelper.log("[OrderGuardJob] send email failed");
        }
    }

}

