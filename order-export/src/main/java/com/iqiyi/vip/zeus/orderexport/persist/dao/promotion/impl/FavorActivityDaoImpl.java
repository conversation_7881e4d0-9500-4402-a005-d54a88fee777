package com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.impl;

import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorActivityDao;

/**
 * @Author: <PERSON>
 * @Date: 2022/8/12
 */
@Repository
@Profile("!sg")
public class FavorActivityDaoImpl implements FavorActivityDao {
    @Resource
    SqlSessionTemplate promotionSqlSessionTemplate;

    @Override
    public Long getFavorIdByActCode(String actCode) {
        return promotionSqlSessionTemplate.getMapper(FavorActivityDao.class).getFavorIdByActCode(actCode);
    }
}
