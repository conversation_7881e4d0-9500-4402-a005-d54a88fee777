package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @Author: <PERSON>
 * @Date: 2020/12/01
 */
@Data
public class Product implements Serializable {

    public final static String MONTHLY_SUPER_CODE = "a0226bd958843452";

    public final static long PRODUCT_SUBTYPE_SUPER = 1L;

    public final static long PRODUCT_SUBTYPE_FUNCTION = 3L;

    public final static long PRODUCT_SUBTYPE_WHITE_GOLD = 4L;

    public final static long PRODUCT_SUBTYPE_TENNIS = 7L;

    public final static Long PRODUCT_SUBTYPE_DIAMOND = 4L;

    /**
     * 天
     */
    public static final int PRODUCT_PERIODUNIT_DAY = 1;
    /**
     * 月
     */
    public static final int PRODUCT_PERIODUNIT_MONTH = 2;
    /**
     * 年
     */
    public static final int PRODUCT_PERIODUNIT_YEAR = 7;

    /**
     * 拥有赠品标识
     */
    public final static int HAVE_GIFT_PRODUCT = 1;

    // Fields
    /**
     * 名称
     */
    private String name;
    /**
     * 价格
     */
    private Integer price;
    /**
     * 有效期
     */
    private Integer period;
    /**
     * 有效期单位
     */
    private Integer periodUnit;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 状态
     */
    private Integer status = 1;
    /**
     * 下线时间
     */
    private Timestamp deadline;
    /**
     * id标识
     */
    protected Long id;
    /**
     * url
     */
    private String url;
    /**
     * 是否支持激活码
     */
    private Integer supportExp;
    /**
     * 体验卡支持类型
     */
    private String supportType;
    /**
     * 产品编码
     */
    private String code;
    /**
     * 子类型,套餐(教育、影视等),会员(经济套餐、超值套餐等)
     */
    private Long subType;
    /**
     * 源类型，针对升级套餐
     */
    private Long sourceSubType;
    /**
     * 服务类别，会员类：1，教育类：2
     */
    private Integer serviceType;
    /**
     * 非时长型：1，时长型：0
     */
    private Integer timeType = 0;

    private String payPageDesc;

    private String h5PayPageDesc;
    /**
     * 地区
     */
    private String area;

    /**
     * 会员类型code
     */
    private String vipTypeCode;
    /**
     * 升级产品的源会员类型code
     */
    private String sourceVipTypeCode;
    /**
     * 业务类型code
     */
    private String businessCode;
    /**
     * 付费类型
     */
    private Integer chargeType;
    /**
     * 原价
     */
    private Integer originalPrice;
    /**
     * 是否有赠品，1：有；0：没有
     */
    private Integer isHasGift;
    /**
     * 是否是预付费产品. 0:否, 1:是
     */
    private Integer prePaid;
    /**
     * 货币单位.
     */
    private String currencyUnit;

    /**
     * 货币符号
     */
    private String currencySymbol;
    /**
     * 套餐产品子类型
     */
    private Integer packageSubType;


    /**
     * 是否是预付费产品
     *
     * @return boolean return true if this product is pre-paid product
     */
    public boolean prePaidProduct() {
        return Integer.valueOf(1).equals(prePaid);
    }

    /**
     * 网球会员
     */
    public boolean tennisVipNonTV() {
        return "9e2482efade39c44".equals(vipTypeCode);
    }

    /**
     * TV网球会员
     */
    public boolean tvTennisVip() {
        return "8a9ac68b026408f1".equals(vipTypeCode);
    }

    /**
     * 单位是否是"天"
     */
    public boolean isDayPeriodUnit() {
        return this.periodUnit == PRODUCT_PERIODUNIT_DAY;
    }

    /**
     * 单位是否是"月"
     */
    public boolean isMonthPeriodUnit() {
        return this.periodUnit == PRODUCT_PERIODUNIT_MONTH;
    }

    /**
     * 单位是否是"年"
     */
    public boolean isYearPeriodUnit() {
        return this.periodUnit == PRODUCT_PERIODUNIT_YEAR;
    }

    /**
     * 是否是单点
     * @return
     */
    public boolean isVod() {
        return type != null && type == 0;
    }

}
