package com.iqiyi.vip.zeus.orderexport.component.impl;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.qiyi.vip.api.PaymentTypeServiceI;
import com.qiyi.vip.dto.MultiResponse;
import com.qiyi.vip.dto.data.PaymentTypeDTO;
import com.qiyi.vip.service.PaymentTypeClient;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

import static java.util.Arrays.asList;

/**
 * @author: linpeihui
 * @createTime: 2023/10/17
 */
@Component
public class PaymentInfoService {

    @Resource
    private PaymentTypeClient paymentTypeCloudClient;
    @Resource
    private PaymentTypeClient paymentTypeClient;

    @Value("${invokeByQSM:false}")
    private boolean invokeByQSM;

    @CacheRefresh(refresh = 600, stopRefreshAfterLastAccess = 1800)
    @Cached(cacheType = CacheType.LOCAL, name = "getPaymentType", key = "#payType")
    public PaymentTypeDTO getPaymentType(Integer payType) {
        List list = asList(payType);
        MultiResponse<PaymentTypeDTO> response = getPaymentTypeClient().getPaymentTypes(list);
        if (response.isSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
            return response.getData().get(0);
        }
        return null;
    }

    private PaymentTypeServiceI getPaymentTypeClient() {
        return invokeByQSM ? paymentTypeClient : paymentTypeCloudClient;
    }
}
