package com.iqiyi.vip.zeus.orderexport.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.zeus.orderexport.exception.CustomJsonException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created at: 2021-02-20
 *
 * <AUTHOR>
 */
@Slf4j
public class JacksonUtils {

    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
        try {
            return objectMapper.readValue(jsonString, javaType);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    /**
     * json str解析为map
     * @param jsonString
     * @return
     */
    public static Map<String, Object> parseMap(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>(){});
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> String toJsonString(T object) {
        if (object == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.error("writeValueAsString occurred exception, objectValue: {}", object, e);
            throw new CustomJsonException("解析json出现异常", e);
        }
    }

    public static <T> byte[] toJsonBytes(T object) {
        if (object == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsBytes(object);
        } catch (IOException e) {
            log.error("writeValueAsBytes occurred exception, objectValue: {}", object, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> Map<String, Object> beanToMap(T object) {
        if (object == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(object, new TypeReference<Map<String, Object>>(){});
    }

    public static <T> Map<String, String> beanToStringMap(T object) {
        if (object == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return objectMapper.convertValue(object, new TypeReference<Map<String, String>>(){});
    }

}
