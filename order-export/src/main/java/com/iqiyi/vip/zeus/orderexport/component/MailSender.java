package com.iqiyi.vip.zeus.orderexport.component;

import com.iqiyi.vip.zeus.orderexport.util.mail.MailComponent;
import com.iqiyi.vip.zeus.orderexport.util.mail.MailHeader;
import com.iqiyi.vip.zeus.orderexport.util.mail.TableMailContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @author: guojing
 * @date: 2025/5/27 19:19
 */
@Slf4j
@Component
public class MailSender {

    @Resource
    private MailComponent mailComponent;

    public boolean sendMail(String title, String tableComment, List<String> tableHeaders, List<List<Object>> tableContentList, String[] addressees) {
        if (CollectionUtils.isEmpty(tableContentList)) {
            return true;
        }
        MailHeader mailHeader = new MailHeader();
        mailHeader.setTos(addressees);
        mailHeader.setTitle(title);
        mailHeader.setNeedTitlePrefix(false);

        TableMailContent mailContent = new TableMailContent(tableComment, tableHeaders, tableContentList);
        log.info("start send email");
        try {
            mailComponent.sendMail(mailHeader, Arrays.asList(mailContent, mailContent));
            log.info("send email finished, addressees:{}", Arrays.toString(addressees));
            return true;
        } catch (Exception e) {
            log.error("send mail occurred exception", e);
        }
        return false;
    }

    public boolean sendMail(String title, List<TableMailContent> tableMailContentList, String[] addressees) {
        if (CollectionUtils.isEmpty(tableMailContentList)) {
            return true;
        }
        MailHeader mailHeader = new MailHeader();
        mailHeader.setTos(addressees);
        mailHeader.setTitle(title);
        mailHeader.setNeedTitlePrefix(false);

        log.info("start send multi table email");
        try {
            mailComponent.sendMail(mailHeader, tableMailContentList);
            log.info("send multi email finished, addressees:{}", Arrays.toString(addressees));
            return true;
        } catch (Exception e) {
            log.error("send multi mail occurred exception", e);
        }
        return false;
    }


}
