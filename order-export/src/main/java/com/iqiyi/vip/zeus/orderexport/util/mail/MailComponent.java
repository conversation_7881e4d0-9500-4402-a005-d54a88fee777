package com.iqiyi.vip.zeus.orderexport.util.mail;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;

/**
 * 邮件工具类
 *
 * <AUTHOR>
 * @date 2018/8/8 17:31
 * http://wiki.qiyi.domain/pages/viewpage.action?pageId=549260783
 */
@Slf4j
@Data
public class MailComponent {

    private static String nickName = "爱奇艺会员";

    /*邮箱前缀*/
    private String userName;
    /*邮箱全称*/
    private String from;
    /*获取邮箱密码的token*/
    private String token;

    /**
     * 发邮件-以表格形式
     *
     * @param header 邮件头
     * @param tableMailContentList 表格内容列表
     * @return true:发送成功, false:发送失败
     */
    public Boolean sendMail(MailHeader header, List<TableMailContent> tableMailContentList) throws Exception {
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        return sendMail(header, content);
    }

    private static int getTableWidth(MailHeader header) {
        int tableWidth;
        if (header.getTableWidth() != null) {
            tableWidth = header.getTableWidth();
        }else {
            tableWidth = MailHeader.DEFAULT_WIDTH;
        }
        return tableWidth;
    }

    /**
     * 发邮件-正文内容
     *
     * @param header 邮件头
     * @param content 邮件内容
     * @return true:发送成功, false:发送失败
     */
    public Boolean sendMail(MailHeader header, String content) throws Exception {
        JavaMailSenderImpl mailSender = mailSender();
        MimeMessage mailMessage = createMimeMessage(null,mailSender, header, content,from);
        send(null,mailSender, mailMessage);
        return true;
    }

    /**
     * 发邮件-以表格形式 (用自己配置的mailSender发邮件)
     *
     * @param mailSender 自己配置的mailSender
     * @param header 邮件头
     * @param tableMailContentList 表格内容列表
     * @return true:发送成功, false:发送失败
     */
    public Boolean sendMail(JavaMailSender mailSender, MailHeader header,
        List<TableMailContent> tableMailContentList) throws Exception {
        String content = HtmlStyleUtil.tableHtml(tableMailContentList);
        MimeMessage mailMessage = createMimeMessage(mailSender,null, header, content,from);
        send(mailSender,null, mailMessage);
        return true;
    }

    /**
     * 发邮件-正文内容 (用自己配置的mailSender发邮件)
     *
     * @param mailSender 自己配置的mailSender
     * @param header 邮件头
     * @param content 邮件内容
     * @return true:发送成功, false:发送失败
     */
    public Boolean sendMail(JavaMailSender mailSender, MailHeader header, String content) throws Exception {
        MimeMessage mailMessage = createMimeMessage(mailSender, null,header, content,from);
        send(mailSender,null, mailMessage);
        return true;
    }

    private static MimeMessage createMimeMessage(JavaMailSender customMailSender,  JavaMailSenderImpl mailSender,
        MailHeader header, String content,String from) throws Exception {
        MimeMessage mailMessage;
        if (customMailSender == null) {
            mailMessage = mailSender.createMimeMessage();
        } else {
            mailMessage = customMailSender.createMimeMessage();
        }

        //支持发送单人和多人,单人验证email地址
        String[] recipients = null;
        String code = "UTF-8";

        if (header.getTo() != null) {
            if (!MailStringUtils.validateEmail(header.getTo())) {
                log.error("to mail illegal");
                throw new RuntimeException("to email illegal");
            }
            recipients = new String[]{header.getTo()};
        } else if (header.getTos() != null) {
            recipients = header.getTos();
        } else {
            log.error("to and tos all null");
            throw new RuntimeException("to and tos all null");
        }

        for (String recipient : recipients) {
            if (recipient.indexOf("@tom") != -1) {
                code = "GBK";
                break;
            }
        }

        //设置utf-8或GBK编码，否则邮件会有乱码
        MimeMessageHelper messageHelper = new MimeMessageHelper(mailMessage, true, code);
        messageHelper.setTo(recipients);
        messageHelper.setFrom(new InternetAddress(from, nickName, "UTF-8"));
        //主题
        messageHelper.setSubject(getSubject(header));
        //邮件内容，注意加参数true，表示启用html格式
        content = MailStringUtils.removeContentTypeTag(content);
        messageHelper.setText(content, true);
        if (header.getAttachments() != null) {
            for (String filename : header.getAttachments()) {
                File file = new File(filename);
                if (file.exists()) {
                    messageHelper.addAttachment(MimeUtility.encodeWord(file.getName(), "UTF-8", null), file);
                }
            }
        }else if(header.getAttachmentSource() != null && StringUtils.isNotBlank(header.getAttachmentFileName())){
            messageHelper.addAttachment(MimeUtility.encodeWord(header.getAttachmentFileName(), "UTF-8", null), header.getAttachmentSource());
        }
        return mailMessage;
    }

    private static String getSubject(MailHeader header) {
        if (header.isNeedTitlePrefix()
            && !header.getTitle().contains(MailHeader.TITLE_PREFIX)) {

            return MailHeader.TITLE_PREFIX + header.getTitle();
        }
        return header.getTitle();
    }

    private void send(JavaMailSender customMailSender,JavaMailSenderImpl mailSender, MimeMessage mimeMessage) {
        if (customMailSender == null) {
            mailSender.send(mimeMessage);
        } else {
            customMailSender.send(mimeMessage);
        }
    }

    private JavaMailSenderImpl mailSender(){
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost("smtp.qiyi.domain");
        mailSender.setPort(465);
        mailSender.setUsername(userName);
        try {
            HashMap<String, Object> param = Maps.newHashMap();
            param.put("token",token);
            param.put("domainuser",userName);
            JSONObject jsonObject = MailHttpHelper.doGet("http://itpwd.qiyi.domain/api/GetPassword",param);
            if(jsonObject.get("password")!=null){
                String password = jsonObject.get("password").toString();
                mailSender.setPassword(password);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        Properties javaMailProperties = new Properties();
        javaMailProperties.setProperty("mail.smtp.auth", "true");
        javaMailProperties.setProperty("mail.smtp.timeout", "25000");
        mailSender.setJavaMailProperties(javaMailProperties);
        return mailSender;
    }



}
