package com.iqiyi.vip.zeus.orderexport.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.model.DashboardDisplayInfo;
import com.iqiyi.vip.zeus.core.req.DashboardCreateParam;
import com.iqiyi.vip.zeus.core.req.DashboardSearchParam;
import com.iqiyi.vip.zeus.core.service.EagleDashboardService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardMeta;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardSearchResult;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: guojing
 * @date: 2023/12/12 20:00
 */
@Profile("!sg")
@Slf4j
@Validated
@RestController
@RequestMapping("/zeus/dashboard")
@Api(tags = "鹰眼Dashboard相关接口")
public class DashboardController {

    @Value("${eagle.server.domain}")
    private String eagleServerDomain;
    @Resource
    private EagleDashboardService dashboardService;

    @ApiOperation(value = "创建dashboard")
    @PostMapping(value = "/create")
    public BaseResponse<DashboardDisplayInfo> create(@Validated @RequestBody DashboardCreateParam createParam) {
        return BaseResponse.createSuccess(dashboardService.create(createParam));
    }

    /**
     * 搜索dashboard，在当前用户所在Folder下
     * @param searchParam
     */
    @ApiOperation(value = "搜索dashboard")
    @GetMapping(value = "/search")
    public BaseResponse<List<DashboardSearchResult>> search(@Validated DashboardSearchParam searchParam) {
        AuthorityTeamBasic userRealTeam = RequestContextHolder.getCurrentUserRealTeam();
        return BaseResponse.createSuccessList(dashboardService.search(searchParam, userRealTeam));
    }

    /**
     * 根据dashboardUid查询Dashboard详情
     * @param dashboardUid
     */
    @ApiOperation(value = "根据dashboardUid查询Dashboard详情")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "dashboardUid", value = "dashboardUid", dataType = "String", required = true, paramType = "query"),
        @ApiImplicitParam(name = "withoutCache", value = "withoutCache", dataType = "Boolean", paramType = "query")
    })
    @GetMapping(value = "/getByUid")
    public BaseResponse<DashboardDisplayInfo> getByUid(@NotBlank(message = "dashboardUid不能为空") String dashboardUid, Boolean withoutCache) {
        DashboardWithMeta dashboardWithMeta = BooleanUtils.isTrue(withoutCache)
            ? dashboardService.getByUidWithoutCache(dashboardUid)
            : dashboardService.getByUid(dashboardUid);
        if (dashboardWithMeta == null) {
            return BaseResponse.createSuccess(null);
        }
        DashboardMeta dashboardMeta = dashboardWithMeta.getMeta();
        if (dashboardMeta != null) {
            dashboardMeta.setUrl(eagleServerDomain + dashboardMeta.getUrl());
            dashboardMeta.setFolderUrl(eagleServerDomain + dashboardMeta.getFolderUrl());
        }
        return BaseResponse.createSuccess(DashboardDisplayInfo.buildFrom(eagleServerDomain, dashboardWithMeta));
    }

}
