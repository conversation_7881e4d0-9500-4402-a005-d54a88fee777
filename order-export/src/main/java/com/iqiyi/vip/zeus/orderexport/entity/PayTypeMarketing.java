package com.iqiyi.vip.zeus.orderexport.entity;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/4/10 8:45
 */
@Data
public class PayTypeMarketing {
    private Integer id;
    private Integer scenes;
    private Integer type;
    private String name;
    private String skuId;
    private Integer payType;
    private String storeCode;
    private Integer minusFee;
    private Integer priority;
    private Timestamp validStartTime;
    private Timestamp validEndTime;
    private Timestamp updateTime;
    private String applicant;
}
