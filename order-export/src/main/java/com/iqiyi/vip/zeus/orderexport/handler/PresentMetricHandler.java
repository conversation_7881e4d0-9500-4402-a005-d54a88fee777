package com.iqiyi.vip.zeus.orderexport.handler;

import com.iqiyi.vip.zeus.orderexport.entity.PresentOrder;
import com.iqiyi.vip.zeus.orderexport.entity.PresentRecord;

import io.micrometer.core.instrument.ImmutableTag;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tag;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
public class PresentMetricHandler {

    public static Map<String, Integer> reportPresentOrderData(List<PresentOrder> presentOrders) {
        if (CollectionUtils.isEmpty(presentOrders)) {
            return Collections.emptyMap();
        }

        Map<String, Integer> presentOrderCountMap = new HashMap<>();
        for (PresentOrder presentOrder : presentOrders) {
            String buyVipType = presentOrder.getBuyType();
            String presentVipType = presentOrder.getPresentType();
            Integer presentStatus = presentOrder.getStatus();
            Tag buyVipTypeTag = new ImmutableTag("buyVipType", buyVipType);
            Tag presentVipTypeTag = new ImmutableTag("presentVipType", presentVipType);
            Tag presentStatusTag = new ImmutableTag("presentStatus", presentStatus.toString());
            List<Tag> tags = Arrays.asList(buyVipTypeTag, presentVipTypeTag, presentStatusTag);
            Metrics.counter("vip_trade_present_order_total", tags).increment();
            String vipTypeKey = buyVipType + "-" + presentVipType;
            if (!presentOrderCountMap.containsKey(vipTypeKey)) {
                presentOrderCountMap.put(vipTypeKey, 0);
            }
            presentOrderCountMap.put(vipTypeKey, presentOrderCountMap.get(vipTypeKey) + 1);
        }
        return presentOrderCountMap;
    }

    public static Map<String, Integer> reportPresentRecordData(List<PresentRecord> presentRecords) {
        if (CollectionUtils.isEmpty(presentRecords)) {
            return Collections.emptyMap();
        }

        Map<String, Integer> presentRecordCountMap = new HashMap<>();
        for (PresentRecord presentRecord : presentRecords) {
            String buyVipType = presentRecord.getBuyType();
            String presentVipType = presentRecord.getPresentType();
            Tag buyVipTypeTag = new ImmutableTag("buyVipType", buyVipType);
            Tag presentVipTypeTag = new ImmutableTag("presentVipType", presentVipType);
            List<Tag> tags = Arrays.asList(buyVipTypeTag, presentVipTypeTag);
            Metrics.counter("vip_trade_present_record_total", tags).increment();

            String vipTypeKey = buyVipType + "-" + presentVipType;
            if (!presentRecordCountMap.containsKey(vipTypeKey)) {
                presentRecordCountMap.put(vipTypeKey, 0);
            }
            presentRecordCountMap.put(vipTypeKey, presentRecordCountMap.get(vipTypeKey) + 1);
        }

        return presentRecordCountMap;
    }

}
