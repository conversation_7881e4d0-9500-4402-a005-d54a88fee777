package com.iqiyi.vip.zeus.orderexport.persist.dao.expire;

import io.lettuce.core.dynamic.annotation.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

import com.iqiyi.vip.zeus.orderexport.entity.Points;

/**
 * <AUTHOR>
 * @date 2024/4/8 21:21
 */
public interface PointsDao {

    @Select("select * from points_activity_config "
        + "where status =1 "
        + "and valid_start_time<now() "
        + "and valid_end_time>date_add(now(),interval #{rangeLeft} day) "
        + "and valid_end_time<date_add(now(),interval #{rangeRight} day)")
    List<Points> queryExpiringData(@Param("rangeLeft") String rangeLeft, @Param("rangeRight") String rangeRight);
}
