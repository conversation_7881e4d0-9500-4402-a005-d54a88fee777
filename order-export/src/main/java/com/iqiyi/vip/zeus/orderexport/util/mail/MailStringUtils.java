package com.iqiyi.vip.zeus.orderexport.util.mail;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: guojing
 * @date: 2025/6/4 18:21
 */
public class MailStringUtils {

    private static final Pattern EMAIL_ADDRESS_PATTERN = Pattern.compile("^([a-z0-9A-Z_\\-|\\.])+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$");

    private static final Pattern CONTENT_TYPE_PATTERN = Pattern.compile("<meta[^>]*?charset\\s*=\\s*([\\w-\\s]+).*?>");

    private static Pattern patternDouble = Pattern.compile("^[-\\+]?[.\\d]*$");

    private static Pattern patternNegativeInt = Pattern.compile("^-?[0-9]+$");

    public static String removeContentTypeTag(String content) {
        if (content == null || content.equals("")) {
            return content;
        }
        Matcher m = CONTENT_TYPE_PATTERN.matcher(content);
        return m.replaceAll("");
    }

    public static boolean validateEmail(String email) {
        Matcher matcher = EMAIL_ADDRESS_PATTERN.matcher(email);
        return matcher.matches();
    }

    /**
     * 数值转换千分位
     *
     * 1.整数转换为千分位
     * 转换前:1234567
     * 转换后:1,234,567
     * 2.小数转换为千分位
     * 格式            输入       输出
     * ,###,###.00    0.593     .59
     * ,###,##0.00    0.593     0.59
     * 转换前:6789.30
     * 转换后:6,789.30
     * @param source
     * @return
     */
    public static Object calNumberThousands(Object source) {
        if (source == null) {
            return source;
        }
        String value = source.toString();
        if (StringUtils.isBlank(value) || "-".equals(source)) {
            return source;
        }

        if (org.apache.commons.lang3.math.NumberUtils.isDigits(value)) {
            //正整数大于12位（万亿），则不做千分位处理，这类数据一般是批次号
            if (value.length() > 12) {
                return source;
            }
            BigDecimal bigD = new BigDecimal(value);
            DecimalFormat df = new DecimalFormat(",###,###");
            return df.format(bigD);
        }

        if (patternNegativeInt.matcher(value).matches()) {
            //负整数大于13位（万亿），则不做千分位处理，这类数据一般是批次号
            if (value.length() > 13) {
                return source;
            }
            BigDecimal bigD = new BigDecimal(value);
            DecimalFormat df = new DecimalFormat(",###,###");
            return df.format(bigD);
        }

        if (patternDouble.matcher(value).matches()) {
            //小数大于15位（万亿），则不做千分位处理，这类数据一般是批次号
            if (value.length() > 15) {
                return source;
            }
            BigDecimal bigD = new BigDecimal(value);
            DecimalFormat df = new DecimalFormat(",###,##0.00");
            return df.format(bigD);
        }

        return source;
    }

}
