spring:
  application:
    name: intl-order-export
  cache:
    type: caffeine
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=60s
  cloud:
    loadbalancer:
      retry:
        enabled: true
      ribbon:
        enabled: true
  main:
    allow-bean-definition-overriding: true

server:
  port: 8080

intl:
  flag: true

management:
  security:
    enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
      jmx:
        enabled: true
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: health,info,prometheus
      base-path: /actuator
  health:
    db:
      enabled: false
    redis:
      enabled: false
    defaults:
      enabled: false
  info:
    git:
      mode: full

# eureka config
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    non-secure-port: 8080
    lease-renewal-interval-in-seconds: 3
    lease-expiration-duration-in-seconds: 5
  client:
    service-url:
      defaultZone: http://***********:8080/eureka/
    healthcheck:
      enabled: true
    register-with-eureka: false

  ribbon:
    eureka:
      enabled: true
    restclient:
      enabled: true
    ConnectTimeout: 3000
    ReadTimeout: 500
#配置中心
cloud:
  config:
    app:
      name: intl-vip-xuanwu
      env: dev
      region: intl

mysql:
  order:
    binlog:
      rmq:
        namesrvaddr: aws-apse1-az1.intl-aliyun-test-multi.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-aliyun-test-multi.online002.rocketmq.qiyi.middle:9876
        consumer:
          token: CT-716810bd-3524-4470-82d2-cb8c59b508c5

trade:
  order:
    rmq:
      namesrvaddr: aws-apse1-az1.intl-aliyun-test-multi.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-aliyun-test-multi.online002.rocketmq.qiyi.middle:9876
      paid:
        consumer:
          token: CT-861df39e-08f5-476e-b7d8-f3aaf1422ab6
      finished:
        consumer:
          token: CT-726149db-a921-4495-a363-beb5de085be4
      monitor:
        producer:
          token: PT-42e26969-afbc-4720-8416-7004a7c633d9
        consumer:
          token: CT-16faf587-c498-48dd-aead-bd560a4ca6cb

#自动续费binlog RMQ集群配置
autorenew:
  binlog:
    rmq:
      namesrvaddr: vip-autorenew-rocketmq-dev009-bdwg.qiyi.virtual:9876;vip-autorenew-rocketmq-dev011-bdwg.qiyi.virtual:9876
      consumer:
        token: CT-dd9f59e2-7dd8-4299-bcf5-e1f0d77ccdc3
  async:
    task:
      binlog:
        rmq:
          consumer:
            token: CT-f7d3b0cf-f2dc-414d-85a9-545e28a44f27
      rmq:
        consumer:
          token: CT-9a62bcdd-8764-4a40-9de3-522c56f94a03


datasource:
  order:
    url: ******************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  present:
    url: ****************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  autorenew:
    url: *******************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  tidb:
    order:
      url: ***********************************************************************************************************************************************************************************************************************************************************************
      username: vip_test
      password: rg_z_6UF)w=Y
      driverClassName: com.mysql.jdbc.Driver
      hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
        minimumIdle: 10
        maximumPoolSize: 10
        maxLifetime: 1800000
        idleTimeout: 600000
        connectionTimeout: 30000
  zeus:
    url: *********************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000

# vip-job配置
vip:
  job:
    access:
      way: qke
    accessToken: 1c542274efdd42dfb0eeaf1663daa585
    admin:
      addresses: http://qiyi-job-admin.test.qiyi.domain
    executor:
      appname: intl-vip-xuanwu
      logpath: /data/log/vip-xuanwu/
      port: 9083
      switch: on
    qke:
      app:
        id: 12388


# 买赠present_order表个数
present:
  order:
    table:
      count: 100

cache:
  max:
    size: 5000000
  expire:
    hours: 6

user:
  statics:
    cache:
      max:
        size: 5000000
      expire:
        minutes: 60

pay:
  info:
    url: http://viptrade-pay-info/api/payChannel/
    sign:
      key: 123456


#邮件配置
mail:
  user:
    name: vipmessage
    address: <EMAIL>
    token: 8u36q9d63g96hqlr

# Order Sharding Configuration
order:
  sharding:
    tableSize: 2
    database:
      urls: ******************************************************,******************************************************
      username: vip_test
      password: rg_z_6UF)w=Y
  system:
    rootUrl: http://ORDER-SYSTEM-TEST/api/orders/repo/
    source: vip-xuanwu
    signKey: 123456

commodity:
  config:
    app:
      caller: vip-operation-api-test
      signKey: 123456
      url: http://vcc-test.vip.qiyi.domain/vip-commodity
      profile: test

qiyi:
  job:
    domain: http://qiyi-job-admin.test.qiyi.domain

alertUrl: http://admin-test.vip.qiyi.domain/watcher-api/alert/sendAlert

jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 300000

DBM_CONFIG_APPID: qpaas-db-viptrade-xuanwu-TEST
APOLLO_PAAS_TOKEN: 9ffa1cd1-6bbe-60ae-4526-27fbfb8e3adb
DBM_CONFIG_RD_IDS: redis-120880071