# 天网 - Thymeleaf 模板集成说明

## 目录结构

```
order-export/src/main/resources/
├── static/                     # 静态资源目录
│   ├── css/
│   │   └── main.css           # 样式文件
│   ├── js/
│   │   └── main.js            # JavaScript文件
│   └── assets/                # 其他静态资源
└── templates/                  # Thymeleaf模板目录
    ├── index.html             # 主页面模板
    ├── order-guardian-detail-data.html
    ├── packageMail.html
    └── ruleMail.html
```

## Controller 集成

### 示例Controller
已创建 `TianWangController.java`，包含以下功能：

- **主页**: `/tianwang` - 默认仪表盘页面
- **数据源管理**: `/tianwang/datasources`
- **稽核项管理**: `/tianwang/items`
- **我的订阅**: `/tianwang/subscription`
- **稽核报告**: `/tianwang/reports`
- **操作日志**: `/tianwang/logs`

### 数据传递
使用 `ModelAndView` 将数据传递给模板：

```java
model.addAttribute("user", userObject);
model.addAttribute("datasources", datasourceList);
model.addAttribute("guardItems", guardItemList);
model.addAttribute("reportData", reportDataList);
```

## 模板语法

### Thymeleaf 命名空间
```html
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
```

### 静态资源引用
```html
<link rel="stylesheet" th:href="@{/css/main.css}">
<script th:src="@{/js/main.js}"></script>
```

### 数据绑定
```html
<title th:text="${pageTitle != null ? pageTitle : '天网'}">天网</title>
<img th:alt="${user?.name}" />
```

### 服务器数据传递到JavaScript
```html
<script th:inline="javascript">
    window.serverData = {
        activeTab: /*[[${activeTab}]]*/ 'dashboard',
        userRole: /*[[${user?.role}]]*/ 'DEVELOPER',
        userName: /*[[${user?.name}]]*/ '张三',
        datasources: /*[[${datasources}]]*/ [],
        guardItems: /*[[${guardItems}]]*/ [],
        reportData: /*[[${reportData}]]*/ []
    };
</script>
```

## JavaScript集成

### 服务器数据获取
JavaScript代码已调整为可以从 `window.serverData` 获取服务器传递的数据：

```javascript
const userName = ref(window.serverData?.userName || '张三');
const userRole = ref(window.serverData?.userRole || 'DEVELOPER');
```

### 数据初始化
在应用挂载前会检查服务器数据：

```javascript
if (window.serverData) {
    console.log('服务器数据已加载:', window.serverData);
}
```

## 后续开发建议

### 1. Service层开发
- 创建 `AuditService`、`DatasourceService` 等业务服务
- 在Controller中注入服务，获取真实数据

### 2. 实体类设计
- `User` - 用户实体
- `Datasource` - 数据源实体
- `AuditItem` - 稽核项实体
- `AuditReport` - 稽核报告实体

### 3. API接口开发
- RESTful API用于AJAX调用
- 表单提交处理
- 文件上传/下载

### 4. 安全集成
- Spring Security集成
- 角色权限控制
- CSRF保护

### 5. 数据库集成
- MyBatis配置
- 实体映射
- 数据访问层

## 访问URL

启动应用后，访问以下URL：

- 主页: `http://localhost:8080/tianwang`
- 数据源管理: `http://localhost:8080/tianwang/datasources`
- 稽核项管理: `http://localhost:8080/tianwang/items`
- 我的订阅: `http://localhost:8080/tianwang/subscription`
- 稽核报告: `http://localhost:8080/tianwang/reports`
- 操作日志: `http://localhost:8080/tianwang/logs`
