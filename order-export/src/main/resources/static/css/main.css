/* 业务稽核系统 - 主样式文件 */
/* Ant Design Vue 风格 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: #f0f2f5;
}

/* 布局容器 */
.app-layout {
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    background: #ffffff;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    width: 260px;
    z-index: 1000;
    overflow-y: auto;
    box-shadow: none;
    border-right: 1px solid #e5e7eb;
}

.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 2px;
}

/* Logo 区域 */
.logo-container {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    color: #1890ff;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
}

.logo-content {
    text-align: left;
    width: 100%;
}

.logo-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
}

.logo-title i {
    margin-right: 8px;
    font-size: 16px;
}

.logo-subtitle {
    font-size: 11px;
    color: #8c8c8c;
    letter-spacing: 0.5px;
    margin-left: 24px;
}

/* 主布局 */
.main-layout {
    margin-left: 260px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    background: white;
    height: 64px;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-title {
    font-size: 20px;
    font-weight: 600;
    color: #262626;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.action-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    color: #595959;
}

.action-icon:hover {
    background: #f5f5f5;
    color: #1890ff;
}

/* 用户下拉菜单 */
.user-dropdown {
    position: relative;
    cursor: pointer;
}

.user-dropdown__menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 160px;
    z-index: 1050;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    padding: 4px 0;
}

.user-dropdown__item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    color: #262626;
    font-size: 14px;
    transition: background-color 0.2s;
}

.user-dropdown__item:hover {
    background-color: #f5f5f5;
}

.user-dropdown__item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

/* 表单提示样式 */
.form-hint {
    font-size: 12px;
    color: #8c8c8c;
    margin-top: 4px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border-left: 3px solid #1890ff;
}


.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    cursor: pointer;
    position: relative;
    z-index: 1;
}

.user-info:hover {
    background: #f8fafc;
    border-color: #e5e7eb;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 0;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    line-height: 1.2;
}

.dropdown-arrow {
    font-size: 12px;
    color: #8c8c8c;
    transition: transform 0.3s;
    flex-shrink: 0;
}

.dropdown-arrow.rotated {
    transform: rotate(180deg);
}

.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 160px;
    z-index: 1050;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    padding: 4px 0;
    display: none;
}

.user-menu.show {
    display: block;
}


.menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    color: #262626;
    font-size: 14px;
    line-height: 1.5;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
}

.menu-item:hover {
    background: #f5f5f5;
    color: #1890ff;
}

.menu-item:active {
    background: #e6f7ff;
    color: #1890ff;
}

.menu-item i {
    font-size: 14px;
    width: 16px;
    text-align: center;
    color: #8c8c8c;
    transition: color 0.2s ease;
}

.menu-item:hover i {
    color: #1890ff;
}

.menu-item span {
    font-size: 14px;
    font-weight: normal;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 24px;
}

/* 统计卡片 */
.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,0.03), 0 1px 6px -1px rgba(0,0,0,0.02), 0 2px 4px 0 rgba(0,0,0,0.02);
    transition: all 0.3s;
    border: 1px solid #f0f0f0;
}

.stat-card:hover {
    box-shadow: 0 4px 12px 0 rgba(0,0,0,0.05), 0 2px 4px 0 rgba(0,0,0,0.02);
    transform: translateY(-2px);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.stat-icon {
    width: 56px;
    height: 56px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.stat-number {
    font-size: 32px;
    font-weight: 600;
    color: #262626;
    line-height: 1.2;
    margin-bottom: 8px;
}

.stat-label {
    color: #8c8c8c;
    font-size: 14px;
    margin-bottom: 8px;
}

.stat-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.trend-up {
    color: #52c41a;
}

.trend-down {
    color: #ff4d4f;
}

/* 卡片组件 */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px 0 rgba(0,0,0,0.03), 0 1px 6px -1px rgba(0,0,0,0.02), 0 2px 4px 0 rgba(0,0,0,0.02);
    border: 1px solid #f0f0f0;
    margin-bottom: 24px;
}

.card-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

.card-body {
    padding: 24px;
}

/* 搜索区域 */
.search-section {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 12px 16px;
    margin-bottom: 12px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

/* 搜索表单布局 */
.search-form-layout {
    display: flex;
    gap: 24px;
    align-items: flex-start;
}

.search-fields {
    flex: 1;
}

.form-row {
    display: grid;
    gap: 12px;
    margin-bottom: 12px;
}

.form-row:last-child {
    margin-bottom: 0;
}

/* 主要搜索字段行 */
.form-row-primary {
    grid-template-columns: 2fr 1fr 1fr;
    margin-bottom: 16px;
}

/* 辅助搜索字段行 */
.form-row-secondary {
    grid-template-columns: 1fr 2fr 1fr;
}

/* 宽字段样式 */
.form-item-wide {
    /* 继承父级grid布局，不需要额外样式 */
}

/* 普通字段样式 */
.form-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.search-actions {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: flex-end;
    padding-top: 20px;
    min-width: 160px;
    justify-content: flex-end;
}

/* 3列网格布局 */
.form-grid-3col {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    align-items: end;
}

/* 操作按钮样式 */
.form-actions {
    display: flex;
    align-items: end;
    gap: 8px;
}

/* 图表容器 */
.chart-container {
    height: 320px;
    position: relative;
}

.chart-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

/* 订阅网格 */
.subscription-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 24px;
}

.subscription-item {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s;
    background: white;
}

.subscription-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24,144,255,0.1);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.item-info h4 {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 4px 0;
    color: #262626;
}

.item-info p {
    font-size: 12px;
    color: #8c8c8c;
    margin: 0;
}

/* 报告筛选 */
.report-filters {
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

/* 导航菜单 */
.nav-menu {
    padding: 8px 0;
}

.nav-section {
    margin-bottom: 24px;
}

.nav-section-title {
    font-size: 12px;
    font-weight: 600;
    color: #8c8c8c;
    text-transform: uppercase;
    margin: 0 24px 12px;
    letter-spacing: 0.5px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 12px 24px;
    color: #595959;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    position: relative;
    border: none;
    margin: 2px 16px;
    border-radius: 6px;
}

.nav-item:hover {
    background: #f5f5f5;
    color: #1890ff;
}

.nav-item.active {
    background: #1890ff;
    color: white;
}

.nav-item i {
    width: 18px;
    margin-right: 12px;
    font-size: 14px;
    text-align: center;
}

.nav-item span {
    font-size: 14px;
    font-weight: 400;
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    background: white;
    color: #262626;
    text-decoration: none;
}

.btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.btn-primary {
    background: #1890ff;
    border-color: #1890ff;
    color: white;
}

.btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: white;
}

.btn-secondary {
    background: #f5f5f5;
    border-color: #d9d9d9;
    color: #595959;
}

.btn-secondary:hover {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.btn-danger {
    color: #ff4d4f;
    border-color: #ff4d4f;
}

.btn-danger:hover {
    background: #ff4d4f;
    color: white;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 标签组件 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    border: 1px solid;
}

.tag-primary {
    background: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
}

.tag-success {
    background: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
}

.tag-warning {
    background: #fff7e6;
    border-color: #ffd591;
    color: #fa8c16;
}

.tag-danger {
    background: #fff2f0;
    border-color: #ffccc7;
    color: #ff4d4f;
}

.tag-info {
    background: #f0f0f0;
    border-color: #d9d9d9;
    color: #595959;
}

/* 类型徽章样式 */
.type-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-align: center;
    flex-shrink: 0;
    line-height: 1;
}

.type-accuracy {
    background-color: #52c41a; /* 绿色 - 准确性 */
}

.type-consistency {
    background-color: #1890ff; /* 蓝色 - 一致性 */
}

.type-timeliness {
    background-color: #faad14; /* 橙色 - 时效性 */
}

.type-rationality {
    background-color: #722ed1; /* 紫色 - 合理性 */
}

.type-completeness {
    background-color: #ff4d4f; /* 红色 - 完整性 */
}

.type-default {
    background-color: #d9d9d9; /* 灰色 - 默认 */
    color: #666;
}

/* 表格组件 */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    text-align: left;
    font-weight: 500;
    color: #262626;
    font-size: 14px;
}

.table td {
    border-bottom: 1px solid #f5f5f5;
    padding: 12px 16px;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: #fafafa;
}

/* URL文本样式 */
.url-text {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #1890ff;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    background: #f0f9ff;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #e1f5fe;
}

.url-text:hover {
    background: #e6f7ff;
    border-color: #91d5ff;
}

/* 稽核报告表头特殊样式 */
.report-table-header {
    background: #fafafa !important;
    color: #262626 !important;
    font-weight: 600 !important;
}

/* 表单组件 */
.form-item {
    margin-bottom: 8px;
}

.form-label {
    display: block;
    margin-bottom: 2px;
    color: #262626;
    font-size: 14px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

/* 模态框组件 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.45);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 16px 48px rgba(0,0,0,0.12);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #8c8c8c;
}

.modal-close:hover {
    background: #f5f5f5;
    color: #262626;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 12px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
}

/* 数据源详情样式 */
.datasource-detail {
    max-height: 60vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 24px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-title {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-label {
    font-size: 12px;
    font-weight: 500;
    color: #8c8c8c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    font-size: 14px;
    color: #262626;
    word-break: break-all;
}

.detail-value.code-block {
    background-color: #f5f5f5;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 8px 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    max-height: 120px;
    overflow-y: auto;
}

.type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-mysql { background-color: #e6f7ff; color: #1890ff; }
.type-tidb { background-color: #f6ffed; color: #52c41a; }
.type-clickhouse { background-color: #fff7e6; color: #fa8c16; }
.type-starrocks { background-color: #f9f0ff; color: #722ed1; }
.type-hive { background-color: #fff1f0; color: #ff4d4f; }
.type-prometheus { background-color: #f0f0f0; color: #595959; }

.status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background-color: #f6ffed;
    color: #52c41a;
}

.status-inactive {
    background-color: #fff1f0;
    color: #ff4d4f;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f0f0f0;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 分页组件 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 24px;
}

.page-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    color: #262626;
}

.page-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.page-btn.active {
    background: #1890ff;
    border-color: #1890ff;
    color: white;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #8c8c8c;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-layout {
        margin-left: 0;
    }

    .stats-row {
        grid-template-columns: 1fr;
    }

    .chart-grid {
        grid-template-columns: 1fr;
    }

    .subscription-grid {
        grid-template-columns: 1fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-grid-3col {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        justify-content: center;
    }
    
    .search-form-layout {
        flex-direction: column;
        gap: 12px;
    }
    
    .search-actions {
        flex-direction: row;
        justify-content: center;
        padding-top: 0;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .form-row-primary,
    .form-row-secondary {
        grid-template-columns: 1fr;
    }
    
    .search-actions {
        min-width: auto;
        width: 100%;
        justify-content: center;
        padding-top: 12px;
    }
}

/* 状态标签样式 */
.status-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5;
}

.status-active {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.status-inactive {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
}
