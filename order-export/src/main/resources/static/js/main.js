/**
 * 业务稽核系统 - 主JavaScript文件
 * Vue 3 + Composition API
 */

const { createApp, ref, reactive, computed } = Vue;

// 点击外部关闭指令
const clickOutsideDirective = {
    beforeMount(el, binding) {
        el.clickOutsideEvent = function(event) {
            if (!(el === event.target || el.contains(event.target))) {
                binding.value(event);
            }
        };
        document.addEventListener('click', el.clickOutsideEvent);
    },
    unmounted(el) {
        document.removeEventListener('click', el.clickOutsideEvent);
    }
};

const app = createApp({
    setup() {
        // ===== 响应式数据 =====
        // 可以从Thymeleaf模板获取服务器数据，或者从URL参数获取
        const getActiveTabFromUrl = () => {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('tab') || window.serverData?.activeTab || 'dashboard';
        };
        const activeTab = ref(getActiveTabFromUrl());
        const userRole = ref(window.serverData?.userRole || 'DEVELOPER'); // DEVELOPER, PRODUCT, OPERATION
        const userName = ref(window.serverData?.userName || '张三');
        const userAvatar = ref(window.serverData?.userAvatar || 'https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32');
        const showUserMenu = ref(false);
        
        // 计算属性
        const pageTitle = computed(() => {
            const titleMap = {
                'dashboard': '仪表盘',
                'business': '业务管理',
                'datasource': '数据源管理',
                'guard-items': '稽核项管理',
                'subscription': '我的订阅',
                'reports': '稽核报告',
                'logs': '操作日志'
            };
            return titleMap[activeTab.value] || '天网';
        });

        // ===== 统计数据 =====
        const stats = reactive({
            newAuditItems: 5,
            guardItemTotal: 60,
            issueTotal: 25,
            pendingIssues: 10
        });

        // ===== 模态框状态 =====
        const showBusinessModal = ref(false);
        const showDataSourceModal = ref(false);
        const showAuditItemModal = ref(false);
        const showDetailModal = ref(false);
        const showFeedbackModal = ref(false);
        const showViewDatasourceModal = ref(false);
        const isEditMode = ref(false);
        const testing = ref(false);
        const saving = ref(false);
        const feedbackSubmitting = ref(false);

        // ===== 异常稽核项数据 =====
        const abnormalItems = ref([
            {
                id: 1,
                name: '订单数据一致性检查',
                description: '检查订单状态与支付状态的一致性',
                business: '交易-订单',
                type: '一致性',
                abnormalValue: 125,
                executeTime: '2024-01-10 09:30',
                status: '异常'
            },
            {
                id: 2,
                name: '自动续费准确性验证',
                description: '验证自动续费功能的准确性',
                business: '交易-自动续费',
                type: '准确性',
                abnormalValue: 89,
                executeTime: '2024-01-10 09:15',
                status: '异常'
            }
        ]);

        // ===== 数据源相关 =====
        const datasourceFilter = reactive({
            id: '',
            name: '',
            type: '',
            connUrl: '',
            operator: '',
            status: ''
        });
        
        const datasourceForm = reactive({
            id: null,
            name: '',
            type: '',
            connUrl: '',
            description: '',
            config: '',
            status: 1
        });

        const datasources = ref([
            {
                id: 1,
                name: '订单库-主库',
                code: 'order_master',
                type: 'MySQL',
                connUrl: '******************************************************************************',
                description: '订单相关数据主库',
                status: 1,
                createUser: '张三',
                updateUser: '张三',
                createTime: '2024-01-08 09:00:00',
                updateTime: '2024-01-09 15:30:00'
            },
            {
                id: 2,
                name: '会员信息库',
                code: 'member_info',
                type: 'TiDB',
                connUrl: '******************************************************************************',
                description: '会员相关信息存储',
                status: 1,
                createUser: '李四',
                updateUser: '李四',
                createTime: '2024-01-07 14:20:00',
                updateTime: '2024-01-08 10:15:00'
            },
            {
                id: 3,
                name: '监控数据源',
                code: 'monitor_prometheus',
                type: 'Prometheus',
                connUrl: 'http://prometheus-server:9090/api/v1/query',
                description: '系统监控数据源',
                status: 0,
                createUser: '王五',
                updateUser: '赵六',
                createTime: '2024-01-06 11:30:00',
                updateTime: '2024-01-10 08:45:00'
            }
        ]);

        // ===== 业务管理相关 =====
        const businessFilter = reactive({
            name: '',
            department: '',
            status: ''
        });
        
        const businessForm = reactive({
            id: null,
            name: '',
            description: '',
            department: '',
            departmentEmails: '',
            status: 1
        });

        const businessList = ref([
            {
                id: 1,
                name: '交易-自动续费',
                description: '自动续费相关业务',
                department: 'VIP业务部',
                departmentEmails: '<EMAIL>,<EMAIL>',
                status: 1,
                createUser: '张三',
                createTime: '2024-01-01 10:00:00'
            },
            {
                id: 2,
                name: '交易-订单',
                description: '订单支付相关业务',
                department: 'VIP业务部',
                departmentEmails: '<EMAIL>,<EMAIL>',
                status: 1,
                createUser: '李四',
                createTime: '2024-01-02 14:30:00'
            },
            {
                id: 3,
                name: '会员信息',
                description: '会员信息管理业务',
                department: '会员运营部',
                departmentEmails: '<EMAIL>',
                status: 1,
                createUser: '王五',
                createTime: '2024-01-03 09:15:00'
            }
        ]);

        // ===== 分页相关 =====
        const currentPage = ref(1);
        const pageSize = ref(10);
        const totalItems = computed(() => businessList.value.length);
        const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

        // ===== 稽核项相关 =====
        const guardItemFilter = reactive({
            name: '',
            type: '',
            business: '',
            status: ''
        });

        const guardItemForm = reactive({
            id: null,
            name: '',
            type: '',
            datasourceId: null,
            businesses: [],
            description: '',
            checkSql: '',
            detailSql: ''
        });

        const guardItems = ref([
            {
                id: 1,
                name: '订单数据一致性检查',
                description: '检查订单状态与支付状态的一致性',
                type: '一致性',
                business: '交易-订单',
                datasource: '订单库-主库',
                lastExecuteTime: '2024-01-10 09:30',
                lastResult: 125,
                isAbnormal: true,
                status: 1,
                creator: '张三'
            },
            {
                id: 2,
                name: '自动续费准确性验证',
                description: '验证自动续费功能的准确性',
                type: '准确性',
                business: '交易-自动续费',
                datasource: '订单库-主库',
                lastExecuteTime: '2024-01-10 09:15',
                lastResult: 0,
                isAbnormal: false,
                status: 1,
                creator: '李四'
            }
        ]);

        const businesses = ref([
            { label: '交易-自动续费', value: 'trade-auto' },
            { label: '交易-订单', value: 'trade-order' },
            { label: '交易-收银台', value: 'trade-pay' },
            { label: '会员信息', value: 'member' },
            { label: '云包场', value: 'cloud-cinema' },
            { label: '联名会员', value: 'co-brand' },
            { label: '对外合作', value: 'cooperation' }
        ]);

        // ===== 订阅相关 =====
        const subscriptionSearch = ref('');
        
        // 批量订阅相关
        const showBatchSubscribeModal = ref(false);
        const showBatchUnsubscribeModal = ref(false);
        const batchSubscribeSearch = ref('');
        const batchSubscribeBusinessFilter = ref('');
        const batchSubscribeTypeFilter = ref('');
        const selectedBatchItems = ref([]);
        const selectedUnsubscribeItems = ref([]);
        const batchSelectAll = ref(false);
        const batchUnselectAll = ref(false);

        // ===== 意见反馈相关 =====
        const feedbackForm = reactive({
            type: '',
            title: '',
            description: '',
            contact: ''
        });
        const availableItems = ref([
            { id: 3, name: '支付数据完整性检查', business: '交易-收银台', type: '准确性' },
            { id: 4, name: '会员权益时效性验证', business: '会员信息', type: '时效性' },
            { id: 5, name: '云包场数据合理性校验', business: '云包场', type: '合理性' }
        ]);

        const subscribedItems = ref([
            {
                id: 1,
                name: '订单数据一致性检查',
                description: '检查订单状态与支付状态的一致性',
                business: '交易-订单',
                type: '一致性',
                subscribeTime: '2024-01-05'
            },
            {
                id: 2,
                name: '自动续费准确性验证',
                description: '验证自动续费功能的准确性',
                business: '交易-自动续费',
                type: '准确性',
                subscribeTime: '2024-01-03'
            }
        ]);

        const filteredAvailableItems = computed(() => {
            if (!subscriptionSearch.value) return availableItems.value;
            return availableItems.value.filter(item => 
                item.name.includes(subscriptionSearch.value) ||
                item.business.includes(subscriptionSearch.value)
            );
        });

        // 批量订阅筛选
        const filteredBatchItems = computed(() => {
            let items = availableItems.value;
            
            if (batchSubscribeSearch.value) {
                items = items.filter(item => 
                    item.name.includes(batchSubscribeSearch.value) ||
                    item.business.includes(batchSubscribeSearch.value)
                );
            }
            
            if (batchSubscribeBusinessFilter.value) {
                items = items.filter(item => item.business === batchSubscribeBusinessFilter.value);
            }
            
            if (batchSubscribeTypeFilter.value) {
                items = items.filter(item => item.type === batchSubscribeTypeFilter.value);
            }
            
            return items;
        });

        // ===== 报告相关 =====
        const reportFilter = reactive({
            business: '',
            type: '',
            startDate: '2024-01-03',
            endDate: '2024-01-10'
        });

        const reportTableData = ref([
            {
                name: '订单数据一致性检查',
                business: '交易-订单',
                type: '一致性',
                dayOverDayChange: -2.5,
                weekOverWeekChange: 5.8,
                '01-10': 125, '01-09': 0, '01-08': 5, '01-07': 0,
                '01-06': 0, '01-05': 23, '01-04': 0, '01-03': 0
            },
            {
                name: '自动续费准确性验证',
                business: '交易-自动续费',
                type: '准确性',
                dayOverDayChange: 0,
                weekOverWeekChange: -1.2,
                '01-10': 0, '01-09': 0, '01-08': 0, '01-07': 0,
                '01-06': 0, '01-05': 0, '01-04': 0, '01-03': 0
            },
            {
                name: '支付数据完整性检查',
                business: '交易-收银台',
                type: '准确性',
                dayOverDayChange: -100,
                weekOverWeekChange: -33.3,
                '01-10': 0, '01-09': 2, '01-08': 0, '01-07': 1,
                '01-06': 0, '01-05': 0, '01-04': 3, '01-03': 0
            },
            {
                name: '收银台响应时效验证',
                business: '交易-收银台',
                type: '时效性',
                dayOverDayChange: 0,
                weekOverWeekChange: 0,
                '01-10': 0, '01-09': 0, '01-08': 0, '01-07': 0,
                '01-06': 5, '01-05': 0, '01-04': 0, '01-03': 2
            },
            {
                name: '会员权益数据校验',
                business: '会员信息',
                type: '准确性',
                dayOverDayChange: 0,
                weekOverWeekChange: 0,
                '01-10': 0, '01-09': 0, '01-08': 0, '01-07': 0,
                '01-06': 0, '01-05': 0, '01-04': 0, '01-03': 0
            },
            {
                name: '会员等级一致性检查',
                business: '会员信息',
                type: '一致性',
                dayOverDayChange: 200,
                weekOverWeekChange: 50,
                '01-10': 3, '01-09': 0, '01-08': 0, '01-07': 1,
                '01-06': 0, '01-05': 2, '01-04': 0, '01-03': 0
            },
            {
                name: '云包场数据合理性校验',
                business: '云包场',
                type: '合理性',
                dayOverDayChange: 0,
                weekOverWeekChange: 0,
                '01-10': 0, '01-09': 0, '01-08': 0, '01-07': 0,
                '01-06': 0, '01-05': 0, '01-04': 0, '01-03': 0
            },
            {
                name: '订单字段完整性检查',
                business: '交易-订单',
                type: '完整性',
                dayOverDayChange: 100,
                weekOverWeekChange: -33.3,
                '01-10': 2, '01-09': 0, '01-08': 1, '01-07': 0,
                '01-06': 3, '01-05': 0, '01-04': 0, '01-03': 1
            },
            {
                name: '会员资料完整性验证',
                business: '会员信息',
                type: '完整性',
                dayOverDayChange: -100,
                weekOverWeekChange: -50,
                '01-10': 0, '01-09': 1, '01-08': 0, '01-07': 2,
                '01-06': 0, '01-05': 0, '01-04': 1, '01-03': 0
            }
        ]);

        const reportDates = ['01-10', '01-09', '01-08', '01-07', '01-06', '01-05', '01-04', '01-03'];

        // 按业务分组的报告数据
        const groupedReportData = computed(() => {
            const businessGroups = {};
            
            // 按业务分组
            reportTableData.value.forEach(item => {
                if (!businessGroups[item.business]) {
                    businessGroups[item.business] = [];
                }
                businessGroups[item.business].push(item);
            });
            
            // 返回按业务字母顺序排序的分组
            return businessGroups;
        });

        // ===== 明细查看 =====
        const detailData = reactive({
            guardItemName: '',
            date: '',
            columns: [],
            details: []
        });

        // ===== 数据源查看 =====
        const viewDatasourceData = reactive({
            id: null,
            name: '',
            type: '',
            connUrl: '',
            config: '',
            description: '',
            status: null,
            createTime: '',
            updateTime: '',
            createUser: '',
            updateUser: ''
        });

        // ===== 日志相关 =====
        const logFilter = reactive({
            type: '',
            operator: '',
            startDate: '',
            endDate: ''
        });

        const logs = ref([
            {
                id: 1,
                type: 'CREATE',
                resource: '数据源',
                resourceName: '订单库-主库',
                operator: '张三',
                operateTime: '2024-01-10 14:30:25',
                description: '新增MySQL类型数据源：订单库-主库'
            },
            {
                id: 2,
                type: 'UPDATE',
                resource: '稽核项',
                resourceName: '订单数据一致性检查',
                operator: '李四',
                operateTime: '2024-01-10 11:20:15',
                description: '修改稽核项配置，更新校验SQL'
            }
        ]);

        // ===== 业务方法 =====
        
        // 标签页切换
        const setActiveTab = (tab) => {
            activeTab.value = tab;
            // 更新URL参数
            const url = new URL(window.location);
            url.searchParams.set('tab', tab);
            window.history.pushState({}, '', url);
        };

        // ===== 业务管理相关方法 =====
        const searchBusiness = () => {
            // 模拟搜索业务
            console.log('搜索业务:', businessFilter);
        };

        const resetBusinessFilter = () => {
            Object.assign(businessFilter, {
                name: '',
                department: '',
                status: ''
            });
        };

        const editBusiness = (business) => {
            isEditMode.value = true;
            Object.assign(businessForm, business);
            showBusinessModal.value = true;
        };

        const deleteBusiness = (id) => {
            if (confirm('确定要删除这个业务吗？')) {
                const index = businessList.value.findIndex(item => item.id === id);
                if (index > -1) {
                    businessList.value.splice(index, 1);
                    alert('删除成功');
                }
            }
        };

        const saveBusiness = () => {
            if (!businessForm.name || !businessForm.department || !businessForm.departmentEmails) {
                alert('请填写必填字段');
                return;
            }

            if (isEditMode.value) {
                // 编辑模式
                const index = businessList.value.findIndex(item => item.id === businessForm.id);
                if (index > -1) {
                    businessList.value[index] = { ...businessForm };
                    alert('更新成功');
                }
            } else {
                // 新增模式
                const newBusiness = {
                    ...businessForm,
                    id: Date.now(), // 临时ID
                    createUser: userName.value,
                    createTime: new Date().toLocaleString()
                };
                businessList.value.unshift(newBusiness);
                alert('新增成功');
            }

            // 重置表单
            Object.assign(businessForm, {
                id: null,
                name: '',
                description: '',
                department: '',
                departmentEmails: '',
                status: 1
            });
            showBusinessModal.value = false;
        };

        const formatDate = (dateString) => {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        // ===== 用户菜单相关方法 =====
        const toggleUserMenu = () => {
            showUserMenu.value = !showUserMenu.value;
        };

        const closeUserMenu = () => {
            showUserMenu.value = false;
        };

        // 点击外部关闭菜单
        const handleClickOutside = (event) => {
            const userDropdown = event.target.closest('.user-dropdown');
            if (!userDropdown && showUserMenu.value) {
                closeUserMenu();
            }
        };

        // 添加全局点击事件监听器
        const addGlobalClickListener = () => {
            document.addEventListener('click', handleClickOutside);
        };

        const removeGlobalClickListener = () => {
            document.removeEventListener('click', handleClickOutside);
        };
        
        // 将函数暴露到全局作用域
        window.addGlobalClickListener = addGlobalClickListener;
        window.removeGlobalClickListener = removeGlobalClickListener;

        const logout = () => {
            if (confirm('确定要退出登录吗？')) {
                // 清理本地数据
                localStorage.clear();
                sessionStorage.clear();
                
                // 模拟退出登录，实际项目中应该调用后端API
                alert('已退出登录');
                
                // 跳转到登录页面
                window.location.href = '/login.html';
            }
            closeUserMenu();
        };

        const openFeedback = () => {
            showFeedbackModal.value = true;
            closeUserMenu();
            // 重置表单
            feedbackForm.type = '';
            feedbackForm.title = '';
            feedbackForm.description = '';
            feedbackForm.contact = '';
        };

        const submitFeedback = async () => {
            // 验证表单
            if (!feedbackForm.type || !feedbackForm.title || !feedbackForm.description) {
                alert('请填写必填项');
                return;
            }

            feedbackSubmitting.value = true;

            try {
                // 模拟提交反馈，实际项目中应该调用后端API
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                alert('感谢您的反馈，我们会认真处理！');
                showFeedbackModal.value = false;
                
                // 重置表单
                feedbackForm.type = '';
                feedbackForm.title = '';
                feedbackForm.description = '';
                feedbackForm.contact = '';
            } catch (error) {
                alert('提交失败，请稍后重试');
            } finally {
                feedbackSubmitting.value = false;
            }
        };

        // 获取类型标签样式
        const getTypeTagClass = (type) => {
            const typeMap = {
                '准确性': 'tag-success',
                '一致性': 'tag-primary',
                '时效性': 'tag-warning',
                '合理性': 'tag-info',
                '完整性': 'tag-danger'
            };
            return typeMap[type] || 'tag-info';
        };

        // 获取类型指示器样式
        const getTypeIndicatorClass = (type) => {
            const typeMap = {
                '准确性': 'type-accuracy',
                '一致性': 'type-consistency',
                '时效性': 'type-timeliness',
                '合理性': 'type-rationality',
                '完整性': 'type-completeness'
            };
            return typeMap[type] || 'type-default';
        };

        // 获取类型缩写
        const getTypeAbbreviation = (type) => {
            const typeMap = {
                '准确性': '准',
                '一致性': '致',
                '时效性': '时',
                '合理性': '理',
                '完整性': '整'
            };
            return typeMap[type] || '?';
        };

        // 格式化时间戳为 yyyy-MM-dd HH:mm:ss 格式
        const formatDateTime = (timestamp) => {
            if (!timestamp) {
                return '无';
            }
            
            // 如果是数字时间戳（毫秒）
            if (typeof timestamp === 'number') {
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }).replace(/\//g, '-');
            }
            
            // 如果是字符串时间戳
            if (typeof timestamp === 'string') {
                const numTimestamp = parseInt(timestamp);
                if (!isNaN(numTimestamp)) {
                    const date = new Date(numTimestamp);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    }).replace(/\//g, '-');
                }
            }
            
            // 如果已经是格式化的时间字符串，直接返回
            return timestamp;
        };

        // 查看详情
        const viewDetail = (item) => {
            detailData.guardItemName = item.name;
            detailData.date = item.executeTime.split(' ')[0];
            detailData.columns = ['订单号', '用户ID', '订单状态', '支付状态', '创建时间'];
            detailData.details = [
                { 订单号: 'ORDER123456', 用户ID: '1001', 订单状态: '已完成', 支付状态: '未支付', 创建时间: '2024-01-10 09:25' },
                { 订单号: 'ORDER123457', 用户ID: '1002', 订单状态: '已完成', 支付状态: '未支付', 创建时间: '2024-01-10 09:26' }
            ];
            showDetailModal.value = true;
        };

        // ===== 数据源管理方法 =====
        // 保存原始数据源数据
        const originalDatasources = ref([]);
        
        // 数据源类型列表
        const datasourceTypes = ref([]);
        
        // 获取数据源类型列表
        const loadDatasourceTypes = async () => {
            try {
                console.log('开始获取数据源类型...');
                const response = await fetch('/guard/datasource/supportTypes');
                console.log('API响应状态:', response.status);
                const result = await response.json();
                console.log('API响应数据:', result);
                
                if (result.code === 'A00000') {
                    // supportTypes接口使用createSuccessList，返回dataList字段
                    datasourceTypes.value = result.dataList || [];
                    console.log('成功获取数据源类型:', datasourceTypes.value);
                    console.log('datasourceTypes.value.length:', datasourceTypes.value.length);
                    // 延迟调用调试函数，确保Vue已更新
                    setTimeout(() => {
                        if (window.debugDatasourceTypes) {
                            window.debugDatasourceTypes();
                        }
                    }, 100);
                } else {
                    console.error('获取数据源类型失败:', result.msg);
                    // 使用默认类型
                    datasourceTypes.value = [
                        { value: 'MySQL', queryType: 'SQL', connUrlDemo: '************************************', connConfigDemo: '{"username":"root","password":"password"}' },
                        { value: 'TiDB', queryType: 'SQL', connUrlDemo: '**************************************', connConfigDemo: '{"username":"root","password":"password"}' },
                        { value: 'Hive', queryType: 'SQL', connUrlDemo: '***************************************', connConfigDemo: '{"username":"hive","password":"password"}' },
                        { value: 'Prometheus', queryType: 'HTTP', connUrlDemo: 'http://prometheus-server:9090/api/v1/query', connConfigDemo: '{"timeout":"30s"}' }
                    ];
                    console.log('使用默认数据源类型:', datasourceTypes.value);
                    console.log('默认数据源类型长度:', datasourceTypes.value.length);
                }
            } catch (error) {
                console.error('获取数据源类型失败:', error);
                // 使用默认类型
                datasourceTypes.value = [
                    { value: 'MySQL', queryType: 'SQL', connUrlDemo: '************************************', connConfigDemo: '{"username":"root","password":"password"}' },
                    { value: 'TiDB', queryType: 'SQL', connUrlDemo: '**************************************', connConfigDemo: '{"username":"root","password":"password"}' },
                    { value: 'Hive', queryType: 'SQL', connUrlDemo: '***************************************', connConfigDemo: '{"username":"hive","password":"password"}' },
                    { value: 'Prometheus', queryType: 'HTTP', connUrlDemo: 'http://prometheus-server:9090/api/v1/query', connConfigDemo: '{"timeout":"30s"}' }
                ];
                console.log('使用默认数据源类型:', datasourceTypes.value);
                console.log('默认数据源类型长度:', datasourceTypes.value.length);
            }
        };
        
        // 根据数据源类型清空连接URL和配置字段，让placeholder显示
        const updateDatasourceFormByType = (type) => {
            console.log('选择数据源类型:', type);
            console.log('可用的数据源类型:', datasourceTypes.value);
            
            // 清空字段，让placeholder显示示例内容
            datasourceForm.connUrl = '';
            datasourceForm.config = '';
            
            const typeInfo = datasourceTypes.value.find(t => t.value === type);
            if (typeInfo) {
                console.log('找到类型信息:', typeInfo);
                console.log('连接URL示例:', typeInfo.connUrlDemo);
                console.log('连接配置示例:', typeInfo.connConfigDemo);
            } else {
                console.log('未找到类型信息');
            }
        };
        
        // 获取连接URL占位符
        const getConnUrlPlaceholder = () => {
            const typeInfo = datasourceTypes.value.find(t => t.value === datasourceForm.type);
            return typeInfo ? `请输入${typeInfo.value}连接URL` : '请输入数据源连接URL';
        };
        
        // 获取连接URL示例
        const getConnUrlDemo = () => {
            const typeInfo = datasourceTypes.value.find(t => t.value === datasourceForm.type);
            const demo = typeInfo ? typeInfo.connUrlDemo : '';
            console.log('获取连接URL示例:', demo, 'for type:', datasourceForm.type);
            return demo;
        };
        
        // 获取连接配置占位符
        const getConnConfigPlaceholder = () => {
            const typeInfo = datasourceTypes.value.find(t => t.value === datasourceForm.type);
            return typeInfo ? `请输入${typeInfo.value}连接配置（JSON格式）` : '请输入JSON格式的连接配置';
        };
        
        // 获取连接配置示例
        const getConnConfigDemo = () => {
            const typeInfo = datasourceTypes.value.find(t => t.value === datasourceForm.type);
            const demo = typeInfo ? typeInfo.connConfigDemo : '';
            console.log('获取连接配置示例:', demo, 'for type:', datasourceForm.type);
            
            // 如果是对象，转换为JSON字符串；如果是字符串，直接返回
            if (typeof demo === 'object' && demo !== null) {
                return JSON.stringify(demo, null, 2);
            }
            return demo || '';
        };
        
        const searchDatasources = async () => {
            try {
                console.log('搜索条件:', datasourceFilter);
                
                // 确保数据源类型已加载（用于搜索表单的类型下拉框）
                if (datasourceTypes.value.length === 0) {
                    console.log('搜索时发现数据源类型为空，开始加载...');
                    await loadDatasourceTypes();
                }
                
                // 构建查询参数
                const params = new URLSearchParams();
                if (datasourceFilter.id) params.append('id', datasourceFilter.id);
                if (datasourceFilter.name) params.append('partName', datasourceFilter.name);
                if (datasourceFilter.type) params.append('type', datasourceFilter.type);
                if (datasourceFilter.connUrl) params.append('partConnUrl', datasourceFilter.connUrl);
                if (datasourceFilter.operator) params.append('operateUser', datasourceFilter.operator);
                if (datasourceFilter.status !== '') params.append('status', datasourceFilter.status);
                
                const response = await fetch(`/guard/datasource/search?${params.toString()}`);
                const result = await response.json();
                
                if (result.code === 'A00000') {
                    // search接口使用createSuccess，返回data字段，其中包含datasourceList
                    const datasourceList = result.data.datasourceList || [];
                    // 修复字段映射，确保connConfig正确映射到config
                    datasources.value = datasourceList.map(item => ({
                        ...item,
                        config: item.connConfig ? (typeof item.connConfig === 'object' ? JSON.stringify(item.connConfig, null, 2) : item.connConfig) : (item.config || '') // 将对象转换为JSON字符串
                    }));
                    console.log('搜索完成，找到', datasources.value.length, '条数据源');
                } else {
                    console.error('搜索失败:', result.msg);
                    alert('搜索失败: ' + result.msg);
                }
            } catch (error) {
                console.error('搜索数据源失败:', error);
                alert('搜索失败，请稍后重试');
            }
        };

        const resetDatasourceFilter = () => {
            Object.assign(datasourceFilter, {
                id: '',
                name: '',
                type: '',
                connUrl: '',
                operator: '',
                status: ''
            });
            // 从原始数据恢复
            if (originalDatasources.value.length > 0) {
                datasources.value = [...originalDatasources.value];
            } else {
                loadAllDatasources();
            }
        };

        const loadAllDatasources = async () => {
            try {
                const response = await fetch('/guard/datasource/search');
                const result = await response.json();
                
                if (result.code === 'A00000') {
                    // loadAllDatasources也使用search接口，返回data字段，其中包含datasourceList
                    const datasourceList = result.data.datasourceList || [];
                    // 修复字段映射，确保connConfig正确映射到config
                    datasources.value = datasourceList.map(item => ({
                        ...item,
                        config: item.connConfig ? (typeof item.connConfig === 'object' ? JSON.stringify(item.connConfig, null, 2) : item.connConfig) : (item.config || '') // 将对象转换为JSON字符串
                    }));
                } else {
                    console.error('加载数据源失败:', result.msg);
                    // 如果API失败，使用模拟数据
            datasources.value = [
                {
                    id: 1,
                    name: '订单库-主库',
                    code: 'order_master',
                    type: 'MySQL',
                    connUrl: '******************************************************************************',
                    description: '订单相关数据主库',
                    status: 1,
                    createUser: '张三',
                    updateUser: '张三',
                    createTime: '2024-01-08 09:00:00',
                    updateTime: '2024-01-09 15:30:00'
                },
                {
                    id: 2,
                    name: '会员信息库',
                    code: 'member_info',
                    type: 'TiDB',
                    connUrl: '******************************************************************************',
                    description: '会员相关信息存储',
                    status: 1,
                    createUser: '李四',
                    updateUser: '李四',
                    createTime: '2024-01-07 14:20:00',
                    updateTime: '2024-01-08 10:15:00'
                },
                {
                    id: 3,
                    name: '监控数据源',
                    code: 'monitor_prometheus',
                    type: 'Prometheus',
                    connUrl: 'http://prometheus-server:9090/api/v1/query',
                    description: '系统监控数据源',
                    status: 1,
                    createUser: '王五',
                    updateUser: '赵六',
                    createTime: '2024-01-06 11:30:00',
                    updateTime: '2024-01-10 08:45:00'
                }
            ];
                }
            } catch (error) {
                console.error('加载数据源失败:', error);
                // 如果网络错误，使用模拟数据
                datasources.value = [
                    {
                        id: 1,
                        name: '订单库-主库',
                        code: 'order_master',
                        type: 'MySQL',
                        connUrl: '******************************************************************************',
                        description: '订单相关数据主库',
                        status: 1,
                        createUser: '张三',
                        updateUser: '张三',
                        createTime: '2024-01-08 09:00:00',
                        updateTime: '2024-01-09 15:30:00'
                    }
                ];
            }
        };

        const viewDatasource = async (item) => {
            try {
                const response = await fetch(`/guard/datasource/getById?id=${item.id}`);
                const result = await response.json();
                
                if (result.code === 'A00000' && result.data) {
                    const datasource = result.data;
                    // 填充查看数据
                    Object.assign(viewDatasourceData, {
                        id: datasource.id,
                        name: datasource.name,
                        type: datasource.type,
                        connUrl: datasource.connUrl,
                        config: datasource.connConfig ? (typeof datasource.connConfig === 'object' ? JSON.stringify(datasource.connConfig, null, 2) : datasource.connConfig) : (datasource.config || ''), // 将对象转换为JSON字符串
                        description: datasource.description,
                        status: datasource.status,
                        createTime: datasource.createTime,
                        updateTime: datasource.updateTime,
                        createUser: datasource.createUser,
                        updateUser: datasource.updateUser
                    });
                    showViewDatasourceModal.value = true;
                } else {
                    alert('获取数据源详情失败: ' + (result.msg || '未知错误'));
                }
            } catch (error) {
                console.error('获取数据源详情失败:', error);
                alert('获取数据源详情失败，请稍后重试');
            }
        };

        const addDataSource = async () => {
            isEditMode.value = false;
            // 重置表单
            Object.assign(datasourceForm, {
                id: null,
                name: '',
                type: '',
                connUrl: '',
                description: '',
                config: '',
                status: 1
            });
            
            // 确保数据源类型已加载
            if (datasourceTypes.value.length === 0) {
                console.log('数据源类型为空，开始加载...');
                await loadDatasourceTypes();
            }
            
            showDataSourceModal.value = true;
        };

        const editDatasource = (item) => {
            isEditMode.value = true;
            // 直接使用item，因为loadAllDatasources和searchDatasources已经将connConfig映射到config
            Object.assign(datasourceForm, item);
            showDataSourceModal.value = true;
        };


        const testDatasourceConnection = () => {
            testing.value = true;
            setTimeout(() => {
                testing.value = false;
                alert('连接测试成功');
            }, 2000);
        };

        const saveDatasource = async () => {
            saving.value = true;
            try {
                const url = isEditMode.value ? '/guard/datasource/update' : '/guard/datasource/create';
                const method = isEditMode.value ? 'PUT' : 'POST';
                
                // 准备发送数据，将config字段映射为connConfig
                let connConfigValue = datasourceForm.config;
                
                // 如果config是字符串，尝试解析为JSON对象
                if (typeof connConfigValue === 'string') {
                    if (connConfigValue.trim()) {
                        try {
                            connConfigValue = JSON.parse(connConfigValue);
                        } catch (e) {
                            // 如果解析失败，保持原字符串
                            console.warn('连接配置不是有效的JSON格式:', connConfigValue);
                        }
                    } else {
                        // 如果为空字符串，设置为null
                        connConfigValue = null;
                    }
                }
                
                const requestData = {
                    ...datasourceForm,
                    connConfig: connConfigValue // 将config映射为connConfig
                };
                delete requestData.config; // 删除config字段，避免混淆
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                const result = await response.json();
                
                if (result.code === 'A00000') {
                    // 保存成功，刷新数据源列表
                    await loadAllDatasources();
                showDataSourceModal.value = false;
                alert('保存成功');
                } else {
                    alert('保存失败: ' + result.msg);
                }
            } catch (error) {
                console.error('保存数据源失败:', error);
                alert('保存失败，请稍后重试');
            } finally {
                saving.value = false;
            }
        };

        // ===== 稽核项管理方法 =====
        const searchAuditItems = () => {
            // 实际项目中在此处调用搜索API
        };

        const viewAuditDetail = (item) => {
            viewDetail(item);
        };

        const editAuditItem = (item) => {
            isEditMode.value = true;
            Object.assign(guardItemForm, item);
            showAuditItemModal.value = true;
        };

        const executeAuditItem = (item) => {
            alert('稽核项执行成功！');
        };

        const deleteAuditItem = (item) => {
            if (confirm('确定要删除这个稽核项吗？')) {
                alert('稽核项删除成功！');
            }
        };

        const saveAuditItem = () => {
            saving.value = true;
            setTimeout(() => {
                saving.value = false;
                showAuditItemModal.value = false;
                alert('保存成功');
            }, 1000);
        };

        // ===== 订阅管理方法 =====
        const subscribeItem = (item) => {
            subscribedItems.value.push({
                ...item,
                description: '稽核项描述',
                subscribeTime: new Date().toISOString().split('T')[0]
            });
            availableItems.value = availableItems.value.filter(i => i.id !== item.id);
            alert(`已订阅：${item.name}`);
        };

        const unsubscribeItem = (item) => {
            subscribedItems.value = subscribedItems.value.filter(i => i.id !== item.id);
            availableItems.value.push(item);
            alert(`已取消订阅：${item.name}`);
        };

        // ===== 批量订阅管理方法 =====
        const handleBatchSelectionChange = (event) => {
            const itemId = parseInt(event.target.value);
            if (event.target.checked) {
                if (!selectedBatchItems.value.includes(itemId)) {
                    selectedBatchItems.value.push(itemId);
                }
            } else {
                selectedBatchItems.value = selectedBatchItems.value.filter(id => id !== itemId);
            }
        };

        const handleBatchSelectAll = () => {
            if (batchSelectAll.value) {
                selectedBatchItems.value = [];
                batchSelectAll.value = false;
            } else {
                selectedBatchItems.value = filteredBatchItems.value.map(item => item.id);
                batchSelectAll.value = true;
            }
        };

        const clearBatchSelection = () => {
            selectedBatchItems.value = [];
            batchSelectAll.value = false;
        };

        const confirmBatchSubscribe = () => {
            const selectedItems = availableItems.value.filter(item => 
                selectedBatchItems.value.includes(item.id)
            );
            
            selectedItems.forEach(item => {
                subscribedItems.value.push({
                    ...item,
                    description: '稽核项描述',
                    subscribeTime: new Date().toISOString().split('T')[0]
                });
            });
            
            availableItems.value = availableItems.value.filter(item => 
                !selectedBatchItems.value.includes(item.id)
            );
            
            showBatchSubscribeModal.value = false;
            clearBatchSelection();
            
            alert(`成功订阅 ${selectedItems.length} 个稽核项！`);
        };

        const handleBatchUnsubscribeSelectionChange = (event) => {
            const itemId = parseInt(event.target.value);
            if (event.target.checked) {
                if (!selectedUnsubscribeItems.value.includes(itemId)) {
                    selectedUnsubscribeItems.value.push(itemId);
                }
            } else {
                selectedUnsubscribeItems.value = selectedUnsubscribeItems.value.filter(id => id !== itemId);
            }
        };

        const handleBatchUnselectAll = () => {
            if (batchUnselectAll.value) {
                selectedUnsubscribeItems.value = [];
                batchUnselectAll.value = false;
            } else {
                selectedUnsubscribeItems.value = subscribedItems.value.map(item => item.id);
                batchUnselectAll.value = true;
            }
        };

        const clearUnsubscribeSelection = () => {
            selectedUnsubscribeItems.value = [];
            batchUnselectAll.value = false;
        };

        const confirmBatchUnsubscribe = () => {
            const selectedItems = subscribedItems.value.filter(item => 
                selectedUnsubscribeItems.value.includes(item.id)
            );
            
            selectedItems.forEach(item => {
                const { subscribeTime, description, ...availableItem } = item;
                availableItems.value.push(availableItem);
            });
            
            subscribedItems.value = subscribedItems.value.filter(item => 
                !selectedUnsubscribeItems.value.includes(item.id)
            );
            
            showBatchUnsubscribeModal.value = false;
            clearUnsubscribeSelection();
            
            alert(`成功取消订阅 ${selectedItems.length} 个稽核项！`);
        };

        // ===== 报告管理方法 =====
        const loadReportData = () => {
            // 实际项目中在此处调用API加载报告数据
        };

        const viewReportDetail = (row, date) => {
            detailData.guardItemName = row.name;
            detailData.date = date;
            detailData.columns = ['订单号', '用户ID', '异常原因', '发生时间'];
            detailData.details = [
                { 订单号: 'ORDER123456', 用户ID: '1001', 异常原因: '订单状态与支付状态不一致', 发生时间: `${date} 09:25` },
                { 订单号: 'ORDER123457', 用户ID: '1002', 异常原因: '订单状态与支付状态不一致', 发生时间: `${date} 09:26` }
            ];
            showDetailModal.value = true;
        };

        // ===== 报告分组和统计方法 =====

        // ===== 日志管理方法 =====
        const searchLogs = () => {
            // 实际项目中在此处调用搜索API
        };

        const getLogTypeTagClass = (type) => {
            const typeMap = {
                'CREATE': 'tag-success',
                'UPDATE': 'tag-warning',
                'DELETE': 'tag-danger'
            };
            return typeMap[type] || 'tag-info';
        };

        const getLogTypeText = (type) => {
            const typeMap = {
                'CREATE': '新增',
                'UPDATE': '修改',
                'DELETE': '删除'
            };
            return typeMap[type] || type;
        };

        const viewLogDetail = (log) => {
            alert(`日志详情：${log.operation} - ${log.target}`);
        };


        // 调试函数：检查数据源类型状态
        const debugDatasourceTypes = () => {
            console.log('=== 数据源类型调试信息 ===');
            console.log('datasourceTypes.value:', datasourceTypes.value);
            console.log('datasourceTypes.value.length:', datasourceTypes.value.length);
            console.log('datasourceTypes是否为响应式:', datasourceTypes);
            console.log('========================');
        };

        // 将方法暴露到全局作用域
        window.loadAllDatasources = loadAllDatasources;
        window.loadDatasourceTypes = loadDatasourceTypes;
        window.debugDatasourceTypes = debugDatasourceTypes;

        // ===== 返回暴露的数据和方法 =====
        return {
            // 响应式数据
            activeTab,
            userRole,
            userName,
            userAvatar,
            pageTitle,
            showUserMenu,
            stats,
            
            // 模态框
            showBusinessModal,
            showDataSourceModal,
            showAuditItemModal,
            showDetailModal,
            showFeedbackModal,
            showViewDatasourceModal,
            isEditMode,
            testing,
            saving,
            feedbackSubmitting,
            
            // 批量订阅模态框
            showBatchSubscribeModal,
            showBatchUnsubscribeModal,
            
            // 数据
            abnormalItems,
            businessFilter,
            businessForm,
            businessList,
            currentPage,
            pageSize,
            totalItems,
            totalPages,
            datasourceFilter,
            datasourceForm,
            datasources,
            datasourceTypes,
            originalDatasources,
            guardItemFilter,
            guardItemForm,
            guardItems,
            businesses,
            subscriptionSearch,
            availableItems,
            subscribedItems,
            filteredAvailableItems,
            
            // 批量订阅数据
            batchSubscribeSearch,
            batchSubscribeBusinessFilter,
            batchSubscribeTypeFilter,
            selectedBatchItems,
            selectedUnsubscribeItems,
            batchSelectAll,
            batchUnselectAll,
            filteredBatchItems,
            
            // 意见反馈数据
            feedbackForm,
            
            reportFilter,
            reportTableData,
            reportDates,
            groupedReportData,
            detailData,
            viewDatasourceData,
            logFilter,
            logs,
            
            // 方法
            setActiveTab,
            searchBusiness,
            resetBusinessFilter,
            editBusiness,
            deleteBusiness,
            saveBusiness,
            formatDate,
            toggleUserMenu,
            closeUserMenu,
            handleClickOutside,
            logout,
            openFeedback,
            submitFeedback,
            getTypeTagClass,
            getTypeIndicatorClass,
            getTypeAbbreviation,
            formatDateTime,
            viewDetail,
            searchDatasources,
            resetDatasourceFilter,
            loadAllDatasources,
            loadDatasourceTypes,
            updateDatasourceFormByType,
            getConnUrlPlaceholder,
            getConnUrlDemo,
            getConnConfigPlaceholder,
            getConnConfigDemo,
            viewDatasource,
            addDataSource,
            editDatasource,
            testDatasourceConnection,
            saveDatasource,
            searchAuditItems,
            viewAuditDetail,
            editAuditItem,
            executeAuditItem,
            deleteAuditItem,
            saveAuditItem,
            subscribeItem,
            unsubscribeItem,
            
            // 批量订阅方法
            handleBatchSelectionChange,
            handleBatchSelectAll,
            clearBatchSelection,
            confirmBatchSubscribe,
            handleBatchUnsubscribeSelectionChange,
            handleBatchUnselectAll,
            clearUnsubscribeSelection,
            confirmBatchUnsubscribe,
            
            loadReportData,
            viewReportDetail,
            
            searchLogs,
            getLogTypeTagClass,
            getLogTypeText,
            viewLogDetail
        };
    }
});

// 注册指令
app.directive('click-outside', clickOutsideDirective);

// 初始化服务器数据
if (window.serverData) {
    // 可以在这里处理从服务器传递的数据
    console.log('服务器数据已加载:', window.serverData);
}

// 挂载应用
app.mount('#app');

// 立即检查并加载数据源类型（如果当前是数据源页面）
if (window.location.pathname.includes('/datasource')) {
    console.log('Vue应用挂载完成，立即加载数据源类型...');
    setTimeout(async () => {
        try {
            if (window.loadDatasourceTypes) {
                await window.loadDatasourceTypes();
            }
            if (window.loadAllDatasources) {
                await window.loadAllDatasources();
            }
        } catch (error) {
            console.error('Vue挂载后初始化失败:', error);
        }
    }, 200);
}

// 页面加载完成后初始化数据（移除重复调用，避免接口被请求两次）
document.addEventListener('DOMContentLoaded', async () => {
    console.log('页面加载完成，当前路径:', window.location.pathname);
    // 注释掉重复的调用，避免接口被请求两次
    // 数据加载已经在Vue应用挂载后处理
});

// 添加全局事件监听器
addGlobalClickListener();
