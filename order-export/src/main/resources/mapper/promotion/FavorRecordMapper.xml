<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorRecordDao">

    <select id="queryIdsByFavorIdAndFavorDataAndType" resultType="java.lang.Long">
    select id
    from ${tableName}
    where favor_data = #{favorData} and index_data_type = #{indexDataType}
    and `favor_id` = #{favorId} and order_code != #{excludedOrderCode}
    </select>

</mapper>