<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.promotion.FavorActivityDao">

    <select id="getFavorIdByActCode" resultType="java.lang.Long">
        select favor_id
        from promotion_favor_activity
        where status = 1 and act_code = #{actCode,jdbcType=VARCHAR}
    </select>

</mapper>