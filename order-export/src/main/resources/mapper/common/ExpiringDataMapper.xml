<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.ExpiringDataDao">

    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.ExpiringData">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="promotion" jdbcType="VARCHAR" property="promotion"/>
        <result column="act_name" jdbcType="VARCHAR" property="actName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, valid_start_time, valid_end_time, name, promotion, act_name
    </sql>

    <select id="queryExpiringData" resultMap="BaseResultMap">
    select *
    from ${expiringDataReq.tableName}
    <where>
       status = 1
        <choose>
            <when test="expiringDataReq.validEndTime != null and expiringDataReq.validEndTime !=''">
                and valid_start_time &lt; now()
                and valid_end_time > now()
                and valid_end_time &lt;= #{expiringDataReq.validEndTime}
            </when>
            <otherwise>
                and deadline > now()
                and deadline &lt;= #{expiringDataReq.deadline}
            </otherwise>
        </choose>
    </where>
    </select>

</mapper>