<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.coupon.CouponUsedDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.CouponUsed">
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="fee" jdbcType="INTEGER" property="fee"/>
        <result column="settlement_fee" jdbcType="INTEGER" property="settlementFee"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="tag" jdbcType="INTEGER" property="tag"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_code, user_id, batch_no, code, fee, settlement_fee, type, tag, create_time, update_time
    </sql>

    <select id="getCouponCodeByOrderCode" parameterType="java.lang.String" resultType="java.lang.String">
        select code
        from coupon_used
        where order_code = #{orderCode}
    </select>

    <select id="getOrderCodeByCouponCode" parameterType="java.lang.String" resultType="java.lang.String">
        select order_code
        from coupon_used
        where code = #{couponCode}
    </select>

</mapper>