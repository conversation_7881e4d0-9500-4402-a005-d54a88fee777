<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.autorenew.AutoRenewDutTypeDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.AutoRenewDutType">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dut_type" jdbcType="INTEGER" property="dutType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="source_vip_type" jdbcType="INTEGER" property="sourceVipType"/>
        <result column="vip_type" jdbcType="INTEGER" property="vipType"/>
        <result column="pay_channel" jdbcType="INTEGER" property="payChannel"/>
        <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName"/>
        <result column="pay_channel_type" jdbcType="INTEGER" property="payChannelType"/>
        <result column="dut_pay_type" jdbcType="INTEGER" property="dutPayType"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="cancel_autorenw_unbind" jdbcType="TINYINT" property="cancelAutorenwUnbind"/>
        <result column="change_amount" jdbcType="TINYINT" property="changeAmount"/>
        <result column="direct_cancel" jdbcType="TINYINT" property="directCancel"/>
        <result column="support_pure_sign" jdbcType="TINYINT" property="supportPureSign"/>
        <result column="priority" jdbcType="TINYINT" property="priority"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="valid_start_time" jdbcType="TIMESTAMP" property="validStartTime"/>
        <result column="valid_end_time" jdbcType="TIMESTAMP" property="validEndTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, dut_type, name, source_vip_type, vip_type, pay_channel, pay_channel_name, pay_channel_type, dut_pay_type,
        product_code, cancel_autorenw_unbind, change_amount, direct_cancel, support_pure_sign, priority, status, business_code, valid_start_time,
        valid_end_time
    </sql>

    <select id="selectValidEndTimeNotNullRecords" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from autorenew_dut_type
        where valid_end_time is not null and status = 1
    </select>

    <select id="getPayChannelByDutType" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select pay_channel from autorenew_dut_type where dut_type = #{dutType,jdbcType=INTEGER}
    </select>

    <select id="getVipTypeByDutType" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select vip_type from autorenew_dut_type where dut_type = #{dutType,jdbcType=INTEGER}
    </select>

</mapper>