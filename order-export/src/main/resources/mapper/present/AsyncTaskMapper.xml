<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.present.VipPresentAsyncTaskDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.repository.ClusterAsyncTask">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="class_name" jdbcType="VARCHAR" property="className"/>
        <result column="data" jdbcType="VARCHAR" property="data"/>
        <result column="exe_count" jdbcType="INTEGER" property="exeCount"/>
        <result column="in_queue" jdbcType="INTEGER" property="inQueue"/>
        <result column="pool_type" jdbcType="INTEGER" property="poolType"/>
        <result column="run_time" jdbcType="TIMESTAMP" property="runTime"/>
        <result column="segment" jdbcType="INTEGER" property="segment"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, class_name, data, exe_count, in_queue, pool_type, run_time, segment, deadline, create_time, update_time
    </sql>

    <select id="getAsyncTasks" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from async_task where run_time >= #{nSecondsAgo} and run_time &lt; #{now}
    </select>

</mapper>