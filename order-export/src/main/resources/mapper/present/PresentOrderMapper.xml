<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.present.VipPresentDao">
    <resultMap id="PresentOrderResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.PresentOrder">
        <result column="uid" jdbcType="BIGINT" property="uid"/>
        <result column="msg_id" jdbcType="VARCHAR" property="msgId"/>
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="present_order_code" jdbcType="VARCHAR" property="presentOrderCode"/>
        <result column="present_trade_code" jdbcType="VARCHAR" property="presentTradeCode"/>
        <result column="refund_order_code" jdbcType="VARCHAR" property="refundOrderCode"/>
        <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>
        <result column="present_type" jdbcType="VARCHAR" property="presentType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
    </resultMap>
    <resultMap id="PresentRecordResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.PresentRecord">
        <result column="buy_uid" jdbcType="VARCHAR" property="buyUid"/>
        <result column="receive_uid" jdbcType="VARCHAR" property="receiveUid"/>
        <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>
        <result column="present_type" jdbcType="VARCHAR" property="presentType"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Present_Order_Column_List">
        uid, msg_id, order_code, present_order_code, present_trade_code, refund_order_code,
        buy_type, present_type, `status`, pay_time, receive_time
    </sql>
    <sql id="Present_Record_Column_List">
        buy_uid, receive_uid, buy_type, present_type, create_time, update_time
    </sql>

    <select id="selectRecentPresentOrders" resultMap="PresentOrderResultMap">
        select <include refid="Present_Order_Column_List"/>
        from present_order_${tableNo}
        where pay_time >= #{startPayTime} and pay_time &lt; #{endPayTime}
    </select>

    <select id="selectRecentPresentRecords" resultMap="PresentRecordResultMap">
        select <include refid="Present_Record_Column_List"/>
        from present_record_${tableNo}
        where create_time >= #{startTime} and create_time &lt; #{endTime}
    </select>

</mapper>