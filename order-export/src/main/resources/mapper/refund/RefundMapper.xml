<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.refund.RefundDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.Refund">
        <result property="refundOrderCode" jdbcType="VARCHAR" column="refund_order_code"/>
        <result property="idempotentCode" jdbcType="VARCHAR" column="idempotent_code"/>
    </resultMap>

    <sql id="Base_Column_List">
        refund_order_code, idempotent_code
    </sql>

    <select id="getRefundOrderCodeByIdempotentCode" parameterType="java.lang.String" resultType="java.lang.String">
        select refund_order_code
        from boss_refund
        where idempotent_code = #{idempotentCode}
    </select>

</mapper>