<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.order.ProductDao">

    <cache size="10000" eviction="LRU" flushInterval="300000"/>

    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.Product">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="period" jdbcType="INTEGER" property="period"/>
        <result column="period_unit" jdbcType="INTEGER" property="periodUnit"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="deadline" jdbcType="TIMESTAMP" property="deadline"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="support_type" jdbcType="VARCHAR" property="supportType"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="sub_type" jdbcType="INTEGER" property="subType"/>
        <result column="source_sub_type" jdbcType="INTEGER" property="sourceSubType"/>
        <result column="service_type" jdbcType="INTEGER" property="serviceType"/>
        <result column="time_type" jdbcType="INTEGER" property="timeType"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="support_exp" jdbcType="INTEGER" property="supportExp"/>
        <result column="pay_page_desc" jdbcType="LONGVARCHAR" property="payPageDesc"/>
        <result column="h5_pay_page_desc" jdbcType="LONGVARCHAR" property="h5PayPageDesc"/>
        <result column="vip_type_code" jdbcType="VARCHAR" property="vipTypeCode"/>
        <result column="source_vip_type_code" jdbcType="VARCHAR" property="sourceVipTypeCode"/>
        <result column="business_code" jdbcType="VARCHAR" property="businessCode"/>
        <result column="charge_type" jdbcType="INTEGER" property="chargeType"/>
        <result column="original_price" jdbcType="INTEGER" property="originalPrice"/>
        <result column="is_has_gift" jdbcType="INTEGER" property="isHasGift"/>
        <result column="pre_paid" jdbcType="INTEGER" property="prePaid"/>
        <result column="currency_unit" jdbcType="VARCHAR" property="currencyUnit"/>
        <result column="currency_symbol" jdbcType="VARCHAR" property="currencySymbol"/>
        <result column="package_sub_type" jdbcType="INTEGER" property="packageSubType"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, name, price, period, period_unit, type, `status`, deadline, url, support_type,
    code, sub_type, source_sub_type, service_type, time_type, area, support_exp,pay_page_desc, h5_pay_page_desc, vip_type_code,
    source_vip_type_code, business_code, charge_type, original_price, is_has_gift, pre_paid, currency_unit, currency_symbol, package_sub_type
  </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap" useCache="true">
        select
        <include refid="Base_Column_List"/>
        from qiyue_product_new
        where id = #{id}
    </select>

    <select id="getByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from qiyue_product_new
        where code = #{code}
    </select>

</mapper>