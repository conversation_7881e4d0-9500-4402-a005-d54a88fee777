<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.order.PlatformDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.entity.Platform">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="corporation" jdbcType="VARCHAR" property="corporation"/>
        <result column="businessType" jdbcType="VARCHAR" property="businessType"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, description, code, update_time, type, area, corporation
    </sql>

    <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from qiyue_platform where id = #{id}
    </select>

    <select id="getByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from qiyue_platform where code = #{code}
    </select>

</mapper>