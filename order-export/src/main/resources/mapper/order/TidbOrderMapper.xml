<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.zeus.orderexport.persist.dao.order.TidbOrderDao">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.zeus.orderexport.model.OrderDto">
        <result column="order_code" jdbcType="VARCHAR" property="orderCode"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        order_code, status
    </sql>

    <select id="getByOrderCodeList" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orders
        where order_code in
        <foreach collection="list" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
    </select>

    <select id="getByOrderCode" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from orders
        where order_code = #{orderCode}
    </select>

</mapper>