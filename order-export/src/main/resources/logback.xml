<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="INFO_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/vip-xuanwu/info.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/vip-xuanwu/info.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>168</maxHistory>
            <!--保留7天的历史记录，但最多8GB-->
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_INFO_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="INFO_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/vip-xuanwu/error.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/vip-xuanwu/error.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>168</maxHistory>
            <!--保留7天的历史记录，但最多8GB-->
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="ERROR_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="DAILY_APP_ACCESS_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/vip-xuanwu/httptrace.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%d{yyyy-MM-dd HH:mm:ss}] [%tid] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/vip-xuanwu/httptrace.log.%d{yyyy-MM-dd}</fileNamePattern>
            <!-- keep 15 days' worth of history -->
            <maxHistory>7</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="APP_ACCESS_LOG_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>640</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="DAILY_APP_ACCESS_LOG_FILE"/>
    </appender>
    <logger name="APP_ACCESS_LOG" level="INFO" additivity="false">
        <appender-ref ref="APP_ACCESS_LOG_FILE"/>
    </logger>

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
<!--            <appender-ref ref="CONSOLE"/>-->
            <appender-ref ref="ASYNC_INFO_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

    <springProfile name="sg">
        <root level="INFO">
<!--            <appender-ref ref="CONSOLE"/>-->
            <appender-ref ref="ASYNC_INFO_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

</configuration>