<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稽核项管理 - 天网</title>
    
    <link rel="stylesheet" href="https://unpkg.com/ant-design-vue@4/dist/reset.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
    <div id="app">
        <div class="app-layout">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="logo-container">
                    <div class="logo-content">
                        <div class="logo-title">
                            <i class="fas fa-shield-alt"></i>
                            天网
                        </div>
                        <div class="logo-subtitle">TIAN WANG</div>
                    </div>
                </div>
                
                <div class="nav-menu">
                    <!-- 主要功能 -->
                    <div class="nav-section">
                        <div class="nav-section-title">主要功能</div>
                        <a href="/tianwang/dashboard" class="nav-item">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>仪表盘</span>
                        </a>
                    </div>

                    <!-- 个人中心 -->
                    <div class="nav-section">
                        <div class="nav-section-title">个人中心</div>
                        <a href="/tianwang/subscription" class="nav-item">
                            <i class="fas fa-bell"></i>
                            <span>我的订阅</span>
                        </a>
                        <a href="/tianwang/reports" class="nav-item">
                            <i class="fas fa-chart-line"></i>
                            <span>稽核报告</span>
                        </a>
                    </div>

                    <!-- 系统管理 - 仅研发人员可见 -->
                    <div class="nav-section" v-if="userRole === 'DEVELOPER'">
                        <div class="nav-section-title">系统管理</div>
                        <a href="/tianwang/business" class="nav-item">
                            <i class="fas fa-building"></i>
                            <span>业务管理</span>
                        </a>
                        <a href="/tianwang/datasource" class="nav-item">
                            <i class="fas fa-database"></i>
                            <span>数据源管理</span>
                        </a>
                        <a href="#" class="nav-item active">
                            <i class="fas fa-list-check"></i>
                            <span>稽核项管理</span>
                        </a>
                        <a href="/tianwang/logs" class="nav-item">
                            <i class="fas fa-history"></i>
                            <span>操作日志</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-layout">
                <!-- 头部 -->
                <div class="header">
                    <div class="header-left">
                        <h1 class="page-title">稽核项管理</h1>
                    </div>
                    <div class="header-right">
                        <div class="user-dropdown" v-click-outside="closeUserMenu">
                            <div class="user-info" @click="toggleUserMenu">
                                <img :src="userAvatar" :alt="userName" class="user-avatar">
                                <span class="user-name">{{ userName }}</span>
                                <i class="fas fa-chevron-down dropdown-arrow" :class="{ rotated: showUserMenu }"></i>
                            </div>
                            <div class="user-dropdown__menu" v-show="showUserMenu">
                                <div class="user-dropdown__item" @click="openFeedback">
                                    <i class="fas fa-comment"></i>
                                    意见反馈
                                </div>
                                <div class="user-dropdown__item" @click="logout">
                                    <i class="fas fa-sign-out-alt"></i>
                                    退出登录
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="content">
                    <div th:replace="fragments/guard-items"></div>
                </div>
            </div>
        </div>

        <!-- 模态框区域 -->
        <div th:replace="fragments/modals"></div>
    </div>

    <!-- JavaScript依赖 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- 服务器数据传递到客户端 -->
    <script th:inline="javascript">
        window.serverData = {
            userRole: /*[[${user?.role}]]*/ 'DEVELOPER',
            userName: /*[[${user?.name}]]*/ '张三',
            userAvatar: /*[[${user?.avatar}]]*/ 'https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32',
            datasources: /*[[${datasources}]]*/ [],
            guardItems: /*[[${guardItems}]]*/ [],
            reportData: /*[[${reportData}]]*/ []
        };
    </script>
    
    <!-- 自定义脚本 -->
    <script th:src="@{/js/main.js}"></script>
</body>
</html>
