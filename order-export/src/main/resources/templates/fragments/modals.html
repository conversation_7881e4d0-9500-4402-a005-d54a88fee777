<!-- 业务管理模态框 -->
<div class="modal" :class="{ show: showBusinessModal }">
    <div class="modal-content">
        <div class="modal-header">
            <div class="modal-title">{{ isEditMode ? '编辑业务' : '新增业务' }}</div>
            <button class="modal-close" @click="showBusinessModal = false">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-item">
                <label class="form-label">业务名称</label>
                <input type="text" class="form-control" v-model="businessForm.name">
            </div>
            <div class="form-item">
                <label class="form-label">业务描述</label>
                <textarea class="form-control" v-model="businessForm.description" rows="3"></textarea>
            </div>
            <div class="form-item">
                <label class="form-label">所属部门</label>
                <input type="text" class="form-control" v-model="businessForm.department">
            </div>
            <div class="form-item">
                <label class="form-label">部门邮件</label>
                <input type="text" class="form-control" v-model="businessForm.departmentEmails" 
                       placeholder="多个邮箱用逗号分隔">
            </div>
            <div class="form-item">
                <label class="form-label">状态</label>
                <select class="form-control" v-model="businessForm.status">
                    <option value="1">有效</option>
                    <option value="0">无效</option>
                </select>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn" @click="showBusinessModal = false">取消</button>
            <button class="btn btn-primary" @click="saveBusiness" :disabled="saving">
                <i class="fas fa-spinner fa-spin" v-if="saving"></i>
                {{ saving ? '保存中...' : '保存' }}
            </button>
        </div>
    </div>
</div>

<!-- 数据源模态框 -->
<div class="modal" :class="{ show: showDataSourceModal }">
    <div class="modal-content" style="width: 600px;">
        <div class="modal-header">
            <div class="modal-title">{{ isEditMode ? '编辑数据源' : '新增数据源' }}</div>
            <button class="modal-close" @click="showDataSourceModal = false">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-item">
                <label class="form-label">数据源名称</label>
                <input type="text" class="form-control" v-model="datasourceForm.name">
            </div>
            <div class="form-item">
                <label class="form-label">数据源类型</label>
                <select class="form-control" v-model="datasourceForm.type" @change="updateDatasourceFormByType(datasourceForm.type)">
                    <option value="">请选择类型</option>
                    <option v-for="type in datasourceTypes" :key="type.value" :value="type.value">
                        {{ type.value }}
                    </option>
                </select>
            </div>
            <div class="form-item">
                <label class="form-label">连接URL</label>
                <input type="text" class="form-control" v-model="datasourceForm.connUrl" 
                       :placeholder="getConnUrlDemo()">
            </div>
            <div class="form-item">
                <label class="form-label">连接配置</label>
                <textarea class="form-control" v-model="datasourceForm.config" rows="5" 
                          :placeholder="getConnConfigDemo()"></textarea>
            </div>
            <div class="form-item">
                <label class="form-label">描述</label>
                <textarea class="form-control" v-model="datasourceForm.description" rows="3"></textarea>
            </div>
            <div class="form-item">
                <label class="form-label">状态</label>
                <select class="form-control" v-model="datasourceForm.status">
                    <option value="1">有效</option>
                    <option value="0">无效</option>
                </select>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn" @click="showDataSourceModal = false">取消</button>
            <button class="btn btn-primary" @click="testDatasourceConnection" :disabled="testing">
                <span v-if="testing" class="loading"></span>
                {{ testing ? '测试中...' : '测试连接' }}
            </button>
            <button class="btn btn-primary" @click="saveDatasource" :disabled="saving">
                <i class="fas fa-spinner fa-spin" v-if="saving"></i>
                {{ saving ? '保存中...' : '保存' }}
            </button>
        </div>
    </div>
</div>

<!-- 稽核项模态框 -->
<div class="modal" :class="{ show: showAuditItemModal }">
    <div class="modal-content" style="width: 700px;">
        <div class="modal-header">
            <div class="modal-title">{{ isEditMode ? '编辑稽核项' : '新增稽核项' }}</div>
            <button class="modal-close" @click="showAuditItemModal = false">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="form-item">
                <label class="form-label">稽核项名称</label>
                <input type="text" class="form-control" v-model="guardItemForm.name">
            </div>
            <div class="form-item">
                <label class="form-label">稽核类型</label>
                <select class="form-control" v-model="guardItemForm.type">
                    <option value="">请选择类型</option>
                    <option value="准确性">准确性</option>
                    <option value="一致性">一致性</option>
                    <option value="时效性">时效性</option>
                    <option value="合理性">合理性</option>
                    <option value="完整性">完整性</option>
                </select>
            </div>
            <div class="form-item">
                <label class="form-label">业务线</label>
                <select class="form-control" v-model="guardItemForm.business">
                    <option value="">请选择业务线</option>
                    <option value="交易-订单">交易-订单</option>
                    <option value="交易-自动续费">交易-自动续费</option>
                    <option value="会员信息">会员信息</option>
                </select>
            </div>
            <div class="form-item">
                <label class="form-label">数据源</label>
                <select class="form-control" v-model="guardItemForm.datasource">
                    <option value="">请选择数据源</option>
                    <option value="订单库-主库">订单库-主库</option>
                    <option value="会员信息库">会员信息库</option>
                    <option value="监控数据源">监控数据源</option>
                </select>
            </div>
            <div class="form-item">
                <label class="form-label">校验规则SQL</label>
                <textarea class="form-control" v-model="guardItemForm.checkSql" rows="5" 
                          placeholder="请输入校验规则SQL"></textarea>
            </div>
            <div class="form-item">
                <label class="form-label">查看明细SQL</label>
                <textarea class="form-control" v-model="guardItemForm.detailSql" rows="5" 
                          placeholder="请输入查看明细SQL"></textarea>
            </div>
            <div class="form-item">
                <label class="form-label">描述</label>
                <textarea class="form-control" v-model="guardItemForm.description" rows="3"></textarea>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn" @click="showAuditItemModal = false">取消</button>
            <button class="btn btn-primary" @click="saveAuditItem" :disabled="saving">
                <i class="fas fa-spinner fa-spin" v-if="saving"></i>
                {{ saving ? '保存中...' : '保存' }}
            </button>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal" :class="{ show: showDetailModal }">
    <div class="modal-content" style="width: 800px;">
        <div class="modal-header">
            <div class="modal-title">稽核详情 - {{ detailData.guardItemName }}</div>
            <button class="modal-close" @click="showDetailModal = false">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="detail-info">
                <p><strong>稽核项：</strong>{{ detailData.guardItemName }}</p>
                <p><strong>日期：</strong>{{ detailData.date }}</p>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th v-for="column in detailData.columns" :key="column">{{ column }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(detail, index) in detailData.details" :key="index">
                            <td v-for="(value, key) in detail" :key="key">{{ value }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn" @click="showDetailModal = false">关闭</button>
        </div>
    </div>
</div>

<!-- 意见反馈模态框 -->
<div class="modal" :class="{ show: showFeedbackModal }">
    <div class="modal-content">
        <div class="modal-header">
            <div class="modal-title">意见反馈</div>
            <button class="modal-close" @click="showFeedbackModal = false">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form @submit.prevent="submitFeedback">
                <div class="form-item">
                    <label class="form-label">反馈类型</label>
                    <select class="form-control" v-model="feedbackForm.type" required>
                        <option value="">请选择反馈类型</option>
                        <option value="bug">Bug报告</option>
                        <option value="feature">功能建议</option>
                        <option value="improvement">改进建议</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-item">
                    <label class="form-label">反馈内容</label>
                    <textarea class="form-control" v-model="feedbackForm.content" rows="5" 
                              placeholder="请详细描述您的反馈内容" required></textarea>
                </div>
                <div class="form-item">
                    <label class="form-label">联系方式</label>
                    <input type="text" class="form-control" v-model="feedbackForm.contact" 
                           placeholder="请输入您的联系方式（可选）">
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button class="btn" @click="showFeedbackModal = false">取消</button>
            <button class="btn btn-primary" @click="submitFeedback" :disabled="feedbackSubmitting">
                <i class="fas fa-spinner fa-spin" v-if="feedbackSubmitting"></i>
                {{ feedbackSubmitting ? '提交中...' : '提交反馈' }}
            </button>
        </div>
    </div>
</div>

<!-- 数据源查看模态框 -->
<div class="modal" :class="{ show: showViewDatasourceModal }">
    <div class="modal-content" style="width: 600px;">
        <div class="modal-header">
            <div class="modal-title">数据源详情</div>
            <button class="modal-close" @click="showViewDatasourceModal = false">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="datasource-detail">
                <div class="detail-grid">
                    <div class="detail-item full-width">
                        <label class="detail-label">数据源名称</label>
                        <div class="detail-value">{{ viewDatasourceData.name || '无' }}</div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">数据源类型</label>
                        <div class="detail-value">
                            <span class="type-badge" :class="'type-' + (viewDatasourceData.type || '').toLowerCase()">
                                {{ viewDatasourceData.type || '无' }}
                            </span>
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">状态</label>
                        <div class="detail-value">
                            <span class="status-badge" :class="viewDatasourceData.status === 1 ? 'status-active' : 'status-inactive'">
                                {{ viewDatasourceData.status === 1 ? '有效' : '无效' }}
                            </span>
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">创建人</label>
                        <div class="detail-value">{{ viewDatasourceData.createUser || '无' }}</div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">更新人</label>
                        <div class="detail-value">{{ viewDatasourceData.updateUser || '无' }}</div>
                    </div>
                    <div class="detail-item full-width">
                        <label class="detail-label">连接URL</label>
                        <div class="detail-value code-block">{{ viewDatasourceData.connUrl || '无' }}</div>
                    </div>
                    <div class="detail-item full-width">
                        <label class="detail-label">连接配置</label>
                        <div class="detail-value code-block">{{ viewDatasourceData.config || '无' }}</div>
                    </div>
                    <div class="detail-item full-width" v-if="viewDatasourceData.description">
                        <label class="detail-label">描述</label>
                        <div class="detail-value">{{ viewDatasourceData.description }}</div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">创建时间</label>
                        <div class="detail-value">{{ formatDateTime(viewDatasourceData.createTime) }}</div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">更新时间</label>
                        <div class="detail-value">{{ formatDateTime(viewDatasourceData.updateTime) }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" @click="showViewDatasourceModal = false">关闭</button>
        </div>
    </div>
</div>
