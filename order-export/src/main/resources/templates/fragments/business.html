<!-- 业务管理 -->
<div v-if="userRole === 'DEVELOPER'">
    <div class="card">
        <div class="card-header">
            <div class="card-title">业务管理</div>
            <button class="btn btn-primary" @click="showBusinessModal = true; isEditMode = false">
                <i class="fas fa-plus"></i>
                新增业务
            </button>
        </div>
        <div class="card-body">
            <!-- 搜索表单 -->
            <div class="search-section">
                <div class="form-grid">
                    <div class="form-item">
                        <label class="form-label">业务名称</label>
                        <input type="text" class="form-control" v-model="businessFilter.name" placeholder="请输入业务名称">
                    </div>
                    <div class="form-item">
                        <label class="form-label">所属部门</label>
                        <input type="text" class="form-control" v-model="businessFilter.department" placeholder="请输入所属部门">
                    </div>
                    <div class="form-item">
                        <label class="form-label">状态</label>
                        <select class="form-control" v-model="businessFilter.status">
                            <option value="">全部状态</option>
                            <option value="1">有效</option>
                            <option value="0">无效</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <button class="btn btn-primary" @click="searchBusiness">搜索</button>
                        <button class="btn" @click="resetBusinessFilter" style="margin-left: 8px;">重置</button>
                    </div>
                </div>
            </div>

            <!-- 业务列表 -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>业务名称</th>
                            <th>业务描述</th>
                            <th>所属部门</th>
                            <th>部门邮件</th>
                            <th>状态</th>
                            <th>创建人</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in businessList" :key="item.id">
                            <td>{{ (currentPage - 1) * pageSize + index + 1 }}</td>
                            <td>{{ item.name }}</td>
                            <td>{{ item.description }}</td>
                            <td>{{ item.department }}</td>
                            <td>{{ item.departmentEmails }}</td>
                            <td>
                                <span :class="item.status ? 'tag tag-success' : 'tag tag-danger'">
                                    {{ item.status ? '有效' : '无效' }}
                                </span>
                            </td>
                            <td>{{ item.createUser }}</td>
                            <td>{{ item.createTime }}</td>
                            <td>
                                <button class="btn btn-sm" @click="editBusiness(item)">编辑</button>
                                <button class="btn btn-sm btn-danger" @click="deleteBusiness(item)">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <button class="btn" :disabled="currentPage === 1" @click="currentPage = 1">首页</button>
                <button class="btn" :disabled="currentPage === 1" @click="currentPage--">上一页</button>
                <span class="pagination-info">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
                <button class="btn" :disabled="currentPage === totalPages" @click="currentPage++">下一页</button>
                <button class="btn" :disabled="currentPage === totalPages" @click="currentPage = totalPages">末页</button>
            </div>
        </div>
    </div>
</div>
