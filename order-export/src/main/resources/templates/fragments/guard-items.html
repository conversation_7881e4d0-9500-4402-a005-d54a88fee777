<!-- 稽核项管理 -->
<div v-if="userRole === 'DEVELOPER'">
    <div class="card">
        <div class="card-header">
            <div class="card-title">稽核项管理</div>
            <button class="btn btn-primary" @click="showAuditItemModal = true; isEditMode = false">
                <i class="fas fa-plus"></i>
                新增稽核项
            </button>
        </div>
        <div class="card-body">
            <!-- 搜索表单 -->
            <div class="search-section">
                <div class="form-grid">
                    <div class="form-item">
                        <label class="form-label">稽核项名称</label>
                        <input type="text" class="form-control" v-model="guardItemFilter.name" placeholder="请输入稽核项名称">
                    </div>
                    <div class="form-item">
                        <label class="form-label">稽核类型</label>
                        <select class="form-control" v-model="guardItemFilter.type">
                            <option value="">全部类型</option>
                            <option value="准确性">准确性</option>
                            <option value="一致性">一致性</option>
                            <option value="时效性">时效性</option>
                            <option value="合理性">合理性</option>
                            <option value="完整性">完整性</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">业务线</label>
                        <select class="form-control" v-model="guardItemFilter.business">
                            <option value="">全部业务</option>
                            <option value="交易-订单">交易-订单</option>
                            <option value="交易-自动续费">交易-自动续费</option>
                            <option value="会员信息">会员信息</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <button class="btn btn-primary" @click="searchAuditItems">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 稽核项列表 -->
            <table class="table">
                <thead>
                    <tr>
                        <th>稽核项名称</th>
                        <th>类型</th>
                        <th>业务线</th>
                        <th>数据源</th>
                        <th>最近执行</th>
                        <th>最近结果</th>
                        <th>创建人</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in guardItems" :key="item.id">
                        <td>
                            <div class="item-name">{{ item.name }}</div>
                            <div class="item-desc">{{ item.description }}</div>
                        </td>
                        <td>
                            <span :class="'tag ' + getTypeTagClass(item.type)">
                                {{ getTypeAbbreviation(item.type) }}
                            </span>
                        </td>
                        <td>{{ item.business }}</td>
                        <td>{{ item.datasource }}</td>
                        <td>{{ item.lastExecute }}</td>
                        <td>
                            <span :class="'tag ' + (item.lastResult === '正常' ? 'tag-success' : 'tag-danger')">
                                {{ item.lastResult }}
                            </span>
                        </td>
                        <td>{{ item.creator }}</td>
                        <td>
                            <button class="btn btn-sm" @click="viewAuditDetail(item)">查看详情</button>
                            <button class="btn btn-sm" @click="editAuditItem(item)">编辑</button>
                            <button class="btn btn-sm" @click="executeAuditItem(item)">执行</button>
                            <button class="btn btn-sm btn-danger" @click="deleteAuditItem(item)">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
