<!-- 操作日志 -->
<div>
    <div class="card">
        <div class="card-header">
            <div class="card-title">操作日志</div>
        </div>
        <div class="card-body">
            <!-- 搜索表单 -->
            <div class="search-section">
                <div class="form-grid">
                    <div class="form-item">
                        <label class="form-label">操作类型</label>
                        <select class="form-control" v-model="logFilter.type">
                            <option value="">全部类型</option>
                            <option value="CREATE">新增</option>
                            <option value="UPDATE">修改</option>
                            <option value="DELETE">删除</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">操作人</label>
                        <input type="text" class="form-control" v-model="logFilter.operator" placeholder="请输入操作人">
                    </div>
                    <div class="form-item">
                        <label class="form-label">操作对象</label>
                        <input type="text" class="form-control" v-model="logFilter.target" placeholder="请输入操作对象">
                    </div>
                    <div class="form-item">
                        <button class="btn btn-primary" @click="searchLogs">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 日志列表 -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>操作类型</th>
                            <th>操作人</th>
                            <th>操作对象</th>
                            <th>操作描述</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="log in logs" :key="log.id">
                            <td>{{ log.time }}</td>
                            <td>
                                <span :class="'tag ' + getLogTypeTagClass(log.type)">
                                    {{ getLogTypeText(log.type) }}
                                </span>
                            </td>
                            <td>{{ log.operator }}</td>
                            <td>{{ log.target }}</td>
                            <td>{{ log.description }}</td>
                            <td>
                                <button class="btn btn-sm" @click="viewLogDetail(log)">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
