<!-- 数据源管理 -->
<div v-if="userRole === 'DEVELOPER'">
    <div class="card">
        <div class="card-header">
            <div class="card-title">数据源管理</div>
            <button class="btn btn-primary" @click="addDataSource">
                <i class="fas fa-plus"></i>
                新增数据源
            </button>
        </div>
        <div class="card-body">
            <!-- 搜索表单 -->
            <div class="search-section">
                <div class="search-form-layout">
                    <div class="search-fields">
                        <!-- 第一行：主要搜索字段 -->
                        <div class="form-row form-row-primary">
                            <div class="form-item form-item-wide">
                                <label class="form-label">数据源名称</label>
                                <input type="text" class="form-control" v-model="datasourceFilter.name" placeholder="请输入数据源名称">
                            </div>
                            <div class="form-item">
                                <label class="form-label">类型</label>
                                <select class="form-control" v-model="datasourceFilter.type">
                                    <option value="">全部类型</option>
                                    <option v-for="type in datasourceTypes" :key="type.value" :value="type.value">
                                        {{ type.value }}
                                    </option>
                                </select>
                            </div>
                            <div class="form-item">
                                <label class="form-label">状态</label>
                                <select class="form-control" v-model="datasourceFilter.status">
                                    <option value="">全部状态</option>
                                    <option value="1">有效</option>
                                    <option value="0">无效</option>
                                </select>
                            </div>
                        </div>
                        <!-- 第二行：辅助搜索字段 -->
                        <div class="form-row form-row-secondary">
                            <div class="form-item">
                                <label class="form-label">ID</label>
                                <input type="text" class="form-control" v-model="datasourceFilter.id" placeholder="请输入数据源ID">
                            </div>
                            <div class="form-item form-item-wide">
                                <label class="form-label">连接URL</label>
                                <input type="text" class="form-control" v-model="datasourceFilter.connUrl" placeholder="请输入连接URL关键词">
                            </div>
                            <div class="form-item">
                                <label class="form-label">操作人</label>
                                <input type="text" class="form-control" v-model="datasourceFilter.operator" placeholder="请输入创建人或更新人">
                            </div>
                        </div>
                    </div>
                    <div class="search-actions">
                        <button class="btn btn-primary" @click="searchDatasources">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <button class="btn btn-secondary" @click="resetDatasourceFilter">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据源列表 -->
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>连接URL</th>
                        <th>状态</th>
                        <th>创建人</th>
                        <th>更新人</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in datasources" :key="item.id">
                        <td>{{ item.id }}</td>
                        <td>{{ item.name }}</td>
                        <td><span class="tag tag-primary">{{ item.type }}</span></td>
                        <td>
                            <span class="url-text" :title="item.connUrl">{{ item.connUrl }}</span>
                        </td>
                        <td>
                            <span :class="['status-tag', item.status === 1 ? 'status-active' : 'status-inactive']">
                                {{ item.status === 1 ? '有效' : '无效' }}
                            </span>
                        </td>
                        <td>{{ item.createUser }}</td>
                        <td>{{ item.updateUser }}</td>
                        <td>{{ formatDateTime(item.createTime) }}</td>
                        <td>{{ formatDateTime(item.updateTime) }}</td>
                        <td>
                            <button class="btn btn-sm" @click="viewDatasource(item)">查看</button>
                            <button class="btn btn-sm" @click="editDatasource(item)">编辑</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
