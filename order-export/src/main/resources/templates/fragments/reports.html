<!-- 稽核报告 -->
<div>
    <div class="card">
        <div class="card-header">
            <div class="card-title">稽核报告</div>
            <div class="card-actions">
                <button class="btn btn-primary" @click="loadReportData">
                    <i class="fas fa-refresh"></i>
                    刷新数据
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 筛选条件 -->
            <div class="search-section">
                <div class="form-grid">
                    <div class="form-item">
                        <label class="form-label">业务线</label>
                        <select class="form-control" v-model="reportFilter.business">
                            <option value="">全部业务</option>
                            <option value="交易-订单">交易-订单</option>
                            <option value="交易-自动续费">交易-自动续费</option>
                            <option value="会员信息">会员信息</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label class="form-label">稽核类型</label>
                        <select class="form-control" v-model="reportFilter.type">
                            <option value="">全部类型</option>
                            <option value="准确性">准确性</option>
                            <option value="一致性">一致性</option>
                            <option value="时效性">时效性</option>
                            <option value="合理性">合理性</option>
                            <option value="完整性">完整性</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <button class="btn btn-primary" @click="loadReportData">查询</button>
                    </div>
                </div>
            </div>

            <!-- 报告表格 -->
            <div class="table-container">
                <table class="table report-table">
                    <thead>
                        <tr class="report-table-header">
                            <th>序号</th>
                            <th>稽核项</th>
                            <th v-for="date in reportDates" :key="date">{{ date }}</th>
                            <th>日环比</th>
                            <th>周同比</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template v-for="(group, businessName) in groupedReportData" :key="businessName">
                            <tr>
                                <td colspan="100%" class="business-group-header">
                                    <strong>{{ businessName }}</strong>
                                </td>
                            </tr>
                            <tr v-for="(item, index) in group" :key="item.id">
                            <td>{{ index + 1 }}</td>
                            <td>
                                <div class="item-name">
                                    {{ item.name }}
                                    <span :class="'type-badge type-' + getTypeIndicatorClass(item.type)">
                                        {{ getTypeAbbreviation(item.type) }}
                                    </span>
                                </div>
                            </td>
                            <td v-for="date in reportDates" :key="date" class="data-cell">
                                <span v-if="item[date] !== undefined" 
                                      :class="'trend-' + (item[date + 'Trend'] || 'normal')">
                                    {{ item[date] }}
                                </span>
                                <span v-else class="no-data">-</span>
                            </td>
                            <td class="trend-cell">
                                <span :class="'trend-' + (item.dayTrend || 'normal')">
                                    {{ item.dayTrendValue || '0%' }}
                                </span>
                            </td>
                            <td class="trend-cell">
                                <span :class="'trend-' + (item.weekTrend || 'normal')">
                                    {{ item.weekTrendValue || '0%' }}
                                </span>
                            </td>
                        </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
