<!-- 仪表盘 -->
<div>
    <div class="stats-row">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                    <i class="fas fa-plus-circle"></i>
                </div>
            </div>
            <div class="stat-number">{{ stats.newAuditItems }}</div>
            <div class="stat-label">新增稽核项</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                本周新增
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon" style="background: linear-gradient(135deg, #52c41a, #73d13d); color: white;">
                    <i class="fas fa-list-check"></i>
                </div>
            </div>
            <div class="stat-number">{{ stats.guardItemTotal }}</div>
            <div class="stat-label">稽核项总数</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-check-circle"></i>
                全部稽核项
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ff6b6b, #ff8e8e); color: white;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
            <div class="stat-number">{{ stats.issueTotal }}</div>
            <div class="stat-label">异常总数</div>
            <div class="stat-trend trend-up">
                <i class="fas fa-arrow-up"></i>
                需要关注
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon" style="background: linear-gradient(135deg, #ffa726, #ffb74d); color: white;">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
            <div class="stat-number">{{ stats.pendingIssues }}</div>
            <div class="stat-label">待处理</div>
            <div class="stat-trend trend-down">
                <i class="fas fa-arrow-down"></i>
                待处理问题
            </div>
        </div>
    </div>

    <!-- 异常稽核项列表 -->
    <div class="card">
        <div class="card-header">
            <div class="card-title">异常稽核项</div>
            <button class="btn btn-primary" @click="setActiveTab('reports')">
                查看全部
            </button>
        </div>
        <div class="card-body">
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>稽核项</th>
                            <th>业务线</th>
                            <th>类型</th>
                            <th>异常值</th>
                            <th>执行时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in abnormalItems" :key="item.id">
                            <td>
                                <div class="item-name">{{ item.name }}</div>
                                <div class="item-desc">{{ item.description }}</div>
                            </td>
                            <td>{{ item.business }}</td>
                            <td>
                                <span :class="'tag ' + getTypeTagClass(item.type)">
                                    {{ getTypeAbbreviation(item.type) }}
                                </span>
                            </td>
                            <td class="abnormal-value">{{ item.abnormalValue }}</td>
                            <td>{{ item.executeTime }}</td>
                            <td>
                                <span class="tag tag-danger">{{ item.status }}</span>
                            </td>
                            <td>
                                <button class="btn btn-sm" @click="viewDetail(item)">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
