<!-- 我的订阅 -->
<div>
    <div class="card">
        <div class="card-header">
            <div class="card-title">我的订阅</div>
            <div class="card-actions">
                <button class="btn btn-primary" @click="showBatchSubscribeModal = true">
                    <i class="fas fa-plus"></i>
                    批量订阅
                </button>
                <button class="btn" @click="showBatchUnsubscribeModal = true">
                    <i class="fas fa-minus"></i>
                    批量取消
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 搜索表单 -->
            <div class="search-section">
                <div class="form-grid">
                    <div class="form-item">
                        <label class="form-label">稽核项名称</label>
                        <input type="text" class="form-control" v-model="subscriptionSearch" placeholder="请输入稽核项名称">
                    </div>
                    <div class="form-item">
                        <button class="btn btn-primary" @click="searchSubscriptions">搜索</button>
                    </div>
                </div>
            </div>

            <!-- 已订阅列表 -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>稽核项名称</th>
                            <th>类型</th>
                            <th>业务线</th>
                            <th>订阅时间</th>
                            <th>最近执行</th>
                            <th>最近结果</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in subscribedItems" :key="item.id">
                            <td>
                                <div class="item-name">{{ item.name }}</div>
                                <div class="item-desc">{{ item.description }}</div>
                            </td>
                            <td>
                                <span :class="'tag ' + getTypeTagClass(item.type)">
                                    {{ getTypeAbbreviation(item.type) }}
                                </span>
                            </td>
                            <td>{{ item.business }}</td>
                            <td>{{ item.subscribeTime }}</td>
                            <td>{{ item.lastExecute }}</td>
                            <td>
                                <span :class="'tag ' + (item.lastResult === '正常' ? 'tag-success' : 'tag-danger')">
                                    {{ item.lastResult }}
                                </span>
                            </td>
                            <td>
                                <button class="btn btn-sm" @click="viewAuditDetail(item)">查看详情</button>
                                <button class="btn btn-sm btn-danger" @click="unsubscribeItem(item)">取消订阅</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
