<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue测试页面</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <h1>{{ message }}</h1>
        <p>当前时间: {{ currentTime }}</p>
    </div>

    <script>
        const { createApp, ref } = Vue;
        
        const app = createApp({
            setup() {
                const message = ref('Vue 3 测试页面');
                const currentTime = ref(new Date().toLocaleString());
                
                return {
                    message,
                    currentTime
                };
            }
        });
        
        app.mount('#app');
    </script>
</body>
</html>
