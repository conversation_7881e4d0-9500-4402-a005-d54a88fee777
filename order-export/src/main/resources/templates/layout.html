<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle != null ? pageTitle : '天网'}">天网</title>
    
    <link rel="stylesheet" href="https://unpkg.com/ant-design-vue@4/dist/reset.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
    <div id="app">
        <div class="app-layout">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="logo-container">
                    <div class="logo-content">
                        <div class="logo-title">
                            <i class="fas fa-shield-alt"></i>
                            天网
                        </div>
                        <div class="logo-subtitle">TIAN WANG</div>
                    </div>
                </div>
                
                <div class="nav-menu">
                    <!-- 主要功能 -->
                    <div class="nav-section">
                        <div class="nav-section-title">主要功能</div>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'dashboard' }"
                           @click.prevent="setActiveTab('dashboard')">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>仪表盘</span>
                        </a>
                    </div>

                    <!-- 个人中心 -->
                    <div class="nav-section">
                        <div class="nav-section-title">个人中心</div>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'subscription' }"
                           @click.prevent="setActiveTab('subscription')">
                            <i class="fas fa-bell"></i>
                            <span>我的订阅</span>
                        </a>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'reports' }"
                           @click.prevent="setActiveTab('reports')">
                            <i class="fas fa-chart-line"></i>
                            <span>稽核报告</span>
                        </a>
                    </div>

                    <!-- 系统管理 - 仅研发人员可见 -->
                    <div class="nav-section" v-if="userRole === 'DEVELOPER'">
                        <div class="nav-section-title">系统管理</div>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'business' }"
                           @click.prevent="setActiveTab('business')">
                            <i class="fas fa-building"></i>
                            <span>业务管理</span>
                        </a>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'datasource' }"
                           @click.prevent="setActiveTab('datasource')">
                            <i class="fas fa-database"></i>
                            <span>数据源管理</span>
                        </a>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'guard-items' }"
                           @click.prevent="setActiveTab('guard-items')">
                            <i class="fas fa-list-check"></i>
                            <span>稽核项管理</span>
                        </a>
                        <a href="#" class="nav-item" 
                           :class="{ active: activeTab === 'logs' }"
                           @click.prevent="setActiveTab('logs')">
                            <i class="fas fa-history"></i>
                            <span>操作日志</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-layout">
                <!-- 头部 -->
                <div class="header">
                    <div class="header-left">
                        <h1 class="page-title">{{ pageTitle }}</h1>
                    </div>
                    <div class="header-right">
                        <div class="user-dropdown" v-click-outside="closeUserMenu">
                            <img :src="userAvatar" :alt="userName" class="user-avatar" @click="toggleUserMenu">
                            <div class="user-dropdown__menu" v-show="showUserMenu">
                                <div class="user-dropdown__item" @click="openFeedback">
                                    <i class="fas fa-comment"></i>
                                    意见反馈
                                </div>
                                <div class="user-dropdown__item" @click="logout">
                                    <i class="fas fa-sign-out-alt"></i>
                                    退出登录
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="content">
                    <!-- 动态加载的内容区域 -->
                    <div th:replace="${contentTemplate}"></div>
                </div>
            </div>
        </div>

        <!-- 模态框区域 -->
        <div th:replace="${modalTemplate}"></div>
    </div>

    <!-- JavaScript依赖 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- 服务器数据传递到客户端 -->
    <script th:inline="javascript">
        window.serverData = {
            activeTab: /*[[${activeTab}]]*/ 'dashboard',
            userRole: /*[[${user?.role}]]*/ 'DEVELOPER',
            userName: /*[[${user?.name}]]*/ '张三',
            userAvatar: /*[[${user?.avatar}]]*/ 'https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32',
            datasources: /*[[${datasources}]]*/ [],
            guardItems: /*[[${guardItems}]]*/ [],
            reportData: /*[[${reportData}]]*/ []
        };
    </script>
    
    <!-- 自定义脚本 -->
    <script th:src="@{/js/main.js}"></script>
</body>
</html>
