<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单稽核监控项数据明细</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
        }

        /* 添加链接样式 */
        a {
            color: #0066cc;
            text-decoration: none !important;
        }

        a:hover {
            text-decoration: underline;
        }

        details {
            border: 1px solid #ddd;
            box-sizing: border-box;
            margin-bottom: 10px;
            /* Add some space below details */
            width: 100%;
        }

        details summary {
            display: block;
            padding: 10px;
            background-color: #f2f2f2;
            /* border: 1px solid #ddd; */
            cursor: pointer;
        }

        details summary::-webkit-details-marker {
            display: none;
        }

        details summary::before {
            content: "▶";
        }

        details[open] summary::before {
            content: "▼";
        }

        pre code {
            display: block;
            padding: 10px;
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <div style="max-width: 95%; margin: 0 auto;">
        <h4 th:text="${monitorName}">监控项名称</h4>
        <details>
            <summary>查看SQL语句</summary>
            <div>
                查询SQL:<pre><code class="language-sql" th:text="${querySql}"></code></pre>
            </div>
            <div>
                明细SQL:<pre><code class="language-sql" th:text="${detailSql}"></code></pre>
            </div>
        </details>
        <table>
            <thead>
                <tr>
                    <th>序号</th>
                    <th th:each="columnName : ${columnNames}" th:text="${columnName}" style="word-wrap: break-word;">
                    </th>
                </tr>
            </thead>
            <tbody th:each="rowData, rowDataStat : ${rowDataList}">
                <tr th:each="orderDetail, detailIndex : ${rowData.orderInfoList}">
                    <td th:if="${detailIndex.index == 0}" th:rowspan="${rowData.orderInfoList.size()}"
                        th:text="${rowDataStat.index + 1}" style="word-wrap: break-word;"></td>
                    <td th:if="${detailIndex.index == 0}" th:rowspan="${rowData.orderInfoList.size()}"
                        th:utext="${rowData.passportIdText}" style="word-wrap: break-word;"></td>
                    <td th:each="cell : ${orderDetail}" th:utext="${cell}" style="word-wrap: break-word;"></td>
                </tr>
            </tbody>
        </table>
    </div>

</body>

</html>