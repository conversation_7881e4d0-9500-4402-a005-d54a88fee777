package com.iqiyi.vip.zeus.orderexport.job;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2024/2/5 14:02
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class DatasourceSchemaSyncJobTest {

    @Resource
    private DatasourceSchemaSyncJob datasourceSchemaSyncJob;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void execute() {
        datasourceSchemaSyncJob.execute();
    }
}