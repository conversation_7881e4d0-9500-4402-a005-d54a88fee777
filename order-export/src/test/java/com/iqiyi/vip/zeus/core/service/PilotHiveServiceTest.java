package com.iqiyi.vip.zeus.core.service;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardMonitor;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

/**
 * @author: guojing
 * @date: 2025/5/27 18:37
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class PilotHiveServiceTest {

    @Resource
    private PilotHiveService pilotHiveService;
    @Resource
    private OrderGuardMonitorService orderGuardMonitorService;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void queryData() {
        OrderGuardMonitor orderGuardMonitor = orderGuardMonitorService.getById(39);
        Map<String, OrderGuardianQueryData> queryDataMap = pilotHiveService.queryData(orderGuardMonitor.getQuerySql(), CommonDateUtils.yesterdayStr(), CommonDateUtils.todayStr());
        System.out.println(JacksonUtils.toJsonString(queryDataMap));

        OrderGuardianDetailData detailData = pilotHiveService.queryDetailData(orderGuardMonitor.getDetailSql(), CommonDateUtils.yesterdayStr());
        System.out.println(detailData);
    }
}
