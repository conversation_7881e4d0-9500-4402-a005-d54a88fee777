package com.iqiyi.vip.zeus.core.service;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardMonitor;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.guard.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.core.service.guard.GuardDatasourceService;
import com.iqiyi.vip.zeus.core.service.guard.PilotQueryService;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

/**
 * @author: guojing
 * @date: 2025/9/12 14:42
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class PilotQueryServiceTest {

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
        System.setProperty("DBM_CONFIG_APPID","qpaas-db-viptrade-xuanwu-TEST");
        System.setProperty("apollo.paas-token","9ffa1cd1-6bbe-60ae-4526-27fbfb8e3adb");
    }

    @Resource
    private GuardDatasourceService guardDatasourceService;
    @Resource
    private OrderGuardMonitorService orderGuardMonitorService;
    @Resource
    private PilotQueryService pilotQueryService;

    @Test
    public void query() {
        //订单没有skuid的监控-升级
        int starRocksMonitorId = 26;
        //双连包-签约数(解约数)
        int clickHouseMonitorId = 51;
        //自动续费营销代金券赠品不是登记时发放
        int hiveMonitorId = 41;
        doQuery(starRocksMonitorId);
//        doQuery(clickHouseMonitorId);
//        doQuery(hiveMonitorId);
    }

    private void doQuery(Integer monitorId) {
        OrderGuardMonitor orderGuardMonitor = orderGuardMonitorService.getById(monitorId);
        Assert.assertNotNull(orderGuardMonitor);
        GuardDatasource datasource = guardDatasourceService.getById(orderGuardMonitor.getDatasourceId());
        Assert.assertNotNull(datasource);
        String todayStr = CommonDateUtils.todayStr();
        String nDaysAgo = CommonDateUtils.nDaysAgo(7);
        Map<String, OrderGuardianQueryData> result = pilotQueryService.queryData(datasource, orderGuardMonitor.getQuerySql(), nDaysAgo, todayStr);
        System.out.println("====== monitorName: " + orderGuardMonitor.getName() + ", result: " + JacksonUtils.toJsonString(result));
        OrderGuardianDetailData detailResult = pilotQueryService.queryDetailData(datasource, orderGuardMonitor.getDetailSql(), todayStr);
        System.out.println("====== monitorName: " + orderGuardMonitor.getName() + ", detailResult: " + JacksonUtils.toJsonString(detailResult));
    }

}
