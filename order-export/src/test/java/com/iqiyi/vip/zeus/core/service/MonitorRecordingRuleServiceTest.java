package com.iqiyi.vip.zeus.core.service;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.core.model.MonitorQueryMapping;
import com.iqiyi.vip.zeus.core.po.MonitorRecordingRulePO;
import com.iqiyi.vip.zeus.core.utils.JacksonUtils;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;

/**
 * @author: guojing
 * @date: 2024/4/11 20:59
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class MonitorRecordingRuleServiceTest {

    @Resource
    private MonitorRecordingRuleService monitorRecordingRuleService;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void batchGetByRecordNames() {
        String recordName = "vip_xuanwu:autorenew_renew_log_insert_total:rate1m";
        String recordName2 = "vip_xuanwu:autorenew_renew_log_insert_total:rate5m";
        MonitorRecordingRulePO monitorRecordingRulePO = new MonitorRecordingRulePO(2, 1, recordName2);
        monitorRecordingRuleService.batchInsert(Collections.singletonList(monitorRecordingRulePO));
        Map<String, List<MonitorQueryMapping>> recordNameToMonitorQueryMap = monitorRecordingRuleService.batchGetByRecordName(Collections.singletonList(recordName2));
        Assert.assertNotNull(recordNameToMonitorQueryMap);
        System.out.println(JacksonUtils.toJsonString(recordNameToMonitorQueryMap));
    }
}
