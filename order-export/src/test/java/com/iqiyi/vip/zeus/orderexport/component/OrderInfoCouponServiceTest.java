package com.iqiyi.vip.zeus.orderexport.component;

import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.component.impl.OrderCouponService;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * @Author: <PERSON>
 * @Date: 2022/2/24
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@RunWith(SpringRunner.class)
public class OrderInfoCouponServiceTest {

    static {
        System.setProperty("spring.profiles.active", "dev");
    }

    @Resource
    private OrderCouponService orderCouponService;

    @Test
    public void testIsCouponReused() {
        OrderDto order = new OrderDto();
        order.setCouponFee(10);
        order.setOrderCode("202201040540317504701");
        boolean isCouponReused = orderCouponService.isCouponReused(order, false);
        assertTrue(isCouponReused);
    }
}
