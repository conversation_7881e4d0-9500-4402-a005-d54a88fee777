package com.iqiyi.vip.zeus.orderexport.client;

import com.qiyi.vip.commons.component.dto.VipInfo;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * @author: guojing
 * @date: 2025/5/29 14:46
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class VipInfoClientTest {

    @Resource
    private VipInfoClient vipInfoClient;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("spring.profiles.active", "dev");
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void getUserMultiVipInfo() {
        Map<Integer, VipInfo> userMultiVipInfo = vipInfoClient.getUserMultiVipInfo(4457286294649216L, Arrays.asList(1L, 58L));
        System.out.println(userMultiVipInfo);
    }
}