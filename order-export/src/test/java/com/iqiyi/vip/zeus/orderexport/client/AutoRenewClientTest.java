package com.iqiyi.vip.zeus.orderexport.client;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.zeus.orderexport.client.resp.UserAgreementSimpleInfoResult;

/**
 * @author: guojing
 * @date: 2025/3/26 22:56
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class AutoRenewClientTest {

    @Resource
    private AutoRenewClient autoRenewClient;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("spring.profiles.active", "dev");
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void getUserAutoRenewAgreementList() {
        List<UserAgreementSimpleInfoResult> userAutoRenewAgreementList = autoRenewClient.getUserAutoRenewAgreementList(1669292066L);
        System.out.println(userAutoRenewAgreementList);
    }
}