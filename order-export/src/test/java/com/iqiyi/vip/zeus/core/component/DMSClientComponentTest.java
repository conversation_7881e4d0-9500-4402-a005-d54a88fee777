package com.iqiyi.vip.zeus.core.component;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;

import com.iqiyi.vip.zeus.core.model.GuardDatasource;
import com.iqiyi.vip.zeus.core.service.OrderGuardMonitorService;
import com.iqiyi.vip.zeus.core.service.guard.DMSQueryService;
import com.iqiyi.vip.zeus.core.service.guard.GuardDatasourceService;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;

/**
 * @author: guojing
 * @date: 2025/9/11 20:49
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class DMSClientComponentTest {

    @Resource
    private DMSClientComponent dmsClientComponent;
    @Resource
    private DMSQueryService dmsQueryService;
    @Resource
    private GuardDatasourceService guardDatasourceService;
    @Resource
    private OrderGuardMonitorService orderGuardMonitorService;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
        System.setProperty("DBM_CONFIG_APPID","qpaas-db-viptrade-xuanwu-TEST");
        System.setProperty("apollo.paas-token","9ffa1cd1-6bbe-60ae-4526-27fbfb8e3adb");
    }

    @Test
    public void query() {
        GuardDatasource datasource = guardDatasourceService.getByName("vip-xuanwu测试库");
        String sql = "select count(1) as cnt,count(distinct datasource_type) as type_cnt from order_guard_monitor;";
        List<LinkedHashMap<String, Object>> result = dmsClientComponent.queryDetail(datasource, sql);
        System.out.println(JacksonUtils.toJsonString(result));
    }
}
