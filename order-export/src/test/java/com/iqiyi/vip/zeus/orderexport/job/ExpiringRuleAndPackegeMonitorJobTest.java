package com.iqiyi.vip.zeus.orderexport.job;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.client.CommodityClient;
import com.iqiyi.vip.zeus.orderexport.component.UserService;

/**
 * <AUTHOR>
 * @date 2023/5/4 21:21
 */
@ActiveProfiles("dev")
@ExtendWith(SpringExtension.class)
@SpringBootTest
public class ExpiringRuleAndPackegeMonitorJobTest {

    @Resource
    private ExpiringRuleAndPackageMonitorJob expiringRuleAndPackageMonitorJob;
    @Resource
    private UserService userService;
    @Resource
    private CommodityClient commodityClient;

    @Test
    public void t() {
        expiringRuleAndPackageMonitorJob.execute();
//        expiringRuleAndPackageMonitorJob.commonTrigger(102L,"");

    }
}
