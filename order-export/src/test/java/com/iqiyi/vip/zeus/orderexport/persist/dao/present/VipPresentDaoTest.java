package com.iqiyi.vip.zeus.orderexport.persist.dao.present;

import com.iqiyi.vip.zeus.orderexport.entity.PresentOrder;
import com.iqiyi.vip.zeus.orderexport.entity.PresentRecord;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
@ActiveProfiles("dev")
@ExtendWith(SpringExtension.class)
@SpringBootTest
class VipPresentDaoTest {

    @Resource
    VipPresentDao vipPresentDao;

    @Test
    void selectRecentPresentOrders() {
        String tableNo = "00";
        String startPayTime = "2021-01-01 00:00:00";
        String endPayTime = "2021-01-20 05:02:00";
        List<PresentOrder> presentOrders = vipPresentDao.selectRecentPresentOrders(tableNo, startPayTime, endPayTime);
        assertNotNull(presentOrders);
        assertTrue(presentOrders.size() > 0);

        List<PresentRecord> presentRecords = vipPresentDao.selectRecentPresentRecords(tableNo, startPayTime, endPayTime);
        assertNotNull(presentRecords);
        assertTrue(presentRecords.size() > 0);
    }
}