package com.iqiyi.vip.zeus.orderexport.client;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.client.resp.AccountResponse;
import com.iqiyi.vip.zeus.orderexport.util.JacksonUtils;

/**
 * @author: guojing
 * @date: 2025/3/25 20:26
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class PayCenterDutAccountClientTest {

    @BeforeClass
    public static void setupClass() {
        System.setProperty("spring.profiles.active", "dev");
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Resource
    private PayCenterDutAccountClient payCenterDutAccountClient;

    @Test
    public void queryBindInfo() {
        AccountResponse accountResponse = payCenterDutAccountClient.queryBindInfo(1352347531880703L, 932);
        System.out.println(JacksonUtils.toJsonString(accountResponse));
    }
}