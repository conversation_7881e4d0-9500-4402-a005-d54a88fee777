package com.iqiyi.vip.zeus.core.service;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import com.iqiyi.vip.zeus.core.enums.DataSourceType;
import com.iqiyi.vip.zeus.core.model.MetricTemplate;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.orderexport.param.MetricTemplateCreateOrUpdateParam;

/**
 * @author: guojing
 * @date: 2024/1/27 10:42
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class MetricTemplateServiceTest {

    @Resource
    private MetricTemplateService metricTemplateService;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void createPrometheusMetricTemplate() {
        MetricTemplateCreateOrUpdateParam createParam = MetricTemplateCreateOrUpdateParam.builder()
            .name("当前数值")
            .datasourceType(DataSourceType.Prometheus.getValue())
            .content("sum(rate(${metric}{${labels}}[1m]))")
            .description("当前数值")
            .build();
        MetricTemplate createParamData = createParam.toMetricTemplate();
        createParamData.setCreateUser("zhouguojing");
        createParamData.setUpdateUser("zhouguojing");
        Integer metricTemplateId = metricTemplateService.create(createParamData);
        Assert.assertNotNull(metricTemplateId);
    }

    @Test
    public void createMySQLMetricTemplate() {
        MetricTemplateCreateOrUpdateParam tableCreateParam = MetricTemplateCreateOrUpdateParam.builder()
            .name("总数")
            .datasourceType(DataSourceType.MySQL.getValue())
            .content("SELECT count(1) as value\n"
                + "FROM ${table}\n"
                + "WHERE $__timeFilter(${timeColumn}) AND (${conditions})")
            .description("计算总数")
            .build();
        MetricTemplate tableCreateParamData = tableCreateParam.toMetricTemplate();
        tableCreateParamData.setCreateUser("zhouguojing");
        tableCreateParamData.setUpdateUser("zhouguojing");
        Integer tableMetricTemplateId = metricTemplateService.create(tableCreateParamData);
        Assert.assertNotNull(tableMetricTemplateId);


        MetricTemplateCreateOrUpdateParam timeSeriesCreateParam = MetricTemplateCreateOrUpdateParam.builder()
            .name("数量趋势-分组维度")
            .datasourceType(DataSourceType.MySQL.getValue())
            .content("SELECT $__timeGroupAlias(${timeColumn},'1s',0), \n"
                + "  ${groupBy} as metric, \n"
                + "  count(1) as value\n"
                + "FROM ${table}\n"
                + "WHERE $__timeFilter(${timeColumn}) AND (${conditions})\n"
                + "GROUP BY time,${groupBy}\n"
                + "ORDER BY time")
            .description("数量趋势-分组维度")
            .build();
        MetricTemplate timeSeriesCreateParamData = timeSeriesCreateParam.toMetricTemplate();
        timeSeriesCreateParamData.setCreateUser("zhouguojing");
        timeSeriesCreateParamData.setUpdateUser("zhouguojing");
        Integer timeSeriesMetricTemplateId = metricTemplateService.create(timeSeriesCreateParamData);
        Assert.assertNotNull(timeSeriesMetricTemplateId);
    }

    @Test
    public void test() {
        //create方法测试
        MetricTemplateCreateOrUpdateParam createParam = MetricTemplateCreateOrUpdateParam.builder()
            .name("总数")
            .datasourceType(DataSourceType.MySQL.getValue())
            .content("SELECT count(1) as value\n"
                + "FROM ${table}\n"
                + "WHERE $__timeFilter(${timeColumn}) AND (${conditions})")
            .description("计算总数")
            .build();
        MetricTemplate createParamData = createParam.toMetricTemplate();
        createParamData.setCreateUser("zhouguojing");
        createParamData.setUpdateUser("zhouguojing");
        Integer metricTemplateId = metricTemplateService.create(createParamData);
        Assert.assertNotNull(metricTemplateId);


        //getById方法测试
        MetricTemplate metricTemplateInDB = metricTemplateService.getById(metricTemplateId);
        Assert.assertNotNull(metricTemplateInDB);
        Assert.assertEquals(createParam.getName(), metricTemplateInDB.getName());
        Assert.assertEquals(createParam.getContent(), metricTemplateInDB.getContent());


        //update方法测试
        MetricTemplateCreateOrUpdateParam updateParam = MetricTemplateCreateOrUpdateParam.builder()
            .id(metricTemplateId)
            .name("总数-update")
            .datasourceType(DataSourceType.MySQL.getValue())
            .content("SELECT count(1) as value\n"
                + "FROM ${table}\n"
                + "WHERE $__timeFilter(${timeColumn})")
            .description("计算总数")
            .build();
        MetricTemplate updateParamData = updateParam.toMetricTemplate();
        boolean updated = metricTemplateService.update(updateParamData);
        Assert.assertTrue(updated);
        MetricTemplate updatedMetricTemplateInDB = metricTemplateService.getById(metricTemplateId);
        Assert.assertNotNull(updatedMetricTemplateInDB);
        Assert.assertEquals(metricTemplateInDB.getName(), updatedMetricTemplateInDB.getName());
        Assert.assertEquals(metricTemplateInDB.getContent(), updatedMetricTemplateInDB.getContent());


        //getByName方法测试
        MetricTemplate metricTemplateByName = metricTemplateService.getByName(updateParam.getName());
        Assert.assertNotNull(metricTemplateByName);
        Assert.assertEquals(updateParamData.getName(), metricTemplateByName.getName());


        //listAll方法测试
        List<MetricTemplate> allMetricTemplates = metricTemplateService.listAll();
        Assert.assertNotNull(allMetricTemplates);


        //delete方法测试
        boolean deleted = metricTemplateService.delete(updateParam.getId());
        Assert.assertTrue(deleted);
    }
}
