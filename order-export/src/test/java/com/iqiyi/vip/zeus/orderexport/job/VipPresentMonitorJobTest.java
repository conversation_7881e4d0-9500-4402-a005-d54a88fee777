package com.iqiyi.vip.zeus.orderexport.job;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
@ActiveProfiles("dev")
@ExtendWith(SpringExtension.class)
@SpringBootTest
class VipPresentMonitorJobTest {

    @Resource
    VipPresentMonitorJob presentMonitorJob;

    @Test
    void execute() throws Exception {
//        ReturnT<String> result = presentMonitorJob.execute(vipJobResp, null);
//        assertTrue(result.isSuccess());
//
//        Thread.sleep(10000);
//        ReturnT<String> result2 = presentMonitorJob.execute(vipJobResp, "5");
////        Thread.sleep(120 * 1000);
////        ReturnT<String> result3 = presentMonitorJob.execute(vipJobResp, null);
//        Thread.sleep(55 * 1000);
//        ReturnT<String> result4 = presentMonitorJob.execute(vipJobResp, null);
    }
}