package com.iqiyi.vip.zeus.orderexport.metric;

import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.orderexport.model.OrderDto;
import com.iqiyi.vip.zeus.orderexport.mysqlio.CanalEvent;

/**
 * @Author: <PERSON>
 * @Date: 2023/4/25
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class SelfTestMetrics {
    @Value("${intl.flag:false}")
    private Boolean isIntlEnv;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void selfTestIllegalUpdateTime() {
        CanalEvent<OrderDto> testedEvent = new CanalEvent<>();
        testedEvent.setEventType("UPDATE");
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        testedEvent.setTimestamp(String.valueOf(timestamp.getTime()));

        LocalDateTime localDateTime = timestamp.toLocalDateTime();
        LocalDateTime orderUpdateTime = localDateTime.minusMinutes(10);
        ZonedDateTime zonedDateTime = orderUpdateTime.atZone(ZoneId.systemDefault());
        OrderDto beforeOrder = OrderDto.builder().orderCode("xuanwu-selfTest-orderCode").updateTime(Timestamp.from(zonedDateTime.toInstant())).build();
        OrderDto afterOrder = OrderDto.builder().orderCode("xuanwu-selfTest-orderCode").updateTime(Timestamp.from(zonedDateTime.toInstant())).build();
        testedEvent.setRowBefore(beforeOrder);
        testedEvent.setRowAfter(afterOrder);

        if (!"UPDATE".equals(testedEvent.getEventType())) {
            System.out.println("false");
        }
        OrderDto before = testedEvent.getRowBefore();
        OrderDto after = testedEvent.getRowAfter();
        Long beforeUpdateTimeLong = before.getUpdateTime().getTime();
        Long afterUpdateTimeLong = after.getUpdateTime().getTime();
        Long messageTimeStampLong = Long.parseLong(String.valueOf(testedEvent.getTimestamp()));
        int minutes = (int) ((messageTimeStampLong - afterUpdateTimeLong) / (1000 * 60));
        if (beforeUpdateTimeLong.equals(afterUpdateTimeLong) && minutes >= 5) {
            System.out.println("true");
        }
        System.out.println("false");
    }

    @Test
    public void testIntlFlag() {
        Assert.assertEquals(false, isIntlEnv);
    }
}
