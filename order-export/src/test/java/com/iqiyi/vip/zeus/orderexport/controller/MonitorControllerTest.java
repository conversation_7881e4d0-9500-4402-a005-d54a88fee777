package com.iqiyi.vip.zeus.orderexport.controller;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.Collections;

import com.iqiyi.vip.zeus.core.context.RequestContext;
import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.MonitorCategory;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQueryCondition;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.orderexport.param.MonitorCreateOrUpdateParam;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2024/1/29 10:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class MonitorControllerTest {

    @Resource
    private MonitorController monitorController;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;

    private ZeusDatasource datasource;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Before
    public void before() {
        RequestContextHolder.setRequestContext(new RequestContext(newCurrentUser("zhouguojing")));
        datasource = zeusDatasourceService.getByName("Prometheus");
    }

    @Test
    public void create() {
        String dashboardUid = "b667b031-bd9d-4586-9438-7cb5627402c6";
        ZeusMonitorQueryCondition applicationCondition = new ZeusMonitorQueryCondition("application", "=", Arrays.asList("vip-xuanwu"));
        ZeusMonitorQueryCondition agreementTypeCondition = new ZeusMonitorQueryCondition("agreementType", "=", Arrays.asList("1"));
        ZeusMonitorQueryCondition operatorTypeCondition = new ZeusMonitorQueryCondition("operatorType", "=", Arrays.asList("1"));
        ZeusMonitorQuery monitorQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(1)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量")
            .build();
        MonitorCreateOrUpdateParam createParam = MonitorCreateOrUpdateParam.builder()
            .name("宙斯-自动续费签约量-单测")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(datasource.getId())
            .query(Collections.singletonList(monitorQuery))
            .dashboardUid(dashboardUid)
            .build();
        BaseResponse<Integer> createResponse = monitorController.create(createParam);
        Assert.assertTrue(createResponse.success());
        System.out.println("monitor create success, id: " + createResponse.getData());
    }

    @Test
    public void update() {
        String dashboardUid = "b667b031-bd9d-4586-9438-7cb5627402c6";
        ZeusMonitorQueryCondition applicationCondition = new ZeusMonitorQueryCondition("application", "=", Arrays.asList("vip-xuanwu"));
        ZeusMonitorQueryCondition agreementTypeCondition = new ZeusMonitorQueryCondition("agreementType", "=", Arrays.asList("1"));
        ZeusMonitorQueryCondition operatorTypeCondition = new ZeusMonitorQueryCondition("operatorType", "=", Arrays.asList("1"));
        ZeusMonitorQuery monitorQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(1)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量")
            .build();
        ZeusMonitorQuery monitorQuery2 = ZeusMonitorQuery.builder()
            .id(2)
            .metricTmpId(2)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量-昨天")
            .build();
        MonitorCreateOrUpdateParam updateParam = MonitorCreateOrUpdateParam.builder()
            .id(9)
            .name("宙斯-自动续费签约量-单测")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(datasource.getId())
            .query(Arrays.asList(monitorQuery, monitorQuery2))
            .dashboardUid(dashboardUid)
            .panelId(8)
            .build();
        BaseResponse<Boolean> updateResponse = monitorController.update(updateParam);
        Assert.assertTrue(updateResponse.success());
        Assert.assertTrue(updateResponse.getData());
    }

    private AuthorityUser newCurrentUser(String oaAccount) {
        AuthorityTeamBasic realTeam = new AuthorityTeamBasic();
        realTeam.setTeamCode("2363");
        AuthorityUser currentUser = new AuthorityUser();
        currentUser.setOaAccount(oaAccount);
        currentUser.setRealTeam(realTeam);
        return currentUser;
    }

}