package com.iqiyi.vip.zeus.core.service;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardMonitor;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianDetailData;
import com.iqiyi.vip.zeus.core.model.orderguardian.OrderGuardianQueryData;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;
import com.iqiyi.vip.zeus.utils.CommonDateUtils;

/**
 * @author: guojing
 * @date: 2025/5/26 16:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class PrometheusQueryServiceTest {

    @Resource
    private PrometheusQueryService prometheusQueryService;
    @Resource
    private OrderGuardMonitorService orderGuardMonitorService;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void test() {
        OrderGuardMonitor orderGuardMonitor = orderGuardMonitorService.getById(2);
        Map<String, OrderGuardianQueryData> resultMap = prometheusQueryService.queryData(orderGuardMonitor.getQuerySql(), CommonDateUtils.nDaysAgo(7), CommonDateUtils.yesterdayStr());
        System.out.println(JsonUtils.toJsonString(resultMap));
    }

    @Test
    public void detailQueryTest() {
        OrderGuardMonitor orderGuardMonitor = orderGuardMonitorService.getById(37);
        Map<String, OrderGuardianQueryData> resultMap = prometheusQueryService.queryData(orderGuardMonitor.getQuerySql(), CommonDateUtils.nDaysAgo(7), CommonDateUtils.yesterdayStr());
        System.out.println(JsonUtils.toJsonString(resultMap));
        OrderGuardianDetailData detailData = prometheusQueryService.queryDetailData(orderGuardMonitor.getDetailSql(), CommonDateUtils.todayStr());
        System.out.println(JsonUtils.toJsonString(detailData));
    }

}
