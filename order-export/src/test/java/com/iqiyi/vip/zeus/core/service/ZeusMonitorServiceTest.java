package com.iqiyi.vip.zeus.core.service;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.Arrays;

import com.iqiyi.vip.zeus.core.context.RequestContext;
import com.iqiyi.vip.zeus.core.context.RequestContextHolder;
import com.iqiyi.vip.zeus.core.enums.MonitorCategory;
import com.iqiyi.vip.zeus.core.model.DashboardDisplayInfo;
import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.model.ZeusMonitor;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQuery;
import com.iqiyi.vip.zeus.core.model.ZeusMonitorQueryCondition;
import com.iqiyi.vip.zeus.core.req.DashboardCreateParam;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityTeamBasic;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;

/**
 * @author: guojing
 * @date: 2023/12/25 10:32
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class ZeusMonitorServiceTest {

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Resource
    private ZeusMonitorService zeusMonitorService;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;
    @Resource
    private EagleDashboardService dashboardService;

    @Test
    public void queryMonitor() {
        ZeusMonitor zeusMonitor = zeusMonitorService.getById(9);
        Assert.assertNotNull(zeusMonitor);
    }

    @Test
    public void createDashboard() {
        AuthorityTeamBasic teamBasic = new AuthorityTeamBasic();
        teamBasic.setCnName("宙斯-2");
        AuthorityUser authorityUser = new AuthorityUser();
        authorityUser.setRealTeam(teamBasic);
        DashboardCreateParam createParam = new DashboardCreateParam();
        createParam.setTitle("Dashboard-222");
        createParam.setMessage("创建Dashboard-222");
        RequestContextHolder.setRequestContext(new RequestContext(authorityUser));
        DashboardDisplayInfo response = dashboardService.create(createParam);
        Assert.assertNotNull(response);
        System.out.println(response.getUid());
    }

    @Test
    public void monitorTest() {
        String dashboardUid = "e48ce5e8-76b5-4c53-8c22-b9a5cf981df2";
        DashboardWithMeta dashboardWithMeta = dashboardService.getByUid(dashboardUid);
        ZeusDatasource prometheusDatasource = zeusDatasourceService.getById(1);

        ZeusMonitorQueryCondition applicationCondition = new ZeusMonitorQueryCondition("application", "=", Arrays.asList("vip-xuanwu"));
        ZeusMonitorQueryCondition agreementTypeCondition = new ZeusMonitorQueryCondition("agreementType", "=", Arrays.asList("1"));
        ZeusMonitorQueryCondition operatorTypeCondition = new ZeusMonitorQueryCondition("operatorType", "=", Arrays.asList("1"));
        ZeusMonitorQuery monitorQuery1 = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(1)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量")
            .build();
        ZeusMonitorQuery monitorQuery2 = ZeusMonitorQuery.builder()
            .id(2)
            .metricTmpId(2)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量-昨天")
            .build();
        ZeusMonitor createParam = ZeusMonitor.builder()
            .name("宙斯-自动续费签约量")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(prometheusDatasource.getId())
            .query(Arrays.asList(monitorQuery1, monitorQuery2))
            .teamCode("2363")
            .createUser("zhouguojing")
            .updateUser("zhouguojing")
            .dashboardUid(dashboardUid)
            .build();
        Integer monitorId = zeusMonitorService.create(createParam, prometheusDatasource, dashboardWithMeta);
        Assert.assertNotNull(monitorId);


        ZeusMonitor updateParam = ZeusMonitor.builder()
            .id(monitorId)
            .name("宙斯-自动续费签约量")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(prometheusDatasource.getId())
            .query(Arrays.asList(monitorQuery1))
            .teamCode("2363")
            .createUser("zhouguojing")
            .updateUser("zhouguojing")
            .dashboardUid(dashboardUid)
            .build();
        ZeusMonitor monitorFromDB = zeusMonitorService.getById(updateParam.getId());
        boolean updated = zeusMonitorService.update(updateParam, monitorFromDB, dashboardWithMeta);
        Assert.assertTrue(updated);
    }

    @Test
    public void updateTest() {
        String dashboardUid = "e48ce5e8-76b5-4c53-8c22-b9a5cf981df2";
        ZeusDatasource prometheusDatasource = zeusDatasourceService.getByName("Prometheus");

        ZeusMonitorQueryCondition applicationCondition = new ZeusMonitorQueryCondition("application", "=", Arrays.asList("vip-xuanwu"));
        ZeusMonitorQueryCondition agreementTypeCondition = new ZeusMonitorQueryCondition("agreementType", "=", Arrays.asList("1"));
        ZeusMonitorQueryCondition operatorTypeCondition = new ZeusMonitorQueryCondition("operatorType", "=", Arrays.asList("1"));
        ZeusMonitorQuery monitorQuery1 = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(1)
            .source("autorenew_set_log_insert_total")
            .conditions(Arrays.asList(applicationCondition, agreementTypeCondition, operatorTypeCondition))
            .displayName("签约量")
            .build();

        DashboardWithMeta dashboardWithMeta = dashboardService.getByUid(dashboardUid);
        ZeusMonitor updateParam = ZeusMonitor.builder()
            .id(6)
            .name("宙斯-自动续费签约量")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(prometheusDatasource.getId())
            .query(Arrays.asList(monitorQuery1))
            .teamCode("2363")
            .createUser("zhouguojing")
            .updateUser("zhouguojing")
            .dashboardUid(dashboardUid)
            .build();
        ZeusMonitor monitorFromDB = zeusMonitorService.getById(updateParam.getId());
        boolean updated = zeusMonitorService.update(updateParam, monitorFromDB, dashboardWithMeta);
        Assert.assertTrue(updated);
    }

    @Test
    public void mysqlMonitor() {
        String dashboardUid = "e48ce5e8-76b5-4c53-8c22-b9a5cf981df2";
        ZeusDatasource mysqlDatasource = zeusDatasourceService.getByName("MySQL-Refund");
        DashboardWithMeta dashboardWithMeta = dashboardService.getByUid(dashboardUid);

        ZeusMonitorQueryCondition statusCondition = new ZeusMonitorQueryCondition("status", "=", Arrays.asList("1"));
        ZeusMonitorQueryCondition refundSourceCondition = new ZeusMonitorQueryCondition("refund_source", "=", Arrays.asList("0"));
        ZeusMonitorQuery timeSeriesMonitorQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(4)
            .source("boss_refund")
            .conditions(Arrays.asList(statusCondition, refundSourceCondition))
            .timeFilter("update_time")
            .groupBy("refund_person")
            .build();
        ZeusMonitor timeSeriesMonitorParam = ZeusMonitor.builder()
            .name("宙斯-退款数-发起人分类-时间序列")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(mysqlDatasource.getId())
            .query(Arrays.asList(timeSeriesMonitorQuery))
            .teamCode("2363")
            .createUser("zhouguojing")
            .updateUser("zhouguojing")
            .dashboardUid(dashboardUid)
            .build();
        Integer timeSeriesMonitorId = zeusMonitorService.create(timeSeriesMonitorParam, mysqlDatasource, dashboardWithMeta);
        Assert.assertNotNull(timeSeriesMonitorId);


        ZeusMonitorQuery statMonitorQuery = ZeusMonitorQuery.builder()
            .id(1)
            .metricTmpId(3)
            .source("boss_refund")
            .conditions(Arrays.asList(statusCondition, refundSourceCondition))
            .timeFilter("update_time")
            .build();
        ZeusMonitor tableMonitorParam = ZeusMonitor.builder()
            .name("宙斯-退款数-发起人分类-总数")
            .category(MonitorCategory.DATA_TREND.getValue())
            .datasourceId(mysqlDatasource.getId())
            .query(Arrays.asList(statMonitorQuery))
            .teamCode("2363")
            .createUser("zhouguojing")
            .updateUser("zhouguojing")
            .dashboardUid(dashboardUid)
            .build();
        Integer statMonitorId = zeusMonitorService.create(tableMonitorParam, mysqlDatasource, dashboardWithMeta);
        Assert.assertNotNull(statMonitorId);
    }
}
