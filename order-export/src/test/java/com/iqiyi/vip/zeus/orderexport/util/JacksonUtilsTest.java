package com.iqiyi.vip.zeus.orderexport.util;

import org.junit.jupiter.api.Test;

import java.util.Map;

/**
 * Created at: 2021-09-27
 *
 * <AUTHOR>
 */
class JacksonUtilsTest {

    @Test
    void parseMap() {
        String jsonStr = "{\"presentVipRequest\":{\"actCode\":\"vip_present\",\"aid\":\"7357966509075200\",\"amount\":1,\"buyPid\":\"9c0b7c568db863ea\",\"buyPidAmt\":1,\"buyPidType\":0,\"chargeType\":1,\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"originalOrderCode\":\"202109271959148093786\",\"payType\":410,\"pid\":\"9c0b7c568db863ea\",\"platform\":\"bb136ff4276771f3\",\"tradeCode\":\"202109271959148093786_7357966509075200\",\"uid\":2132464806},\"presentOrder\":{\"buyType\":\"single\",\"createTime\":1632743962269,\"deadlineEndTime\":1632923962000,\"deadlineStartTime\":1632743962000,\"fv\":\"zh2767610fb39d41c6c1b25f5caac039\",\"id\":5918567,\"msgId\":\"0A6E5DBC0E9931215E8B8A2F66755292\",\"orderCode\":\"202109271959148093786\",\"orderType\":0,\"payTime\":1632743962000,\"presentConditionId\":73,\"presentConfigId\":106,\"presentPerformId\":1,\"presentTradeCode\":\"202109271959148093786_7357966509075200\",\"presentType\":\"single\",\"productAmount\":1,\"receiveTime\":1632743962269,\"status\":1,\"uid\":2132464806,\"updateTime\":1632743962269}}";
        Map<String, Object> resultMap = JacksonUtils.parseMap(jsonStr);
        System.out.println(resultMap);
    }
}