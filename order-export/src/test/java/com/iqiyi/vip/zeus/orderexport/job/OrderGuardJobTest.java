package com.iqiyi.vip.zeus.orderexport.job;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;

/**
 * @author: guojing
 * @date: 2025/5/27 19:37
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class OrderGuardJobTest {

    @Resource
    private OrderGuardJob orderGuardJob;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
        System.setProperty("DBM_CONFIG_APPID","qpaas-db-viptrade-xuanwu-TEST");
        System.setProperty("apollo.paas-token","9ffa1cd1-6bbe-60ae-4526-27fbfb8e3adb");
    }

    @Test
    public void execute() throws Exception {
        orderGuardJob.execute();
    }
}