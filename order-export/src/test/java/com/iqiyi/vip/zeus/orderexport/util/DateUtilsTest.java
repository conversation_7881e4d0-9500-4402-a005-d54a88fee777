package com.iqiyi.vip.zeus.orderexport.util;

import com.iqiyi.vip.zeus.utils.CommonDateUtils;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Created at: 2021-02-07
 *
 * <AUTHOR>
 */
class DateUtilsTest {

    @Test
    void stringToTimestamp() {
        String dateTime = "2021-02-07 15:50:23";
        Timestamp timestamp = DateUtils.stringToTimestamp(dateTime);
        String newDateTime = DateUtils.formatTimestamp(timestamp);
        assertEquals(dateTime, newDateTime);
    }

    @Test
    void minusMinutes() {
        String dateTime = "2021-02-07 15:50:23";
        Timestamp timestamp = DateUtils.stringToTimestamp(dateTime);
        Timestamp fiveMinutesAgo = DateUtils.minusMinutes(timestamp, 5);
        String newDateTime = DateUtils.formatTimestamp(fiveMinutesAgo, DateUtils.DATE_MINUTE_PATTERN);
        assertEquals("2021-02-07 15:45", newDateTime);
    }

    @Test
    void getDayList() {
        System.out.println(DateUtils.getDateList(8, CommonDateUtils.yesterdayStr()));
        System.out.println(DateUtils.getDateList(8, CommonDateUtils.todayStr()));
    }

    @Test
    void aaa() {
        long diffInHours = Duration.between(DateUtils.stringToTimestamp("2025-05-29 20:00:00").toInstant(), DateUtils.stringToTimestamp("2025-05-28 20:00:00").toInstant()).toHours();
        System.out.println(diffInHours);
    }
}