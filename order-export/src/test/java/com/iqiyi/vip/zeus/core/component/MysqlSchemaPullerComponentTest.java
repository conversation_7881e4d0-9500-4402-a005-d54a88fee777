package com.iqiyi.vip.zeus.core.component;

import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;

import com.iqiyi.vip.zeus.core.model.ZeusDatasource;
import com.iqiyi.vip.zeus.core.service.ZeusDatasourceService;
import com.iqiyi.vip.zeus.orderexport.OrderExportApplication;

/**
 * @author: guojing
 * @date: 2024/2/5 15:48
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderExportApplication.class)
@AutoConfigureMetrics
@ActiveProfiles("dev")
public class MysqlSchemaPullerComponentTest {

    @Resource
    private MysqlSchemaPullerComponent mysqlSchemaPullerComponent;
    @Resource
    private ZeusDatasourceService zeusDatasourceService;

    @BeforeClass
    public static void setupClass() {
        System.setProperty("app.id", "vip-xuanwu");
        System.setProperty("env", "dev");
    }

    @Test
    public void test() {
        ZeusDatasource prometheus = zeusDatasourceService.getByName("MySQL-Refund");
        mysqlSchemaPullerComponent.pull(Collections.singletonList(prometheus));
    }

}
