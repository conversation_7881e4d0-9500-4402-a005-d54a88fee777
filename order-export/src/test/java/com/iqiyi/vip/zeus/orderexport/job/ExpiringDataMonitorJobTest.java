package com.iqiyi.vip.zeus.orderexport.job;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/**
 * @Author: <PERSON>
 * @Date: 2023/3/10
 */
@ActiveProfiles("dev")
@ExtendWith(SpringExtension.class)
@SpringBootTest
public class ExpiringDataMonitorJobTest {
    @Resource
    private ExpiringDataMonitorJob expiringDataMonitorJob;

    @Test
    void testExecute() {
        expiringDataMonitorJob.execute();
    }
}
