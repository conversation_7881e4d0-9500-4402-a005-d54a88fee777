<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.6</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.iqiyi.trade</groupId>
    <artifactId>order-monitor</artifactId>
    <version>2.62</version>
    <packaging>pom</packaging>
    <name>order-monitor</name>
    <description>Demo project for Spring Boot</description>

    <modules>
        <module>order-export</module>
        <module>zeus-core</module>
        <module>zeus-eagle-client-sdk</module>
        <module>zeus-worker</module>
        <module>zeus-common</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
<!--        <vip-utils.version>0.1.1</vip-utils.version>-->
        <rocketmq.version>4.3.1-iqiyi-3</rocketmq.version>
        <fastjson.version>1.2.70</fastjson.version>
        <jackson-databind.version>********</jackson-databind.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <ocm.common.version>*******-SNAPSHOT</ocm.common.version>
        <sentinel.version>1.8.0-iqiyi-1</sentinel.version>
        <vip-commons.version>1.0.100</vip-commons.version>
        <rover.version>6.1.0-iqiyi-7</rover.version>
        <order-dal.version>1.5.53</order-dal.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2021.0.5</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.3.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip</groupId>
                <artifactId>viptrade-pay-info-client</artifactId>
                <version>1.3.38</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
                <version>2.2.10.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.db</groupId>
                <artifactId>spring-boot-starter-qdbm-redis2</artifactId>
                <version>1.0.5</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.46</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>20.0</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.kit</groupId>
                <artifactId>http-client</artifactId>
                <version>2.1.4</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>spring-boot-starter-qiyi-job</artifactId>
                <version>2.4.10-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>async-task-core</artifactId>
                <version>********-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.iqiyi.config</groupId>
                        <artifactId>config-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.v</groupId>
                <artifactId>v-spring-boot-starter-eagle</artifactId>
                <version>********-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.11.1</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>3.6.10.Final</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-annotations</artifactId>
                <version>3.5.6-Final</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>4.1.7.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.12.1.GA</version>
                <optional>true</optional>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>commodity-spring-boot-starter</artifactId>
                <version>0.0.4-2-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.qiyi</groupId>
                <artifactId>ocm-common</artifactId>
                <version>${ocm.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.iqiyi.kit</groupId>
                        <artifactId>http-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard.metrics</groupId>
                        <artifactId>metrics-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>pagehelper-spring-boot-starter</artifactId>
                        <groupId>com.github.pagehelper</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.iqiyi.config</groupId>
                        <artifactId>config-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.qiyi.vip.commons</groupId>
                <artifactId>vip-commons-component</artifactId>
                <version>${vip-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-lang</groupId>
                        <artifactId>commons-lang</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.netflix.hystrix</groupId>
                        <artifactId>hystrix-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>vip-component</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${rover.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${rover.version}</version>
            </dependency>

            <!-- 核心依赖，必须引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 簇点链路功能 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-transport-simple-http</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- 配置中心动态规则管理 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-apollo</artifactId>
                <version>${sentinel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.iqiyi.config</groupId>
                        <artifactId>config-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 对接全链路平台Prometheus指标监控 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-metric-prometheus</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- sentinel 热点参数限流必须引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-parameter-flow-control</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- sentinel切面，配合@SentinelResource注解使用 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-apache-httpclient-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>
            <!-- Spring WebMvc jar引入 -->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-spring-webmvc-adapter</artifactId>
                <version>${sentinel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-lettuce</artifactId>
                <version>2.7.3</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>2.13.4</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>2.5.1</version>
            </dependency>

            <dependency>
                <groupId>com.iqiyi.vip</groupId>
                <artifactId>order-dal</artifactId>
                <version>${order-dal.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.iqiyi.config</groupId>
                        <artifactId>config-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.iqiyi.bigdata</groupId>
                <artifactId>pilot-client</artifactId>
                <version>2.6.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>config-client</artifactId>
                        <groupId>com.iqiyi.config</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-io</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>1.4.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.16</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <!-- 如果是0.1.0 有可能出现生成了maptruct的实现类，但该类只创建了对象，没有进行赋值 -->
                            <version>0.2.0</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
        </repository>
        <repository>
            <id>iqiyi-maven-boss</id>
            <name>iqiyi-maven-boss</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-boss</url>
        </repository>
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>libs-release</id>
            <name>libs-release</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-release</url>
        </repository>
        <repository>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <id>libs-snapshot</id>
            <name>libs-snapshot</name>
            <url>http://jfrog.cloud.qiyi.domain/libs-snapshot</url>
        </repository>
        <repository>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>cloudservice-releases</id>
            <name>cloudservice-releases</name>
            <url>http://jfrog.cloud.qiyi.domain:80/iqiyi-maven-cloudservice</url>
        </repository>
        <repository>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <id>cloudservice-snapshots</id>
            <name>cloudservice-snapshots</name>
            <url>http://jfrog.cloud.qiyi.domain:80/iqiyi-maven-cloudservice</url>
        </repository>
        <repository>
            <snapshots/>
            <id>iqiyi-maven-cloudservice</id>
            <name>iqiyi-maven-cloudservice</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-cloudservice</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>iqiyi-maven-release</id>
            <name>iqiyi-maven-release</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-release</url>
        </repository>
        <snapshotRepository>
            <id>iqiyi-maven-snapshot</id>
            <name>iqiyi-maven-snapshot</name>
            <url>http://jfrog.cloud.qiyi.domain/iqiyi-maven-snapshot</url>
        </snapshotRepository>
    </distributionManagement>

</project>
