package com.iqiyi.vip.zeus.eagleclient.model.eagle.panel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @author: guojing
 * @date: 2023/11/20 14:30
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@SuperBuilder
public class PrometheusPanelTarget extends PanelTarget {

    private String expr;
    private String legendFormat;

    @Override
    public String getQueryExpr() {
        return expr;
    }
}
