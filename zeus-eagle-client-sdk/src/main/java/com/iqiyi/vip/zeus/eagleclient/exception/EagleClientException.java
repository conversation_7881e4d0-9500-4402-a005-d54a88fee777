package com.iqiyi.vip.zeus.eagleclient.exception;

import lombok.Getter;
import lombok.ToString;

import com.iqiyi.vip.zeus.eagleclient.CodeEnum;

/**
 * @author: guojing
 * @date: 2023/11/16 14:54
 */
@ToString(callSuper = true)
@Getter
public class EagleClientException extends RuntimeException {

    private String code;

    public EagleClientException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public EagleClientException(CodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }


    public static EagleClientException newInvokeException() {
        return new EagleClientException(CodeEnum.OUTER_SERVICE_ERROR);
    }

    public static EagleClientException newInvokeException(String errorMsg) {
        return new EagleClientException(CodeEnum.OUTER_SERVICE_ERROR.getCode(), errorMsg);
    }

    public static EagleClientException newException(String code, String errorMsg) {
        return new EagleClientException(code, errorMsg);
    }

}
