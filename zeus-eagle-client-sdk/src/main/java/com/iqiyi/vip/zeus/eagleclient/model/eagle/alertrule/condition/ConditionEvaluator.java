package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guojing
 * @date: 2023/11/22 16:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConditionEvaluator {

    /**
     * 条件阈值
     */
    private List<Float> params;
    /**
     * gt、lt、within_range、outside_range
     * 表达式类型为classic_conditions时有：no_value
     */
    private String type;

}
