package com.iqiyi.vip.zeus.eagleclient.model.devops;

import lombok.Data;

/**
 * 服务
 * @author: guojing
 * @date: 2023/11/24 16:07
 */
@Data
public class Server {

    private Integer id;
    /**
     * 服务名称，英文名
     */
    private String serverName;
    private Integer type;
    /**
     * 服务所属的项目id
     */
    private Integer projectId;
    /**
     * 服务所属的项目名称，中文名
     */
    private String cloudProjectName;
    /**
     * 服务所属的团队名称
     */
    private String teamName;
    private Integer serverType;
    /**
     * 服务所属的git项目id
     */
    private Integer gitProjectId;
    /**
     * 服务所属的git项目名称
     */
    private String gitProjectName;
    /**
     * 系统等级
     */
//    private String sysLevel;
    /**
     * 服务描述
     */
    private String description;

}
