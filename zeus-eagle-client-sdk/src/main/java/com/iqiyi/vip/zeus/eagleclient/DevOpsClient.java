package com.iqiyi.vip.zeus.eagleclient;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.exception.DevOpsClientException;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

/**
 * @author: guojing
 * @date: 2023/11/24 16:46
 */
@Slf4j
public class DevOpsClient {

    private static final String PARAM_SYS_CODE = "sysCode";
    private static final String SYS_CODE_VALUE = "vip-zeus";

    private static final String PARAM_TOKEN = "token";

    private String serverDomain;

    private String token;

    private RestTemplate restTemplate;

    public DevOpsClient(RestTemplate restTemplate, String serverDomain, String token) {
        this.restTemplate = restTemplate;
        this.serverDomain = serverDomain;
        this.token = token;
    }

    public <T> BaseResponse<T> doGet(String path, Map<String, Object> requestParam, ParameterizedTypeReference<BaseResponse<T>> returnType) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        builder.queryParam(PARAM_SYS_CODE, SYS_CODE_VALUE);
        builder.queryParam(PARAM_TOKEN, token);
        if (MapUtils.isNotEmpty(requestParam)) {
            requestParam.forEach(builder::queryParam);
        }
        URI requestUri = builder.build().toUri();
        RequestEntity<Void> requestEntity = RequestEntity.get(requestUri).build();

        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("devops client request start, url: {}", requestUri);
            ResponseEntity<BaseResponse<T>> responseEntity = restTemplate.exchange(requestEntity, returnType);
            log.info("devops client request end, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), JsonUtils.toJsonString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (RestClientException e) {
            log.error("devops client request error, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), e.getMessage(), e);
            throw DevOpsClientException.newInvokeException();
        }
    }

    public void setServerDomain(String serverDomain) {
        this.serverDomain = serverDomain;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

}
