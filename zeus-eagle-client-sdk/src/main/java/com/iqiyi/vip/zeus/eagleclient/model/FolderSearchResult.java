package com.iqiyi.vip.zeus.eagleclient.model;

import lombok.Data;

import java.util.List;

/**
 * @author: guojing
 * @date: 2023/11/16 21:43
 */
@Data
public class FolderSearchResult {

    private Integer id;
    private String uid;
    private String title;
    private String uri;
    private String url;
    private String slug;
    /**
     * 类型
     * dash-folder: Folder
     * dash-db: Dashboard
     */
    private String type;
    private List<String> tags;
    private Boolean isStarred;
    private Integer sortMeta;

}
