package com.iqiyi.vip.zeus.eagleclient.exception;

import lombok.Getter;
import lombok.ToString;

/**
 * @author: guojing
 * @date: 2023/11/16 15:14
 */
@ToString(callSuper = true)
@Getter
public class SmartAlertRuleNotExistsException extends SmartAlertClientException {

    public SmartAlertRuleNotExistsException(String code, String msg) {
        super(code, msg);
    }

    public static SmartAlertRuleNotExistsException newException(String code, String errorMsg) {
        return new SmartAlertRuleNotExistsException(code, errorMsg);
    }

}
