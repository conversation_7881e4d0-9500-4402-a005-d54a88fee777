package com.iqiyi.vip.zeus.eagleclient.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/17 10:02
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class UpdateFolderRequest {

    private String uid;
    private String title;
    private Integer version;
    private Boolean overwrite = true;

    public UpdateFolderRequest(String uid, String title) {
        this.uid = uid;
        this.title = title;
    }
}
