package com.iqiyi.vip.zeus.eagleclient.api;

import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.http.HttpMethod;

import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertLevel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertNoticeType;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertReceiver;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertService;

/**
 * 鹰眼告警通知信息API
 * @author: guojing
 * @date: 2023/11/15 15:34
 */
public class EagleNotificationApi {

    /**
     * 通知类型
     */
    private static final String NOTICE_TYPE_URL = "/api/alert-notifications/noticeType";
    /**
     * 接收人列表
     */
    private static final String RECEIVER_LIST_URL = "/api/alert-notifications/receiver";
    /**
     * 所属服务
     */
    private static final String SERVICE_LIST_URL = "/api/alert-notifications/service";
    /**
     * 告警级别
     */
    private static final String ALERT_LEVEL_URL = "/api/alert-notifications/level";

    private EagleClient eagleClient;

    public EagleNotificationApi(EagleClient eagleClient) {
        this.eagleClient = eagleClient;
    }

    /**
     * 获取通知类型
     */
    public List<AlertNoticeType> getNoticeType() {
        return eagleClient.doRequest(HttpMethod.GET, NOTICE_TYPE_URL, new TypeReference<List<AlertNoticeType>>() {});
    }

    /**
     * 获取接收人列表
     */
    public List<AlertReceiver> getAlerReceiverList() {
        return eagleClient.doRequest(HttpMethod.GET, RECEIVER_LIST_URL, new TypeReference<List<AlertReceiver>>() {});
    }

    /**
     * 获取服务列表
     */
    public List<AlertService> getAlertServiceList() {
        return eagleClient.doRequest(HttpMethod.GET, SERVICE_LIST_URL, new TypeReference<List<AlertService>>() {});
    }

    /**
     * 告警等级列表
     */
    public List<AlertLevel> getAlertLevel() {
        return eagleClient.doRequest(HttpMethod.GET, ALERT_LEVEL_URL, new TypeReference<List<AlertLevel>>() {});
    }

}
