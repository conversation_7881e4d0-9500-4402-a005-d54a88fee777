package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: guojing
 * @date: 2023/11/28 14:27
 */
@Data
@NoArgsConstructor
public class AlertRuleExt {

    private static final String FEI_SHU = "feishu";

    private static final List<AlertRuleExtItem> DEFAULT_NOTIFY_CHANNELS = Collections.singletonList(AlertRuleExtItem.newInstance(FEI_SHU));

    /**
     * 通知渠道
     */
    private List<AlertRuleExtItem> notifyChannels;
    /**
     * 接收人
     */
    private List<AlertRuleExtItem> receivers;

    public static AlertRuleExt newInstance(String receivers) {
        if (StringUtils.isBlank(receivers)) {
            return null;
        }
        List<AlertRuleExtItem> receiversList = Arrays.stream(receivers.split(",")).map(AlertRuleExtItem::newInstance).collect(Collectors.toList());
        AlertRuleExt alertRuleExt = new AlertRuleExt();
        alertRuleExt.setNotifyChannels(DEFAULT_NOTIFY_CHANNELS);
        alertRuleExt.setReceivers(receiversList);
        return alertRuleExt;
    }

}
