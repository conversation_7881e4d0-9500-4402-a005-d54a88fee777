package com.iqiyi.vip.zeus.eagleclient.api;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.constants.FieldConstants;
import com.iqiyi.vip.zeus.eagleclient.constants.PrometheusConstants;
import com.iqiyi.vip.zeus.eagleclient.model.MetricMetadata;
import com.iqiyi.vip.zeus.eagleclient.response.MetricMetadataResponse;

/**
 * Grafana数据源schema操作API
 * @author: guojing
 * @date: 2024/1/29 15:37
 */
@Slf4j
public class PrometheusSchemaApi {

    private static final int THREE_DAYS = 3;

    private static final String START_PARAM = "start";
    private static final String END_PARAM = "end";

    private static final String MATCH_PARAM = "match[]";

    public static final String LIMIT_PER_METRIC_PARAM = "limit_per_metric";

    private static final String METRIC_LIST_URL = "/api/datasources/uid/{uid}/resources/api/v1/metadata";

    private static final String METRIC_LABEL_LIST_URL = "/api/datasources/uid/{uid}/resources/api/v1/labels";

    private static final String METRIC_LABEL_VALUES_URL = "/api/datasources/uid/{uid}/resources/api/v1/label/{label}/values";

    private EagleClient eagleClient;

    public PrometheusSchemaApi(EagleClient eagleClient) {
        this.eagleClient = eagleClient;
    }

    /**
     * 查询prometheus数据源下的所有metric
     * @param datasourceUid
     */
    public List<MetricMetadata> getAllMetricsByUid(String datasourceUid) {
        if(StringUtils.isBlank(datasourceUid)) {
            return Collections.emptyList();
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put(FieldConstants.UID, datasourceUid);
        String path = UriComponentsBuilder.fromPath(METRIC_LIST_URL).buildAndExpand(uriVariables).toUriString();
        MetricMetadataResponse<Map<String, List<Map<String, String>>>> response = null;
        try {
            response = eagleClient.doGet(path, new TypeReference<MetricMetadataResponse<Map<String, List<Map<String, String>>>>>() {});
        } catch (Exception e) {
            log.error("get all metrics by datasource uid fail, datasourceUid: {}, errorMsg: {}", datasourceUid, e.getMessage());
        }
        if (response == null || response.returnFail()) {
            return Collections.emptyList();
        }
        Map<String, List<Map<String, String>>> data = response.getData();
        if (MapUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data.entrySet().stream()
            .map(metricEntry -> new MetricMetadata(metricEntry.getKey(), metricEntry.getValue().get(0).get(PrometheusConstants.METRIC_METADATA_FIELD_TYPE)))
            .collect(Collectors.toList());
    }

    /**
     * 查询指定metric的所有label
     * @param datasourceUid
     * @param metric
     */
    public List<String> getMetricLabels(String datasourceUid, String metric) {
        if(StringUtils.isBlank(datasourceUid) || StringUtils.isBlank(metric)) {
            return Collections.emptyList();
        }

        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put(FieldConstants.UID, datasourceUid);
        String path = UriComponentsBuilder.fromPath(METRIC_LABEL_LIST_URL).buildAndExpand(uriVariables).toUriString();
        MetricMetadataResponse<List<String>> response = null;
        try {
            Instant now = Instant.now();
            Instant sevenDaysAgo = now.minus(THREE_DAYS, ChronoUnit.DAYS);
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put(START_PARAM, sevenDaysAgo.getEpochSecond());
            requestParams.put(END_PARAM, now.getEpochSecond());
            requestParams.put(MATCH_PARAM, String.format("{__name__=\"%s\"}", metric));
            response = eagleClient.doGet(path, requestParams, new TypeReference<MetricMetadataResponse<List<String>>>() {});
        } catch (Exception e) {
            log.error("get all metrics by datasource uid fail, datasourceUid: {}, errorMsg: {}", datasourceUid, e.getMessage());
        }
        if (response == null || response.returnFail()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

    public List<String> getMetricLabelValues(String datasourceUid, String metric, String label) {
        return getMetricLabelValues(datasourceUid, null, metric, label);
    }

    /**
     * 查询指定metric下指定label的取值列表
     * @param datasourceUid
     * @param metric
     * @param label
     */
    public List<String> getMetricLabelValues(String datasourceUid, String teamCode, String metric, String label) {
        if(StringUtils.isBlank(datasourceUid) || StringUtils.isBlank(metric) || StringUtils.isBlank(label)) {
            return Collections.emptyList();
        }

        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put(FieldConstants.UID, datasourceUid);
        uriVariables.put(FieldConstants.LABEL, label);
        String path = UriComponentsBuilder.fromPath(METRIC_LABEL_VALUES_URL).buildAndExpand(uriVariables).toUriString();
        MetricMetadataResponse<List<String>> response = null;
        try {
            Instant now = Instant.now();
            Instant threeDaysAgo = now.minus(THREE_DAYS, ChronoUnit.DAYS);
            String matchParamValue = String.format("{__name__=\"%s\"}", metric);
            if (StringUtils.isNotBlank(teamCode)) {
                matchParamValue = String.format("{__name__=\"%s\",teamCode=\"%s\"}", metric, teamCode);
            }
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put(START_PARAM, threeDaysAgo.getEpochSecond());
            requestParams.put(END_PARAM, now.getEpochSecond());
            requestParams.put(MATCH_PARAM, matchParamValue);
            response = eagleClient.doGet(path, requestParams, new TypeReference<MetricMetadataResponse<List<String>>>() {});
        } catch (Exception e) {
            log.error("get all metrics by datasource uid fail, datasourceUid: {}, errorMsg: {}", datasourceUid, e.getMessage(), e);
        }
        if (response == null || response.returnFail()) {
            return Collections.emptyList();
        }
        return response.getData();
    }

}
