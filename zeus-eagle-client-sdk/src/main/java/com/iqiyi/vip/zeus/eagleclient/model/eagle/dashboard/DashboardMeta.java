package com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard;

import lombok.Data;

import java.util.Map;

/**
 * Dashboard元数据
 * @author: guojing
 * @date: 2023/11/16 21:05
 */
@Data
public class DashboardMeta {

    private String type;
    private Boolean canSave;
    private Boolean canEdit;
    private Boolean canAdmin;
    private Boolean canStar;
    private Boolean canDelete;
    private String slug;
    private String url;
    private String expires;
    private String created;
    private String updated;
    private String createdBy;
    private String updatedBy;
    private Integer version;
    private Boolean hasAcl;
    private Boolean isFolder;
    private Integer folderId;
    private String folderUid;
    private String folderTitle;
    private String folderUrl;
    private Boolean provisioned;
    private String provisionedExternalId;
    private Map<String, Object> annotationsPermissions;
    private String publicDashboardUid;
    private Boolean publicDashboardEnabled;

}
