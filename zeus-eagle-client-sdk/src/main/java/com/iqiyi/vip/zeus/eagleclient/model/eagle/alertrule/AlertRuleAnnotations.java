package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/17 17:13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRuleAnnotations {

    /**
     * 告警详情
     */
    private String summary;
    private String description;
    private String runbook_url;
    private String __dashboardUid__;
    private String __panelId__;

    public static AlertRuleAnnotations newInstance(String dashboardUid, Integer panelId, String summary) {
        return AlertRuleAnnotations.builder()
            .summary(summary)
            .__dashboardUid__(dashboardUid)
            .__panelId__(panelId.toString())
            .build();
    }

}
