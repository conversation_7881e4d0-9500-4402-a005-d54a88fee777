package com.iqiyi.vip.zeus.eagleclient.model.devops;

import lombok.Data;

/**
 * 服务云项目信息
 * @author: guojing
 * @date: 2023/11/25 11:33
 */
@Data
public class Project {

    private Integer id;
    /**
     * 项目名称，英文名
     */
    private String name;
    /**
     * 项目描述，中文名
     */
    private String description;
    /**
     * 团队code
     */
    private String teamCode;
    /**
     * 团队名称
     */
    private String teamName;
    /**
     * 项目所属得小组code
     */
    private String groupTeamCode;
    /**
     * 项目所属的小组名称
     */
    private String groupTeamName;
    /**
     * 项目管理员
     */
//    private String owners;
    /**
     * 项目成员
     */
//    private String members;
    /**
     * git仓库id
     */
    private Integer gitProjectId;
//    private Integer projectId;
//    private String sysLevel;
//    private String cloudProjectName;

}
