package com.iqiyi.vip.zeus.eagleclient;

/**
 * @author: guojing
 * @date: 2023/11/25 10:13
 */
public enum CodeEnum {

    SUCCESS("A00000", "处理成功"),
    DUPLICATION("A00001", "重复请求"),
    SYSTEM_ERROR("Q00332", "系统错误"),
    PARAM_ERROR("Q00301", "参数错误"),
    INVALID_SIGN("Q00302", "签名错误"),
    LOGIN_ERROR("Q00304", "用户未登陆"),

    NOT_FOUND("Q00404", "资源不存在"),
    CONFLICT_ERROR("Q00409", "资源已存在"),
    OUTER_SERVICE_ERROR("Q00366", "请求外部服务失败，请稍后重试"),


    SMART_ALERT_RULE_GET_ERROR("Q00501", "查询智能告警规则失败"),

    SMART_ALERT_RULE_SAVE_ERROR("Q00502", "更新智能告警规则失败"),
    ;

    private String code;

    private String msg;

    CodeEnum(String code, String desc) {
        this.code = code;
        this.msg = desc;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code) || DUPLICATION.getCode().equals(code);
    }

}
