package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/28 14:41
 */
@Data
@NoArgsConstructor
public class AlertRuleExtItem {

    private String uid;

    public AlertRuleExtItem(String uid) {
        this.uid = uid;
    }

    public static AlertRuleExtItem newInstance(String uid) {
        return new AlertRuleExtItem(uid);
    }

}
