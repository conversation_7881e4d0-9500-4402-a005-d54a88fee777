package com.iqiyi.vip.zeus.eagleclient.model.eagle;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleAnnotations;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleQuery;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleExt;

/**
 * @author: guojing
 * @date: 2023/11/17 15:06
 */
@Data
public class AlertRule {

    private Integer id;
    private String uid;
    private Integer orgID;
    private String folderUID;
    /**
     * 检测分组名称
     */
    private String ruleGroup;
    /**
     * 告警名称
     */
    private String title;
    private String condition;
    /**
     * 查询和告警条件
     */
    private List<AlertRuleQuery> data;
    private String noDataState;
    private String execErrState;
    /**
     * 持续时间
     * 触发条件持续一段时间后才发送告警
     */
    @JsonProperty("for")
    private String duration;
    private AlertRuleAnnotations annotations;
    private Boolean isPaused;
    private Map<String, String> labels;
//    private String provenance;
    private String updated;
    /**
     * 告警通知所属服务，如：autorenew-api
     */
    private String service;
    /**
     * 告警级别，如：p0
     */
    private String level;
    /**
     * 告警通知扩展信息，包含告警通知的接收人、通知渠道等
     */
    private AlertRuleExt ext;

}
