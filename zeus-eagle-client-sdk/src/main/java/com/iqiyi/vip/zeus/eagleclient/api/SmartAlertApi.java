package com.iqiyi.vip.zeus.eagleclient.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import com.iqiyi.vip.zeus.eagleclient.SmartAlertClient;
import com.iqiyi.vip.zeus.eagleclient.exception.SmartAlertRuleNotExistsException;

/**
 * 智能告警API
 *
 * @author: guojing
 * @date: 2023/11/15 15:15
 * <a href="http://hubble.gitlab.qiyi.domain/normshield/docs/interface.html">智能告警OpenAPI</a>
 */
@Slf4j
public class SmartAlertApi {

    private static final String SMART_ALERT_OPEN_API_URL = "/api/v1/config/rule/{rule}";

    private SmartAlertClient smartAlertClient;

    public SmartAlertApi(SmartAlertClient smartAlertClient) {
        this.smartAlertClient = smartAlertClient;
    }

    public boolean exist(String rule) {
        if (StringUtils.isBlank(rule)) {
            return false;
        }

        try {
            String path = UriComponentsBuilder.fromPath(SMART_ALERT_OPEN_API_URL).buildAndExpand(rule).toUriString();
            smartAlertClient.doGet(path, rule);
            return true;
        } catch (SmartAlertRuleNotExistsException e) {
            return false;
        }
    }

    public String getRuleConfig(String rule) {
        if (StringUtils.isBlank(rule)) {
            return null;
        }

        try {
            String path = UriComponentsBuilder.fromPath(SMART_ALERT_OPEN_API_URL).buildAndExpand(rule).toUriString();
            return smartAlertClient.doGet(path, rule);
        } catch (SmartAlertRuleNotExistsException e) {
            return null;
        }
    }

    public boolean saveOrUpdate(String rule, String ruleConfigYaml) {
        if (StringUtils.isBlank(ruleConfigYaml)) {
            return false;
        }

        String path = UriComponentsBuilder.fromPath(SMART_ALERT_OPEN_API_URL).buildAndExpand(rule).toUriString();
        smartAlertClient.doPost(path, rule, ruleConfigYaml);
        return true;
    }

    public boolean deleteRule(String rule) {
        String path = UriComponentsBuilder.fromPath(SMART_ALERT_OPEN_API_URL).buildAndExpand(rule).toUriString();
        smartAlertClient.deleteRule(path, rule);
        return true;
    }

}
