package com.iqiyi.vip.zeus.eagleclient.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;


/**
 * @author: guojing
 * @date: 2023/11/16 19:34
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(jsonString)) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("read value occurred exception, jsonString: {}", jsonString, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static <T> String toJsonString(T object) {
        if (object == null) {
            return null;
        }
        if (object instanceof String) {
            return (String) object;
        }
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            log.error("writeValueAsString occurred exception, objectValue: {}", object, e);
            throw new RuntimeException("解析json出现异常", e);
        }
    }

    public static boolean validJson(String jsonStr) {
        if (StringUtils.isBlank(jsonStr)) {
            return false;
        }
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonStr);
            return jsonNode.isArray() || jsonNode.isObject();
        } catch (Exception e) {
            return false;
        }
    }

}
