package com.iqiyi.vip.zeus.eagleclient;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.RequestEntity.BodyBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Collections;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientAlreadyExistsException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.response.OperateResponse;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

/**
 * 请求鹰眼监控服务的代理类
 *
 * @author: guojing
 * @date: 2023/11/15 10:28 Grafana开发API： https://grafana.com/docs/grafana/latest/developers/http_api/
 */
@Slf4j
public class EagleClient {

    private String serverDomain;

    private String token;

    private RestTemplate restTemplate;

    public EagleClient(RestTemplate restTemplate, String serverDomain, String token) {
        this.restTemplate = restTemplate;
        this.serverDomain = serverDomain;
        this.token = token;
    }

    public <B, T> T doRequest(HttpMethod httpMethod, String path, B body, TypeReference<T> returnType) {
        return doRequest(httpMethod, path, null, body, returnType);
    }

    public <B, T> T doRequest(HttpMethod httpMethod, String path, TypeReference<T> returnType) {
        return doRequest(httpMethod, path, null, returnType);
    }

    public <B, T> T doRequest(HttpMethod httpMethod, String path, Map<String, String> customHeaders, TypeReference<T> returnType) {
        return doRequest(httpMethod, path, customHeaders, null, returnType);
    }

    public <B, T> T doRequest(HttpMethod httpMethod, String path, Map<String, String> customHeaders, B body, TypeReference<T> returnType) {
        URI requestUri = URI.create(serverDomain + path);
        BodyBuilder requestBuilder = RequestEntity.method(httpMethod, requestUri);
        requestBuilder.accept(MediaType.APPLICATION_JSON);
        requestBuilder.contentType(MediaType.APPLICATION_JSON);
        requestBuilder.header(HttpHeaders.AUTHORIZATION, "Bearer " + token);
        if (MapUtils.isNotEmpty(customHeaders)) {
            customHeaders.forEach(requestBuilder::header);
        }
        RequestEntity<B> requestEntity = requestBuilder.body(body);

        StopWatch stopWatch = StopWatch.createStarted();
        ResponseEntity<String> responseEntity = null;
        try {
            log.info("eagle client request start, url: {}, requestBody:{}", requestUri, JsonUtils.toJsonString(body));
            responseEntity = restTemplate.exchange(requestEntity, String.class);
            HttpStatus statusCode = responseEntity.getStatusCode();
            log.info("eagle client request end, url: {}, cost: {}, httpStatus: {}, response: {}",
                requestUri, stopWatch.getTime(), statusCode, JsonUtils.toJsonString(responseEntity.getBody()));
            return JsonUtils.parseObject(responseEntity.getBody(), returnType);
        } catch (RestClientException e) {
            log.error("eagle client request error, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), e.getMessage(), e);
            if (e instanceof HttpClientErrorException) {
                HttpClientErrorException httpClientErrorException = (HttpClientErrorException) e;
                HttpStatus statusCode = httpClientErrorException.getStatusCode();
                String codeStr = String.valueOf(statusCode.value());
                String errorBodyStr = httpClientErrorException.getResponseBodyAsString();
                String errorMsg = errorBodyStr;
                if (JsonUtils.validJson(errorBodyStr)) {
                    OperateResponse operateResponse = JsonUtils.parseObject(errorBodyStr, OperateResponse.class);
                    errorMsg = operateResponse != null ? operateResponse.getMessage() : null;
                }
                if (statusCode == HttpStatus.NOT_FOUND) {
                    throw EagleClientNotExistsException.newException(codeStr, errorMsg);
                }
                if (statusCode == HttpStatus.CONFLICT || statusCode == HttpStatus.PRECONDITION_FAILED) {
                    throw EagleClientAlreadyExistsException.newException(codeStr, errorMsg);
                }
                throw EagleClientException.newInvokeException(codeStr + "-" + errorMsg);
            }
            throw EagleClientException.newInvokeException(e.getMessage());
        }
    }

    public <T> T doGet(String path, TypeReference<T> returnType) {
        return doGet(path, Collections.emptyMap(), returnType);
    }

    public <T> T doGet(String path, Map<String, Object> requestParam, TypeReference<T> returnType) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        if (MapUtils.isNotEmpty(requestParam)) {
            requestParam.forEach(builder::queryParam);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.AUTHORIZATION, "Bearer " + token);
        URI requestUri = builder.build().toUri();
        RequestEntity<Void> requestEntity = RequestEntity.get(requestUri).headers(headers).build();

        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("eagle client request start, url: {}", requestUri);
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity, String.class);
            log.info("eagle client request end, url: {}, cost: {}, httpStatus: {}, response: {}",
                requestUri, stopWatch.getTime(), responseEntity.getStatusCode(), responseEntity.getBody());
            return JsonUtils.parseObject(responseEntity.getBody(), returnType);
        } catch (RestClientException e) {
            if (e instanceof HttpClientErrorException) {
                HttpClientErrorException httpClientErrorException = (HttpClientErrorException) e;
                HttpStatus statusCode = httpClientErrorException.getStatusCode();
                String codeStr = String.valueOf(statusCode.value());
                String errorBodyStr = httpClientErrorException.getResponseBodyAsString();
                String errorMsg = errorBodyStr;
                if (JsonUtils.validJson(errorBodyStr)) {
                    OperateResponse operateResponse = JsonUtils.parseObject(errorBodyStr, OperateResponse.class);
                    errorMsg = operateResponse != null ? operateResponse.getMessage() : null;
                }
                log.error("eagle client request error, url: {}, cost: {}, httpStatus: {}, response: {}", requestUri, stopWatch.getTime(), codeStr, e.getMessage(), e);
                throw EagleClientException.newInvokeException(codeStr + "-" + errorMsg);
            }
            log.error("eagle client request error, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), e.getMessage(), e);
            throw EagleClientException.newInvokeException(e.getMessage());
        }
    }

    public void setServerDomain(String serverDomain) {
        this.serverDomain = serverDomain;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

}
