package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.AlertRule;

/**
 * @author: guojing
 * @date: 2023/11/21 13:52
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class AlertRuleGroup {

    private String folderUid;
    private String title;
    /**
     * 检测频率，单位秒
     */
    private Integer interval;
    private List<AlertRule> rules;

    public static AlertRuleGroup newInstance(String folderUid, String title, Integer interval) {
        return AlertRuleGroup.builder().folderUid(folderUid).title(title).interval(interval).build();
    }

}
