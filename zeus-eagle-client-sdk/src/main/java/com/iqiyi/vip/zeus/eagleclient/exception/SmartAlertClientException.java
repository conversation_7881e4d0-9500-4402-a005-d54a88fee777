package com.iqiyi.vip.zeus.eagleclient.exception;

import lombok.Getter;
import lombok.ToString;

import com.iqiyi.vip.zeus.eagleclient.CodeEnum;

/**
 * @author: guojing
 * @date: 2023/11/16 14:54
 */
@ToString(callSuper = true)
@Getter
public class SmartAlertClientException extends RuntimeException {

    private String code;

    public SmartAlertClientException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public SmartAlertClientException(CodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }


    public static SmartAlertClientException newInvokeException() {
        return new SmartAlertClientException(CodeEnum.OUTER_SERVICE_ERROR);
    }

    public static SmartAlertClientException newGetRuleException() {
        return new SmartAlertClientException(CodeEnum.SMART_ALERT_RULE_GET_ERROR);
    }

    public static SmartAlertClientException newSaveRuleException() {
        return new SmartAlertClientException(CodeEnum.SMART_ALERT_RULE_SAVE_ERROR);
    }

    public static SmartAlertClientException newException(String code, String errorMsg) {
        return new SmartAlertClientException(code, errorMsg);
    }

}
