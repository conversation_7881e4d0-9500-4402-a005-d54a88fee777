package com.iqiyi.vip.zeus.eagleclient.model.eagle;

import lombok.Data;

import java.util.Map;

/**
 * Grafana 数据源模型
 * @author: guojing
 * @date: 2023/11/15 17:45
 */
@Data
public class Datasource {

    /**
     * 数据源ID
     * an auto-incrementing numeric value,
     * only unique per Grafana install.
     */
    private Integer id;
    /**
     * 数据源UID
     * unique identifier for a data source
     */
    private String uid;
    /**
     * 组织ID
     */
    private Integer orgId;
    /**
     * 数据源名称
     */
    private String name;
    /**
     * 数据源类型
     */
    private String type;
    /**
     * 数据源类型logo url
     */
    private String typeLogoUrl;
    /**
     * 数据源access
     */
    private String access;
    /**
     * 数据源url
     */
    private String url;
    /**
     * 用户名
     */
    private String user;
    /**
     * 数据库
     */
    private String database;
    private Boolean basicAuth;
    private String basicAuthUser;
    private Boolean withCredentials;
    /**
     * 是否默认数据源
     */
    private Boolean isDefault;
    private Map<String, Object> jsonData;
    private Map<String, Boolean> secureJsonFields;
    private Boolean readOnly;
    private Integer version;

}
