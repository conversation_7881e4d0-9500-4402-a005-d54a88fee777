package com.iqiyi.vip.zeus.eagleclient.model.devops;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/24 15:51
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Team {

    //会员开发团队编码
    public static final String VIP_TEAM_CODE = "573";

    /**
     * 团队ID
     */
    private Integer id;
    /**
     * 团队code
     */
    private String teamCode;
    /**
     * 团队英文名称
     */
    private String enName;
    /**
     * 团队中文名称
     */
    private String cnName;
    /**
     * 团队邮箱
     */
    private String teamEmail;
    /**
     * 当前团队上一级团队code
     */
    private String parentTeamCode;
    private Integer type;
    /**
     * 团队描述
     */
    private String description;

    public static Team buildVipTeam() {
        return Team.builder()
            .id(1)
            .teamCode(VIP_TEAM_CODE)
            .enName("MBD_RD")
            .cnName("会员开发")
            .teamEmail("<EMAIL>")
            .parentTeamCode("11")
            .type(1)
            .description("会员及海外业务群MOG—会员业务事业部—会员开发")
            .build();
    }

}
