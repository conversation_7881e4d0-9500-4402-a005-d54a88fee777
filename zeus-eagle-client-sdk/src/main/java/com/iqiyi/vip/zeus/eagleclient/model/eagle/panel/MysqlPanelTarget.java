package com.iqiyi.vip.zeus.eagleclient.model.eagle.panel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/12/21 11:27
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@SuperBuilder
public class MysqlPanelTarget extends PanelTarget {

    private String dataset;
    private String format;
    private Boolean rawQuery;
    private String rawSql;
    private Map<String, Object> sql;

    @Override
    public String getQueryExpr() {
        return rawSql;
    }
}
