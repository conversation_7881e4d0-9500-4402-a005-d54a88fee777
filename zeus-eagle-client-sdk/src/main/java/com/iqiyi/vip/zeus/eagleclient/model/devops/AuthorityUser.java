package com.iqiyi.vip.zeus.eagleclient.model.devops;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guojing
 * @date: 2023/12/13 10:45
 */
@Data
public class AuthorityUser {

    private String oaAccount;
    private Long cloudId;
    private String name;
    private String personalEmail;
    private String phone;
    private Integer hasManager;
    private AuthorityTeamBasic realTeam;
    private List<AuthorityTeamBasic> virtualTeamList;
    private List<AuthorityRoleBasic> roleList;
    private String description;
    private Integer status;
    private String department;
    private String createOperator;
    private String updateOperator;
    private Timestamp createTime;
    private Timestamp updateTime;

    public boolean underThisTeam(String teamCode) {
        if (StringUtils.isBlank(teamCode)) {
            return false;
        }
        return realTeam.getTeamCode().equals(teamCode) || Team.VIP_TEAM_CODE.equals(teamCode);
    }

    public boolean notUnderThisTeam(String teamCode) {
        return !underThisTeam(teamCode);
    }

}
