package com.iqiyi.vip.zeus.eagleclient;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

import com.iqiyi.vip.zeus.eagleclient.exception.SmartAlertClientException;
import com.iqiyi.vip.zeus.eagleclient.exception.SmartAlertRuleNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.utils.SmartAlertTokenUtil;

/**
 * @author: guojing
 * @date: 2023/11/24 16:46
 */
@Slf4j
public class SmartAlertClient {

    private static final String HEADER_X_AUTH_TOKEN = "X-Auth-Token";

    private String serverDomain;

    private RestTemplate restTemplate;

    public SmartAlertClient(RestTemplate restTemplate, String serverDomain) {
        this.restTemplate = restTemplate;
        this.serverDomain = serverDomain;
    }

    public String doGet(String path, String rule) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        String authToken = SmartAlertTokenUtil.genAuthToken(rule);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_X_AUTH_TOKEN, authToken);
        URI requestUri = builder.build().toUri();
        RequestEntity<Void> requestEntity = RequestEntity.get(requestUri).headers(headers).build();

        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("SmartAlertClient doGet request start, url: {}", requestUri);
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity, String.class);
            if (responseEntity.getStatusCode() == HttpStatus.NO_CONTENT) {
                throw SmartAlertRuleNotExistsException.newException(responseEntity.getStatusCode().toString(), "smart alert rule not exists");
            }
            String body = responseEntity.getBody();
            log.info("SmartAlertClient doGet request end, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), body);
            return body;
        } catch (RestClientException e) {
            log.error("SmartAlertClient request error, url: {}, cost: {}, errorMsg: {}", requestUri, stopWatch.getTime(), e.getMessage(), e);
            throw SmartAlertClientException.newGetRuleException();
        }
    }

    public boolean doPost(String path, String rule, String body) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        HttpHeaders headers = new HttpHeaders();
        String authToken = SmartAlertTokenUtil.genAuthToken(rule);
        headers.add(HEADER_X_AUTH_TOKEN, authToken);
        headers.add("Content-Type", "application/yaml");
        URI requestUri = builder.build().toUri();
        HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("SmartAlertClient doPost request start, url: {}, body:{}", requestUri, body);
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestUri, HttpMethod.POST, requestEntity, String.class);
            log.info("SmartAlertClient doPost request end, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), responseEntity.getBody());
            return true;
        } catch (RestClientException e) {
            log.error("SmartAlertClient doPost request error, url: {}, cost: {}, errorMsg: {}", requestUri, stopWatch.getTime(), e.getMessage(), e);
            throw SmartAlertClientException.newSaveRuleException();
        }
    }

    public boolean deleteRule(String path, String rule) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        String authToken = SmartAlertTokenUtil.genAuthToken(rule);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HEADER_X_AUTH_TOKEN, authToken);
        URI requestUri = builder.build().toUri();
        RequestEntity<Void> requestEntity = RequestEntity.delete(requestUri).headers(headers).build();

        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("SmartAlertClient deleteRule request start, url: {}", requestUri);
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity, String.class);
            String body = responseEntity.getBody();
            log.info("SmartAlertClient deleteRule request end, url: {}, cost: {}, response: {}", requestUri, stopWatch.getTime(), body);
            return true;
        } catch (RestClientException e) {
            log.error("SmartAlertClient deleteRule error, url: {}, cost: {}, errorMsg: {}", requestUri, stopWatch.getTime(), e.getMessage(), e);
            throw SmartAlertClientException.newGetRuleException();
        }
    }

    public void setServerDomain(String serverDomain) {
        this.serverDomain = serverDomain;
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

}
