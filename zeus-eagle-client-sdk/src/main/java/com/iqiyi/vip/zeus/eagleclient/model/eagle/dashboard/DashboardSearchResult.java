package com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard;

import lombok.Data;

import java.util.List;

/**
 * @author: guojing
 * @date: 2023/11/16 21:43
 */
@Data
public class DashboardSearchResult {

    private Integer id;
    private String uid;
    private String title;
    private String uri;
    private String url;
    private String slug;
    /**
     * 类型
     * dash-folder: Folder
     * dash-db: Dashboard
     */
    private String type;
    private List<String> tags;
    private Boolean isStarred;
    private Integer folderId;
    private String folderUid;
    private String folderTitle;
    private String folderUrl;
    private Integer sortMeta;

}
