package com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard;

import lombok.Data;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.DatasourceRef;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelFieldConfig;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelGridPos;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.panel.PanelOptions;

/**
 * @author: guojing
 * @date: 2023/11/16 21:25
 */
@Data
public class DashboardPanel {

    private Integer id;
    private String title;
    private String type;
    private String interval;
    private DatasourceRef datasource;
    private List<Map<String, Object>> targets;
    private PanelFieldConfig fieldConfig;
    private PanelGridPos gridPos;
    private PanelOptions options;
    private String pluginVersion;

}
