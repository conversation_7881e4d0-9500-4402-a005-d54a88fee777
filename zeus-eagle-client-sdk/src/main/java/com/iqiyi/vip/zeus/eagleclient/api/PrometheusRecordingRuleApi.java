package com.iqiyi.vip.zeus.eagleclient.api;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.zeus.eagleclient.EagleCenterClient;
import com.iqiyi.vip.zeus.eagleclient.exception.DevOpsClientException;
import com.iqiyi.vip.zeus.eagleclient.model.RecordRule;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * 创建和查询Recording Rule
 *
 * @author: guojing
 * @date: 2024/4/2 17:54
 */
@Slf4j
public class PrometheusRecordingRuleApi {

    private static final String SERVER_NAME_PARAM = "serviceName";
    private static final String SERVICE_VIP_XUANWU = "vip-xuanwu";
    public static final String RECORD_NAME_PREFIX = SERVICE_VIP_XUANWU.replace("-", "_") + ":";
    public static final String RECORD_RULE_LIST_PARAM = "recordRules";
    public static final String RECORD_NAME_LIST_PARAM = "recordNames";


    private static final String BATCH_CREATE_URL = "/record/batchNewConfig";
    private static final String BATCH_DELETE_URL = "/record/batchDeleteConfig";

    private EagleCenterClient eagleCenterClient;

    public PrometheusRecordingRuleApi(EagleCenterClient eagleCenterClient) {
        this.eagleCenterClient = eagleCenterClient;
    }

    /**
     * 批量创建Recording Rule
     */
    public boolean batchCreate(List<RecordRule> recordRules) {
        if (CollectionUtils.isEmpty(recordRules)) {
            return true;
        }
        List<RecordRule> recordRuleParams = recordRules.stream()
            .map(this::removeRecordPrefixIfExists)
            .distinct().collect(Collectors.toList());
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put(RECORD_RULE_LIST_PARAM, recordRuleParams);
        requestParams.put(SERVER_NAME_PARAM, SERVICE_VIP_XUANWU);
        BaseResponse<Void> baseResponse = eagleCenterClient.doPostByJson(BATCH_CREATE_URL, requestParams, new ParameterizedTypeReference<BaseResponse<Void>>() {});
        if (baseResponse.failure()) {
            throw DevOpsClientException.newException(baseResponse.getCode(), baseResponse.getMsg());
        }
        return true;
    }

    /**
     * 批量删除Recording Rule
     * @param recordNames 包含服务名前缀(vip_xuanwu:)
     */
    public boolean batchDelete(List<String> recordNames) {
        if (CollectionUtils.isEmpty(recordNames)) {
            return false;
        }
        List<String> newRecordNames = recordNames.stream()
            .map(this::addRecordPrefixIfNeed)
            .distinct().collect(Collectors.toList());
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put(RECORD_NAME_LIST_PARAM, String.join(",", newRecordNames));
        BaseResponse<String> baseResponse = eagleCenterClient.doGet(BATCH_DELETE_URL, requestParams, new ParameterizedTypeReference<BaseResponse<String>>() {});
        if (baseResponse.failure()) {
            throw DevOpsClientException.newException(baseResponse.getCode(), baseResponse.getMsg());
        }
        return true;
    }

    /**
     * record名字增加服务名前缀
     * @param recordName
     */
    private String addRecordPrefixIfNeed(String recordName) {
        return recordName.startsWith(RECORD_NAME_PREFIX) ? recordName : RECORD_NAME_PREFIX + recordName;
    }

    private RecordRule removeRecordPrefixIfExists(RecordRule recordRule) {
        String record = recordRule.getRecord();
        if (!record.startsWith(RECORD_NAME_PREFIX)) {
            return recordRule;
        }
        recordRule.setRecord(StringUtils.substringAfter(record, RECORD_NAME_PREFIX));
        return recordRule;
    }

}
