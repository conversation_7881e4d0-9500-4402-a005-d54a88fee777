package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition.AlertRuleConditionModelCondition;

/**
 * @author: guojing
 * @date: 2023/12/27 15:47
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRuleConditionModel {

    private static final Map<String, Object> DEFAULT_DATASOURCE = new HashMap<>();

    static {
        DEFAULT_DATASOURCE.put("name", "Expression");
        DEFAULT_DATASOURCE.put("type", "__expr__");
        DEFAULT_DATASOURCE.put("uid", "__expr__");
    }

    private List<AlertRuleConditionModelCondition> conditions;
    private Map<String, Object> datasource;
    private String expression;
    private Integer intervalMs;
    private Integer maxDataPoints;
    private String refId;
    private String type;

    public static AlertRuleConditionModel newDefault(String refId) {
        return AlertRuleConditionModel.builder()
            .datasource(DEFAULT_DATASOURCE)
            .expression("")
            .intervalMs(1000)
            .maxDataPoints(43200)
            .refId(refId)
            .type("classic_conditions")
            .build();
    }

}
