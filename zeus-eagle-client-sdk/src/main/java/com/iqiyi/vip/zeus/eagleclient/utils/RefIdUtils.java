package com.iqiyi.vip.zeus.eagleclient.utils;

/**
 * @author: guojing
 * @date: 2023/12/27 15:29
 */
public class RefIdUtils {

    private static final String QUERY_REF_ID_PREFIX = "Query";

    private static final String CONDITION_REF_ID_PREFIX = "Condition";

    public static String buildRefIdFromMonitorQueryId(Integer queryId) {
        return QUERY_REF_ID_PREFIX + queryId.toString();
    }

    public static String buildConditionRefIdFromMonitorQueryId(Integer conditionId) {
        return CONDITION_REF_ID_PREFIX + conditionId.toString();
    }

}
