package com.iqiyi.vip.zeus.eagleclient.api;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestClientException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Datasource;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDatasourceRequest;
import com.iqiyi.vip.zeus.eagleclient.response.CreateDatasourceResponse;
import com.iqiyi.vip.zeus.eagleclient.response.OperateResponse;

/**
 * Grafana数据源操作API
 *
 * @author: guojing
 * @date: 2023/11/15 15:13
 * <a href="https://grafana.com/docs/grafana/latest/developers/http_api/data_source/">Grafana Datasource API</a>
 */
@Slf4j
public class EagleDatasourceApi {

    private static final String BASE_DATASOURCE_URL = "/api/datasources";

    private static final String DATASOURCE_BY_UID_URL = "/api/datasources/uid/{uid}";

    private static final String DATASOURCE_BY_NAME_URL = "/api/datasources/name/{name}";
    private static final String DATASOURCE_HEALTH_CHECK_URL = "/api/datasources/uid/{uid}/health";

    private EagleClient eagleClient;

    public EagleDatasourceApi(EagleClient eagleClient) {
        this.eagleClient = eagleClient;
    }

    public CreateDatasourceResponse createDataSource(CreateDatasourceRequest createRequest) {
        CreateDatasourceResponse response = null;
        response = eagleClient.doRequest(HttpMethod.POST, BASE_DATASOURCE_URL, createRequest,
            new TypeReference<CreateDatasourceResponse>() {});
        return response;
    }

    public CreateDatasourceResponse updateDataSource(CreateDatasourceRequest updateRequest) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", updateRequest.getUid());
        String updatePath = UriComponentsBuilder.fromPath(DATASOURCE_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        return eagleClient.doRequest(HttpMethod.PUT, updatePath, updateRequest,
            new TypeReference<CreateDatasourceResponse>() {});
    }

    public boolean deleteDataSource(String uid) {
        if (StringUtils.isBlank(uid)) {
            return false;
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String deletePath = UriComponentsBuilder.fromPath(DATASOURCE_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            eagleClient.doRequest(HttpMethod.DELETE, deletePath, new TypeReference<OperateResponse>() {});
            return true;
        } catch (EagleClientException e) {
            return false;
        }
    }

    public Datasource getDataSourceByUid(String uid) {
        if(StringUtils.isBlank(uid)) {
            return null;
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String path = UriComponentsBuilder.fromPath(DATASOURCE_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            return eagleClient.doRequest(HttpMethod.GET, path, new TypeReference<Datasource>() {});
        } catch (EagleClientNotExistsException e) {
            return null;
        }
    }

    /**
     * 根据名称查询数据源，精确匹配
     * @param name
     */
    public Datasource getDataSourceByName(String name) {
        if(StringUtils.isBlank(name)) {
            return null;
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("name", name);
        String path = UriComponentsBuilder.fromPath(DATASOURCE_BY_NAME_URL).buildAndExpand(uriVariables).toUriString();
        try {
            return eagleClient.doRequest(HttpMethod.GET, path, new TypeReference<Datasource>() {});
        } catch (EagleClientNotExistsException e) {
            return null;
        }
    }

    public List<Datasource> getAllDataSource() {
        return eagleClient.doRequest(HttpMethod.GET, BASE_DATASOURCE_URL, new TypeReference<List<Datasource>>() {});
    }

    public boolean checkHealth(String uid) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String path = UriComponentsBuilder.fromPath(DATASOURCE_HEALTH_CHECK_URL).buildAndExpand(uriVariables).toUriString();
        try {
            eagleClient.doRequest(HttpMethod.GET, path, new TypeReference<OperateResponse>() {});
            return true;
        } catch (EagleClientException e) {
            return false;
        }
    }

}
