package com.iqiyi.vip.zeus.eagleclient.response;

import lombok.Data;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.Datasource;

/**
 * @author: guojing
 * @date: 2023/11/15 17:41
 */
@Data
public class CreateDatasourceResponse {

    private Datasource datasource;

    /**
     * 数据源id
     */
    private Integer id;
    /**
     * 数据源名称
     */
    private String name;
    /**
     * 操作信息
     */
    private String message;

}
