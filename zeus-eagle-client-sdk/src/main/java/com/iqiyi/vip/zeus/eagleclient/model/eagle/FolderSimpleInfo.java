package com.iqiyi.vip.zeus.eagleclient.model.eagle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/17 09:40
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FolderSimpleInfo {

    private Integer id;
    private String uid;
    private String title;
    private String url;

    public static FolderSimpleInfo buildFromFolder(Folder folder) {
        if (folder == null) {
            return null;
        }
        return FolderSimpleInfo.builder()
            .id(folder.getId())
            .uid(folder.getUid())
            .title(folder.getTitle())
            .url(folder.getUrl())
            .build();
    }

}
