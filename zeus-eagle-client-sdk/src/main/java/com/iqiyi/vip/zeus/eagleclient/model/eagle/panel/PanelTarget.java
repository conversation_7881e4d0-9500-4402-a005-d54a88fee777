package com.iqiyi.vip.zeus.eagleclient.model.eagle.panel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.DatasourceRef;

/**
 * @author: guojing
 * @date: 2023/11/20 14:30
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@SuperBuilder
public abstract class PanelTarget {

    private DatasourceRef datasource;
    private String editorMode;
    private Boolean hide;
    private Boolean instant;
    private String interval;
    private Boolean range;
    private String refId;

    public abstract String getQueryExpr();

}
