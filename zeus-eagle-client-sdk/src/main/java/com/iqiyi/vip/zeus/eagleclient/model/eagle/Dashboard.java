package com.iqiyi.vip.zeus.eagleclient.model.eagle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardPanel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardTemplating;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardTime;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardTimePicker;

/**
 * @author: guojing
 * @date: 2023/11/15 21:07
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class Dashboard {

    private Integer id;
    private String uid;
    private String title;
    private Annotations annotations;
    private Boolean editable;
    private Integer fiscalYearStartMonth;
    private Integer graphTooltip;
    private List<String> links;
    private Boolean liveNow;
    private List<DashboardPanel> panels;
    private String refresh;
    private Integer schemaVersion;
    private String style;
    private List<String> tags;
    private DashboardTemplating templating;
    private DashboardTime time;
    private DashboardTimePicker timepicker;
    private String timezone;
    private Integer version;
    private String weekStart;

    public void addPanel(DashboardPanel panel) {
        if (panels == null) {
            panels = new ArrayList<>();
        }
        panels.add(panel);
    }

}
