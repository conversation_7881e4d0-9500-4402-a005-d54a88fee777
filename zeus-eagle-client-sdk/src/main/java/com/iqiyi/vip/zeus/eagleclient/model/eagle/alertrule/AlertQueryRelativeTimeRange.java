package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/21 17:26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertQueryRelativeTimeRange {

    private static final int ONE_HOUR_SECONDS = 30 * 60;

    /**
     * 查询开始时间
     * 单位：秒
     */
    private Integer from;
    /**
     * 查询结束时间
     * 单位：秒
     */
    private Integer to;

    public static AlertQueryRelativeTimeRange newQueryDefault() {
        return new AlertQueryRelativeTimeRange(ONE_HOUR_SECONDS, 0);
    }

    public static AlertQueryRelativeTimeRange newConditionDefault() {
        return new AlertQueryRelativeTimeRange(0, 0);
    }

}
