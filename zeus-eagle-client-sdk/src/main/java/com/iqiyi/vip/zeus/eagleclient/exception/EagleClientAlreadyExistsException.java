package com.iqiyi.vip.zeus.eagleclient.exception;

import lombok.Getter;
import lombok.ToString;

/**
 * @author: guojing
 * @date: 2023/11/16 15:14
 */
@ToString(callSuper = true)
@Getter
public class EagleClientAlreadyExistsException extends EagleClientException {

    public EagleClientAlreadyExistsException(String code, String msg) {
        super(code, msg);
    }

    public static EagleClientAlreadyExistsException newException(String code, String errorMsg) {
        return new EagleClientAlreadyExistsException(code, errorMsg);
    }

}
