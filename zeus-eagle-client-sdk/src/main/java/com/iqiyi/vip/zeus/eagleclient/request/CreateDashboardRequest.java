package com.iqiyi.vip.zeus.eagleclient.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;

/**
 * @author: guojing
 * @date: 2023/11/16 20:54
 */
@Data
public class CreateDashboardRequest {

    private Dashboard dashboard;
    private String folderUid;
    private String message;
    private boolean overwrite;

    public CreateDashboardRequest() {
    }

    public CreateDashboardRequest(Dashboard dashboard, String folderUid, String message) {
        this.dashboard = dashboard;
        this.folderUid = folderUid;
        this.message = message;
    }

    public CreateDashboardRequest(Dashboard dashboard, String folderUid, String message, boolean overwrite) {
        this.dashboard = dashboard;
        this.folderUid = folderUid;
        this.message = message;
        this.overwrite = overwrite;
    }

    public static CreateDashboardRequest newCreateInstance(Dashboard dashboard, String folderUid, String message) {
        if (StringUtils.isBlank(message)) {
            message = "创建Dashboard: " + dashboard.getTitle();
        }
        return new CreateDashboardRequest(dashboard, folderUid, message);
    }

    public static CreateDashboardRequest newUpdateInstance(Dashboard dashboard, String folderUid, String message) {
        if (StringUtils.isBlank(message)) {
            message = "更新Dashboard: " + dashboard.getTitle();
        }
        return new CreateDashboardRequest(dashboard, folderUid, message, true);
    }

}
