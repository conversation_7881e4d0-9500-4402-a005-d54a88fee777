package com.iqiyi.vip.zeus.eagleclient.api;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardSearchResult;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDashboardRequest;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.response.CreateDashboardResponse;
import com.iqiyi.vip.zeus.eagleclient.response.OperateResponse;

/**
 * Grafana仪表盘操作API
 *
 * @author: guojing
 * @date: 2023/11/15 15:15
 * <a href="https://grafana.com/docs/grafana/latest/developers/http_api/dashboard/">Grafana Dashboard API</a>
 */
@Slf4j
public class EagleDashboardApi {

    private static final String DASHBOARD_URL = "/api/dashboards/db";

    private static final String DASHBOARD_BY_UID_URL = "/api/dashboards/uid/{uid}";

    private static final String DASHBOARD_SEARCH_URL = "/api/search";

    private EagleClient eagleClient;

    public EagleDashboardApi(EagleClient eagleClient) {
        this.eagleClient = eagleClient;
    }

    /**
     * 创建或更新Dashboard
     */
    public CreateDashboardResponse createOrUpdateDashboard(CreateDashboardRequest request) {
        CreateDashboardResponse response = null;
        response = eagleClient.doRequest(HttpMethod.POST, DASHBOARD_URL, request,
            new TypeReference<CreateDashboardResponse>() {
            });
        return response;
    }

    public boolean deleteDashboard(String uid) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String deletePath = UriComponentsBuilder.fromPath(DASHBOARD_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            eagleClient.doRequest(HttpMethod.DELETE, deletePath, new TypeReference<OperateResponse>() {
            });
            return true;
        } catch (EagleClientException e) {
            return false;
        }
    }

    public DashboardWithMeta getDashboardByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String path = UriComponentsBuilder.fromPath(DASHBOARD_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            return eagleClient.doRequest(HttpMethod.GET, path, new TypeReference<DashboardWithMeta>() {
            });
        } catch (EagleClientNotExistsException e) {
            return null;
        }
    }

    /**
     * 获取指定Folder ID下的所有Dashboard
     */
    public List<DashboardSearchResult> getAllUnderFolder(Integer folderId) {
        String requestPath = UriComponentsBuilder.fromPath(DASHBOARD_SEARCH_URL)
            .queryParam("type", "dash-db")
            .queryParam("folderIds", folderId)
            .toUriString();
        return eagleClient.doRequest(HttpMethod.GET, requestPath, new TypeReference<List<DashboardSearchResult>>() {
        });
    }

    /**
     * 根据title模糊搜索Dashboard
     */
    public List<DashboardSearchResult> searchDashboardByTitle(Integer folderId, String title) {
        String requestPath = UriComponentsBuilder.fromPath(DASHBOARD_SEARCH_URL)
            .queryParam("type", "dash-db")
            .queryParam("folderIds", folderId)
            .queryParam("query", title)
            .toUriString();
        return eagleClient.doRequest(HttpMethod.GET, requestPath, new TypeReference<List<DashboardSearchResult>>() {
        });
    }

}
