package com.iqiyi.vip.zeus.eagleclient.api;

import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.AlertRule;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Folder;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleGroup;

/**
 * 鹰眼告警操作API
 * @author: guojing
 * @date: 2023/11/15 15:15
 * <a href="https://grafana.com/docs/grafana/latest/developers/http_api/alerting_provisioning/">Grafana Alert API</a>
 */
public class EagleAlertRuleApi {

    private static final String HEADER_X_Disable_Provenance = "X-Disable-Provenance";

    private static final String ALERT_RULE_URL = "/api/v1/provisioning/alert-rules";
    private static final String ALERT_RULE_BY_UID_URL = "/api/v1/provisioning/alert-rules/{uid}";

    private static final String RULE_GROUP_URL = "/api/v1/provisioning/folder/{folderUid}/rule-groups/{ruleGroupTitle}";

    private static final Map<String, String> CUSTOM_HEADERS = new HashMap<>();

    static {
        //允许在Grafana UI中编辑告警规则
        CUSTOM_HEADERS.put(HEADER_X_Disable_Provenance, null);
    }

    private EagleClient eagleClient;

    public EagleAlertRuleApi(EagleClient eagleClient) {
        this.eagleClient = eagleClient;
    }

    public String createAlertRule(AlertRule createRequest) {
        AlertRule response = null;
        response = eagleClient.doRequest(HttpMethod.POST, ALERT_RULE_URL, CUSTOM_HEADERS, createRequest,
            new TypeReference<AlertRule>() {});
        return response != null ? response.getUid() : null;
    }

    public boolean updateAlertRule(AlertRule updateRequest) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", updateRequest.getUid());
        String updatePath = UriComponentsBuilder.fromPath(ALERT_RULE_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            AlertRule response = eagleClient.doRequest(HttpMethod.PUT, updatePath, CUSTOM_HEADERS, updateRequest,
                new TypeReference<AlertRule>() {});
            return response != null;
        } catch (EagleClientNotExistsException e) {
            return false;
        }
    }

    public boolean deleteAlertRule(String uid) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String deletePath = UriComponentsBuilder.fromPath(ALERT_RULE_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            eagleClient.doRequest(HttpMethod.DELETE, deletePath, CUSTOM_HEADERS, new TypeReference<Folder>() {});
            return true;
        } catch (EagleClientNotExistsException e) {
            return false;
        }
    }

    public AlertRule getAlertRuleByUid(String uid) {
        if(StringUtils.isBlank(uid)) {
            return null;
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String path = UriComponentsBuilder.fromPath(ALERT_RULE_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            return eagleClient.doRequest(HttpMethod.GET, path, new TypeReference<AlertRule>() {});
        } catch (EagleClientNotExistsException e) {
            return null;
        }
    }

    public List<AlertRule> getAllAlertRule() {
        return eagleClient.doRequest(HttpMethod.GET, ALERT_RULE_URL, new TypeReference<List<AlertRule>>() {});
    }

    public AlertRuleGroup updateRuleGroup(AlertRuleGroup updateRequest) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("folderUid", updateRequest.getFolderUid());
        uriVariables.put("ruleGroupTitle", updateRequest.getTitle());
        String updatePath = UriComponentsBuilder.fromPath(RULE_GROUP_URL).buildAndExpand(uriVariables).encode().toUriString();
        return eagleClient.doRequest(HttpMethod.PUT, updatePath, CUSTOM_HEADERS, updateRequest,
            new TypeReference<AlertRuleGroup>() {});
    }

    public AlertRuleGroup getRuleGroup(String folderUid, String ruleGroupTitle) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("folderUid", folderUid);
        uriVariables.put("ruleGroupTitle", ruleGroupTitle);
        String updatePath = UriComponentsBuilder.fromPath(RULE_GROUP_URL).buildAndExpand(uriVariables).toUriString();
        try {
            return eagleClient.doRequest(HttpMethod.GET, updatePath, new TypeReference<AlertRuleGroup>() {});
        } catch (EagleClientNotExistsException e) {
            return null;
        }
    }


}
