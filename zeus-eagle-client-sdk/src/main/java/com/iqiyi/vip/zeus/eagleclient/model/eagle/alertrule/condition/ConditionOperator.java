package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2023/11/22 16:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConditionOperator {

    private static final String WHEN_TYPE = "when";

    private static final String AND_TYPE = "and";

    private static final String OR_TYPE = "or";

    private String type;

    public static ConditionOperator when() {
        return new ConditionOperator(WHEN_TYPE);
    }

    public static ConditionOperator and() {
        return new ConditionOperator(AND_TYPE);
    }

    public static ConditionOperator or() {
        return new ConditionOperator(OR_TYPE);
    }

}
