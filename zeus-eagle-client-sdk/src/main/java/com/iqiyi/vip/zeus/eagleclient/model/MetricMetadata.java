package com.iqiyi.vip.zeus.eagleclient.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guojing
 * @date: 2024/3/19 20:50
 */
@Data
@NoArgsConstructor
public class MetricMetadata {

    private String name;
    /**
     * metric类型: counter, gauge, histogram, summary
     */
    private String type;

    public MetricMetadata(String name, String type) {
        this.name = name;
        this.type = type;
    }
}
