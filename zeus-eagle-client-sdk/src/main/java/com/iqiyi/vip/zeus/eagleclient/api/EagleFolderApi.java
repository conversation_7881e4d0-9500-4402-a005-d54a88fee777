package com.iqiyi.vip.zeus.eagleclient.api;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientException;
import com.iqiyi.vip.zeus.eagleclient.exception.EagleClientNotExistsException;
import com.iqiyi.vip.zeus.eagleclient.model.FolderSearchResult;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Folder;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.FolderSimpleInfo;
import com.iqiyi.vip.zeus.eagleclient.request.CreateFolderRequest;
import com.iqiyi.vip.zeus.eagleclient.request.UpdateFolderRequest;
import com.iqiyi.vip.zeus.eagleclient.response.OperateResponse;

/**
 * Grafana Folder操作API
 * @author: guojing
 * @date: 2023/11/15 15:15
 * <a href="https://grafana.com/docs/grafana/latest/developers/http_api/dashboard/">Grafana Folder API</a>
 */
@Slf4j
public class EagleFolderApi {

    private static final String SEARCH_FOLDER_URL = "/api/search";

    private static final String FOLDER_URL = "/api/folders";
    private static final String FOLDER_BY_UID_URL = "/api/folders/{uid}";

    private EagleClient eagleClient;

    public EagleFolderApi(EagleClient eagleClient) {
        this.eagleClient = eagleClient;
    }

    public Folder createFolder(String title) {
        CreateFolderRequest createRequest = new CreateFolderRequest(title);
        return eagleClient.doRequest(HttpMethod.POST, FOLDER_URL, createRequest, new TypeReference<Folder>() {});
    }

    public Folder updateFolder(UpdateFolderRequest updateRequest) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", updateRequest.getUid());
        String updatePath = UriComponentsBuilder.fromPath(FOLDER_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        return eagleClient.doRequest(HttpMethod.PUT, updatePath, updateRequest, new TypeReference<Folder>() {});
    }

    /**
     * 会删除Folder下的所有Dashboard和Alert
     * @param uid
     */
    public boolean deleteFolder(String uid) {
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String deletePath = UriComponentsBuilder.fromPath(FOLDER_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            eagleClient.doRequest(HttpMethod.DELETE, deletePath, new TypeReference<OperateResponse>() {});
            return true;
        } catch (EagleClientException e) {
            return false;
        }
    }

    public Folder getFolderByUid(String uid) {
        if(StringUtils.isBlank(uid)) {
            return null;
        }
        Map<String, Object> uriVariables = new HashMap<>();
        uriVariables.put("uid", uid);
        String path = UriComponentsBuilder.fromPath(FOLDER_BY_UID_URL).buildAndExpand(uriVariables).toUriString();
        try {
            return eagleClient.doRequest(HttpMethod.GET, path, new TypeReference<Folder>() {});
        } catch (EagleClientNotExistsException e) {
            return null;
        }
    }

    /**
     * 根据folder title模糊查询Folder列表
     * @param title
     */
    public List<FolderSearchResult> searchFolderByTitle(String title) {
        String requestPath = UriComponentsBuilder.fromPath(SEARCH_FOLDER_URL)
            .queryParam("type", "dash-folder")
            .queryParam("query", title)
            .toUriString();
        return eagleClient.doRequest(HttpMethod.GET, requestPath, new TypeReference<List<FolderSearchResult>>() {});
    }

    public List<FolderSimpleInfo> getAllFolder() {
        return eagleClient.doRequest(HttpMethod.GET, FOLDER_URL, new TypeReference<List<FolderSimpleInfo>>() {});
    }

}
