package com.iqiyi.vip.zeus.eagleclient;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.exception.DevOpsClientException;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

/**
 * @author: guojing
 * @date: 2024/4/3 10:36
 */
@Slf4j
public class EagleCenterClient {

    private String serverDomain;

    private RestTemplate restTemplate;

    public EagleCenterClient(RestTemplate restTemplate, String serverDomain) {
        this.restTemplate = restTemplate;
        this.serverDomain = serverDomain;
    }

    public <P, R> BaseResponse<R> doPostByJson(String path, P body, ParameterizedTypeReference<BaseResponse<R>> returnType) {
        String requestUrl = serverDomain + path;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<P> requestEntity = new HttpEntity<>(body, headers);

        ResponseEntity<BaseResponse<R>> responseEntity = null;
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("eagle center client doPostByJson request start, url: {}, body: {}", requestUrl, JsonUtils.toJsonString(body));
            responseEntity = restTemplate.exchange(requestUrl, HttpMethod.POST, requestEntity, returnType);
            log.info("eagle center client doPostByJson request end, url: {}, cost: {}, response: {}", requestUrl, stopWatch.getTime(), JsonUtils.toJsonString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (RestClientException e) {
            log.error("eagle center client doPostByJson request error, url: {}, cost: {}, exceptionMsg: {}", requestUrl, stopWatch.getTime(), e.getMessage(), e);
            throw DevOpsClientException.newInvokeException();
        }
    }

    public <T> BaseResponse<T> doGet(String path, Map<String, Object> requestParam, ParameterizedTypeReference<BaseResponse<T>> returnType) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        if (MapUtils.isNotEmpty(requestParam)) {
            requestParam.forEach(builder::queryParam);
        }
        String requestUrl = builder.build().toUriString();
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("devops client doGet request start, url: {}", requestUrl);
            ResponseEntity<BaseResponse<T>> responseEntity = restTemplate.exchange(requestUrl, HttpMethod.GET, null, returnType);
            log.info("devops client doGet request end, url: {}, cost: {}, response: {}", requestUrl, stopWatch.getTime(), JsonUtils.toJsonString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (RestClientException e) {
            log.error("devops client doGet request error, url: {}, cost: {}, exceptionMsg: {}", requestUrl, stopWatch.getTime(), e.getMessage(), e);
            throw DevOpsClientException.newInvokeException();
        }
    }

}
