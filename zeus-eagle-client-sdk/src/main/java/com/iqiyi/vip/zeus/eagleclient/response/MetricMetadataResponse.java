package com.iqiyi.vip.zeus.eagleclient.response;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: guojing
 * @date: 2024/1/30 22:41
 */
@Data
public class MetricMetadataResponse<T> {

    private static final String SUCCESS = "success";

    private String status;
    private T data;

    public boolean returnSuccess() {
        return SUCCESS.equals(status);
    }

    public boolean returnFail() {
        return StringUtils.isBlank(status) || !SUCCESS.equals(status);
    }

}
