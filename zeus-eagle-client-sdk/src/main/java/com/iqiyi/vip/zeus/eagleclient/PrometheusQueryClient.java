package com.iqiyi.vip.zeus.eagleclient;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

import com.iqiyi.vip.zeus.eagleclient.exception.DevOpsClientException;
import com.iqiyi.vip.zeus.eagleclient.response.MetricMetadataResponse;
import com.iqiyi.vip.zeus.eagleclient.response.prometheus.PrometheusQueryResp;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

/**
 * 请求Prometheus 查询指标数据代理类
 *
 * @author: guojing
 */
@Slf4j
public class PrometheusQueryClient {

    private RestTemplate restTemplate;

    public PrometheusQueryClient(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public MetricMetadataResponse<PrometheusQueryResp> doPost(String serverDomain, String path, Map<String, String> requestParam) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(serverDomain).path(path);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
        formData.setAll(requestParam);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);
        String requestUrl = builder.build().toUriString();
        StopWatch stopWatch = StopWatch.createStarted();
        try {
            log.info("PrometheusDataClient doPost request start, url: {}, params:{}", requestUrl, JsonUtils.toJsonString(requestParam));
            ResponseEntity<MetricMetadataResponse<PrometheusQueryResp>> responseEntity = restTemplate.exchange(
                requestUrl,
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<MetricMetadataResponse<PrometheusQueryResp>>() {});
            log.info("PrometheusDataClient doPost request end, url: {}, cost: {}, response: {}", requestUrl,
                    stopWatch.getTime(), JsonUtils.toJsonString(responseEntity.getBody()));
            return responseEntity.getBody();
        } catch (RestClientException e) {
            log.error("PrometheusDataClient doPost request error, url: {}, cost: {}, exceptionMsg: {}", requestUrl,
                    stopWatch.getTime(), e.getMessage(), e);
            throw DevOpsClientException.newInvokeException();
        }
    }

    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

}
