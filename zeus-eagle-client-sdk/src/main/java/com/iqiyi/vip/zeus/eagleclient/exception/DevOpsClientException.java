package com.iqiyi.vip.zeus.eagleclient.exception;

import lombok.Getter;
import lombok.ToString;

import com.iqiyi.vip.zeus.eagleclient.CodeEnum;

/**
 * @author: guojing
 * @date: 2023/11/24 18:28
 */
@ToString(callSuper = true)
@Getter
public class DevOpsClientException extends RuntimeException {

    private String code;

    public DevOpsClientException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    public DevOpsClientException(CodeEnum codeEnum) {
        super(codeEnum.getMsg());
        this.code = codeEnum.getCode();
    }


    public static DevOpsClientException newInvokeException() {
        return new DevOpsClientException(CodeEnum.OUTER_SERVICE_ERROR);
    }

    public static DevOpsClientException newInvokeException(String errorMsg) {
        return new DevOpsClientException(CodeEnum.OUTER_SERVICE_ERROR.getCode(), errorMsg);
    }

    public static DevOpsClientException newException(String code, String errorMsg) {
        return new DevOpsClientException(code, errorMsg);
    }

}
