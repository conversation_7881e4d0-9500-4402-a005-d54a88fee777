package com.iqiyi.vip.zeus.eagleclient.response;

import lombok.ToString;

import com.iqiyi.vip.zeus.eagleclient.CodeEnum;

/**
 * @author: guojing
 * @date: 2023/11/14 14:53
 */
@ToString
public class BaseResponse<T> {

    private String code;
    private String msg;
    private String message;
    private T data;
    private T dataList;


    public BaseResponse<T> setData(T data) {
        this.data = data;
        return this;
    }

    public BaseResponse<T> setDataList(T dataList) {
        this.dataList = dataList;
        return this;
    }

    public T getData() {
        return data;
    }

    public T getDataList() {
        return dataList;
    }

    public BaseResponse() {
    }

    public BaseResponse(CodeEnum responseCode) {
        this.code = responseCode.getCode();
        this.msg = responseCode.getMsg();
        this.message = responseCode.getMsg();
    }

    public BaseResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
        this.message = msg;
    }

    public static <T> BaseResponse<T> create(CodeEnum responseCode) {
        return new BaseResponse<>(responseCode);
    }

    public static <T> BaseResponse<T> create(String code, String msg) {
        return new BaseResponse<>(code, msg);
    }

    public static <T> BaseResponse<T> createParamError(String msg) {
        return new BaseResponse<>(CodeEnum.PARAM_ERROR.getCode(), msg);
    }

    public static <T> BaseResponse<T> createSystemError(String msg) {
        return new BaseResponse<>(CodeEnum.SYSTEM_ERROR.getCode(), msg);
    }

    public static <T> BaseResponse<T> createSuccess(T data) {
        BaseResponse<T> response = new BaseResponse<>(CodeEnum.SUCCESS);
        response.setData(data);
        return response;
    }

    public static <T> BaseResponse<T> createSuccessList(T data) {
        BaseResponse<T> response = new BaseResponse<>(CodeEnum.SUCCESS);
        response.setDataList(data);
        return response;
    }

    public boolean failure() {
        return !success();
    }

    public boolean success() {
        return CodeEnum.isSuccess(code);
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg != null ? msg : message;
    }

    public String getMessage() {
        return message != null ? message : msg;
    }
}
