package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: guojing
 * @date: 2023/11/21 17:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRuleQuery {

    private static final String CONDITION_DATASOURCE_UID = "__expr__";

    private String refId;
    private String queryType;
    private AlertQueryRelativeTimeRange relativeTimeRange;
    private String datasourceUid;
    private Map<String, Object> model;

    public static AlertRuleQuery newQueryInstance(String refId, String datasourceUid) {
        return AlertRuleQuery.builder()
            .refId(refId)
            .queryType("")
            .datasourceUid(datasourceUid)
            .relativeTimeRange(AlertQueryRelativeTimeRange.newQueryDefault())
            .build();
    }

    public static AlertRuleQuery newConditionInstance(String refId) {
        return AlertRuleQuery.builder()
            .refId(refId)
            .queryType("")
            .datasourceUid(CONDITION_DATASOURCE_UID)
            .relativeTimeRange(AlertQueryRelativeTimeRange.newConditionDefault())
            .build();
    }

}
