package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * @author: guojing
 * @date: 2023/11/22 16:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConditionReducer {

    private List<String> params;
    /**
     * 聚合函数
     */
    private String type;

    public static ConditionReducer newAvgInstance() {
        return new ConditionReducer(Collections.emptyList(), "avg");
    }

}
