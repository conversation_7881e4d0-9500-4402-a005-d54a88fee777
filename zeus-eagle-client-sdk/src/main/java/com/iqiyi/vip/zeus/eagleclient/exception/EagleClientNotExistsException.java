package com.iqiyi.vip.zeus.eagleclient.exception;

import lombok.Getter;
import lombok.ToString;

/**
 * @author: guojing
 * @date: 2023/11/16 15:12
 */
@ToString(callSuper = true)
@Getter
public class EagleClientNotExistsException extends EagleClientException {


    public EagleClientNotExistsException(String code, String msg) {
        super(code, msg);
    }

    public static EagleClientNotExistsException newException(String code, String errorMsg) {
        return new EagleClientNotExistsException(code, errorMsg);
    }
}
