package com.iqiyi.vip.zeus.eagleclient.api;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.iqiyi.vip.zeus.eagleclient.DevOpsClient;
import com.iqiyi.vip.zeus.eagleclient.exception.DevOpsClientException;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Project;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Server;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;
import com.iqiyi.vip.zeus.eagleclient.response.BaseResponse;

/**
 * @author: guojing
 * @date: 2023/11/24 16:41
 */
public class DevOpsApi {

    /**
     * 父团队编码
     */
    private static final String PARAM_PARENT_TEAM_CODE = "parentTeamCode";
    private static final String PARAM_TEAM_CODE = "teamCode";
    /**
     * 团队类型：真实团队、虚拟团队
     */
    private static final String PARAM_TEAM_TYPE = "type";
    /**
     * 真实团队
     */
    private static final int REAL_TEAM_TYPE_VALUE = 1;
    /**
     * 虚拟团队
     */
    private static final int VIRTUAL_TEAM_TYPE_VALUE = 0;

    //团队列表接口
    private static final String ALL_TEAM_URL = "/vip-devops/api/v2/allTeam";
    //系统列表接口
    private static final String SYSTEM_QUERY_URL = "/vip-devops/api/v2/system/list";
    //服务列表接口
    private static final String SERVER_LIST_URL = "/vip-devops/api/v2/server/list";

    private static final String USER_INFO_URL = "/vip-devops/api/v2/user/userDetails";

    //会员开发团队
    private static final Team vipTeam = Team.buildVipTeam();

    private DevOpsClient devOpsClient;

    public DevOpsApi(DevOpsClient devOpsClient) {
        this.devOpsClient = devOpsClient;
    }

    /**
     * 获取会员研发团队列表
     */
    public List<Team> getVipRdTeams() {
        List<Team> vipRDTeams = searchTeams(Team.VIP_TEAM_CODE, null, REAL_TEAM_TYPE_VALUE);
        if (CollectionUtils.isEmpty(vipRDTeams)) {
            vipRDTeams = Collections.singletonList(vipTeam);
        } else {
            vipRDTeams.add(vipTeam);
        }
        return vipRDTeams;
    }

    /**
     * 获取指定团队下的虚拟团队(小组)
     */
    public List<Team> getVirtualTeams(String parentTeamCode) {
        return searchTeams(parentTeamCode, null, VIRTUAL_TEAM_TYPE_VALUE);
    }

    /**
     * 根据teamCode查询团队信息
     * @param teamCode
     */
    public Team getByTeamCode(String teamCode) {
        if (StringUtils.isEmpty(teamCode)) {
            return null;
        }
        List<Team> teams = searchTeams(null, teamCode, null);
        return CollectionUtils.isNotEmpty(teams) ? teams.get(0) : null;
    }

    /**
     * 获取指定团队下的虚拟团队(小组)
     * @param parentTeamCode 父团队编码
     * @param teamType 团队类型
     */
    public List<Team> searchTeams(String parentTeamCode, String teamCode, Integer teamType) {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put(PARAM_PARENT_TEAM_CODE, parentTeamCode);
        requestParams.put(PARAM_TEAM_CODE, teamCode);
        requestParams.put(PARAM_TEAM_TYPE, teamType);
        BaseResponse<List<Team>> baseResponse = devOpsClient.doGet(ALL_TEAM_URL, requestParams, new ParameterizedTypeReference<BaseResponse<List<Team>>>() {});
        if (baseResponse.failure()) {
            throw DevOpsClientException.newException(baseResponse.getCode(), baseResponse.getMsg());
        }
        return baseResponse.getDataList();
    }

    /**
     * 团队下是否存在指定名称的项目
     * @param name
     * @param teamCode
     */
    public Project existsNameUnderTeam(String name, String teamCode) {
        List<Project> projectList = searchProject(name, teamCode, null);
        if (CollectionUtils.isEmpty(projectList)) {
            return null;
        }
        return projectList.stream().filter(project -> Objects.equals(project.getName(), name)).findFirst().orElse(null);
    }

    /**
     * 获取指定团队下的项目列表
     * @param teamCode 团队编码
     */
    public List<Project> searchProject(String name, String teamCode, String groupTeamCode) {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("name", name);
        requestParams.put("teamCodeList", teamCode);
        requestParams.put("groupTeamCode", groupTeamCode);
        BaseResponse<List<Project>> baseResponse = devOpsClient.doGet(SYSTEM_QUERY_URL, requestParams, new ParameterizedTypeReference<BaseResponse<List<Project>>>() {});
        if (baseResponse.failure()) {
            throw DevOpsClientException.newException(baseResponse.getCode(), baseResponse.getMsg());
        }
        return baseResponse.getDataList();
    }

    /**
     * 获取指定团队下的服务列表
     * @param serverId 服务id
     * @param teamCode 团队编码
     * @param serverName 服务名称
     */
    public List<Server> searchServer(Long serverId, String teamCode, String serverName) {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("id", serverId);
        requestParams.put("teamCode", teamCode);
        requestParams.put("serverName", serverName);
        BaseResponse<List<Server>> baseResponse = devOpsClient.doGet(SERVER_LIST_URL, requestParams, new ParameterizedTypeReference<BaseResponse<List<Server>>>() {});
        if (baseResponse.failure()) {
            throw DevOpsClientException.newException(baseResponse.getCode(), baseResponse.getMsg());
        }
        return baseResponse.getDataList();
    }

    /**
     * 查询用户信息
     * @param oaAccount
     */
    public AuthorityUser oaAccountDetails(String oaAccount) {
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("oaAccount", oaAccount);
        BaseResponse<AuthorityUser> baseResponse = devOpsClient.doGet(USER_INFO_URL, requestParams, new ParameterizedTypeReference<BaseResponse<AuthorityUser>>() {});
        if (baseResponse.failure()) {
            throw DevOpsClientException.newException(baseResponse.getCode(), baseResponse.getMsg());
        }
        return baseResponse.getData();
    }

}
