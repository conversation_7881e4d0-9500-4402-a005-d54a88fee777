package com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.condition;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * @author: guojing
 * @date: 2023/11/22 16:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertRuleConditionModelCondition {

    private static final String TYPE = "query";

    private ConditionEvaluator evaluator;
    /**
     * 操作符
     */
    private ConditionOperator operator;
    private ConditionQuery query;
    private ConditionReducer reducer;
    /**
     * query
     */
    private String type;

    public static AlertRuleConditionModelCondition newCondition(String queryRefId, String operator, List<Float> thresholds) {
        return AlertRuleConditionModelCondition.builder()
            .evaluator(new ConditionEvaluator(thresholds, operator))
            .operator(ConditionOperator.or())
            .query(new ConditionQuery(Collections.singletonList(queryRefId)))
            .reducer(ConditionReducer.newAvgInstance())
            .type(TYPE)
            .build();
    }

}
