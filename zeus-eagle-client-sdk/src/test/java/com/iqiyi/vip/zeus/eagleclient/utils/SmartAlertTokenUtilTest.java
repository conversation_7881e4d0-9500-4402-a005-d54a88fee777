package com.iqiyi.vip.zeus.eagleclient.utils;

import org.junit.Test;

/**
 * @author: guojing
 * @date: 2024/11/15 11:35
 */
public class SmartAlertTokenUtilTest {

    @Test
    public void genAuthToken() {
        String authToken = SmartAlertTokenUtil.genAuthToken("tianyan-order_theme-orderCount_real-platform_name_vip_type_desc-20250312183308");
        System.out.println(authToken);
        String authToken2 = SmartAlertTokenUtil.genAuthToken("tianyan-order_theme-orderCount_real-platform_name_vip_type_desc_renew_type_desc_vip_card_type-20250312185644");
        System.out.println(authToken2);
        String authToken3 = SmartAlertTokenUtil.genAuthToken("tianyan-order_theme-avg_order_price-vip_type_desc_renew_type_desc_vip_card_type-20250408181648");
        System.out.println(authToken3);
    }
}