package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

import com.iqiyi.vip.zeus.eagleclient.EagleCenterClient;
import com.iqiyi.vip.zeus.eagleclient.model.RecordRule;

/**
 * @author: guojing
 * @date: 2024/4/3 11:23
 */
public class PrometheusRecordingRuleApiTest {

    private PrometheusRecordingRuleApi prometheusRecordingRuleApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle-center.online.qiyi.qae";
        RestTemplate restTemplate = new RestTemplate();
        prometheusRecordingRuleApi = new PrometheusRecordingRuleApi(new EagleCenterClient(restTemplate, serverDomain));
    }

    @Test
    public void test() {
        String recordName = "autorenew_renew_log_insert_total_1m";
        String expr = "(sum(increase(autorenew_renew_log_insert_total{application=\"vip-xuanwu\",agreementType=\"1\",payChannel=\"5\"}[1m])) - sum(increase(autorenew_renew_log_insert_total{application=\"vip-xuanwu\",agreementType=\"1\",payChannel=\"5\"}[1m] offset 1m))) / sum(increase(autorenew_renew_log_insert_total{application=\"vip-xuanwu\",agreementType=\"1\",payChannel=\"5\"}[1m] offset 1m)) * 100";
        boolean created = prometheusRecordingRuleApi.batchCreate(Collections.singletonList(new RecordRule(recordName, expr)));
        Assert.assertTrue(created);

        boolean deleted = prometheusRecordingRuleApi.batchDelete(Collections.singletonList(recordName));
        Assert.assertTrue(deleted);
    }
}
