package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.DevOpsClient;
import com.iqiyi.vip.zeus.eagleclient.model.devops.AuthorityUser;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Project;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Server;
import com.iqiyi.vip.zeus.eagleclient.model.devops.Team;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2023/11/25 10:56
 */
public class DevOpsApiTest {

    private DevOpsApi devOpsApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://devops.vip.online.qiyi.qae";
        String token = "3daf9775-2667-4733-a411-4fae622e5c0d";
        RestTemplate restTemplate = new RestTemplate();
        devOpsApi = new DevOpsApi(new DevOpsClient(restTemplate, serverDomain, token));
    }

    @Test
    public void devOpsApiTest() {
        List<Team> vipRdTeams = devOpsApi.getVipRdTeams();
        assertNotNull(vipRdTeams);
        List<Team> virtualTeams = devOpsApi.getVirtualTeams("2363");
        assertNotNull(virtualTeams);

        List<Project> projectList = devOpsApi.searchProject("autorenew", null, null);
        assertNotNull(projectList);

        List<Server> serverList = devOpsApi.searchServer(null, null, "autorenew-api");
        assertNotNull(serverList);
    }

    @Test
    public void oaAccountDetailsTest() {
        String oaAccount = "zhouguojing";
        AuthorityUser authorityUser = devOpsApi.oaAccountDetails(oaAccount);
        assertNotNull(authorityUser);
        assertEquals(authorityUser.getOaAccount(), oaAccount);
    }
}