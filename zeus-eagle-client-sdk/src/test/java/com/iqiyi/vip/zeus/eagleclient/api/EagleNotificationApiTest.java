package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertLevel;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertNoticeType;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertReceiver;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.notification.AlertService;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2023/11/17 14:32
 */
public class EagleNotificationApiTest {

    private EagleNotificationApi eagleNotificationApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle.online.qiyi.qae";
        String serviceAccountToken = "glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693";
        RestTemplate restTemplate = new RestTemplate();
        eagleNotificationApi = new EagleNotificationApi(new EagleClient(restTemplate, serverDomain, serviceAccountToken));
    }

    @Test
    public void testNotificationApi() {
        List<AlertNoticeType> alertNoticeTypeList = eagleNotificationApi.getNoticeType();
        assertNotNull(alertNoticeTypeList);
        System.out.println("noticeTypeList size: " + alertNoticeTypeList.size());

        List<AlertReceiver> alertReceiverList = eagleNotificationApi.getAlerReceiverList();
        assertNotNull(alertReceiverList);
        System.out.println("receiverList size: " + alertReceiverList.size());

        List<AlertService> alertServiceList = eagleNotificationApi.getAlertServiceList();
        assertNotNull(alertServiceList);
        System.out.println("serviceList size: " + alertServiceList.size());

        List<AlertLevel> alertLevelList = eagleNotificationApi.getAlertLevel();
        assertNotNull(alertLevelList);
        System.out.println("alarmLevelList size: " + alertLevelList.size());

    }
}