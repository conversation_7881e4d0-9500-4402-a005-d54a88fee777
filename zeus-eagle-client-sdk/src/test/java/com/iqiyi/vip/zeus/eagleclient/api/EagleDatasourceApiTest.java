package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Datasource;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDatasourceRequest;
import com.iqiyi.vip.zeus.eagleclient.response.CreateDatasourceResponse;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

/**
 * @author: guojing
 * @date: 2023/11/16 16:33
 */
public class EagleDatasourceApiTest {

    private EagleDatasourceApi eagleDatasourceApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle.online.qiyi.qae";
        String serviceAccountToken = "glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693";
        RestTemplate restTemplate = new RestTemplate();
        eagleDatasourceApi = new EagleDatasourceApi(new EagleClient(restTemplate, serverDomain, serviceAccountToken));
    }

    @Test
    public void datasourceTest() {
        String createRequestStr = "{\"name\": \"宙斯MySQL\",\"type\": \"mysql\",\"url\": \"bdrds.viptradezeus.w.qiyi.db:3306\",\"database\": \"vip_zeus\",\"user\": \"vip_zeus\",\"access\": \"proxy\",\"basicAuth\": false,\"secureJsonData\":{\"password\": \"9ycUKdN_Qy-X\"}}";
        String createRequestStr2 = "{\"name\": \"自动续费营销测试MySQL\",\"type\": \"mysql\",\"url\": \"bj.bosstest.w.qiyi.db:6183\",\"database\": \"vip_autorenew_marketing\",\"user\": \"vip_test\",\"access\": \"proxy\",\"basicAuth\": false,\"secureJsonData\":{\"password\": \"rg_z_6UF)w=Y\"}}";
        CreateDatasourceRequest createRequest = JsonUtils.parseObject(createRequestStr, CreateDatasourceRequest.class);
        createRequest.setName("自动续费营销测试MySQL-test");
        CreateDatasourceResponse createResponse = eagleDatasourceApi.createDataSource(createRequest);
        Assert.assertNotNull(createResponse);
        Assert.assertEquals(createRequest.getName(), createResponse.getName());

        CreateDatasourceRequest updateRequest = JsonUtils.parseObject(createRequestStr, CreateDatasourceRequest.class);
        updateRequest.setName("自动续费营销测试MySQL-test-update");
        updateRequest.setUid(createResponse.getDatasource().getUid());
        CreateDatasourceResponse updateResponse = eagleDatasourceApi.updateDataSource(updateRequest);
        Assert.assertNotNull(updateResponse);
        Assert.assertEquals(updateRequest.getName(), updateResponse.getName());

        Datasource datasourceByUid = eagleDatasourceApi.getDataSourceByUid(updateResponse.getDatasource().getUid());
        Assert.assertNotNull(updateResponse);
        Datasource datasourceByName = eagleDatasourceApi.getDataSourceByName(datasourceByUid.getName());
        Assert.assertEquals(datasourceByUid, datasourceByName);

        boolean checkHealth = eagleDatasourceApi.checkHealth(datasourceByUid.getUid());

        List<Datasource> allDatasource = eagleDatasourceApi.getAllDataSource();
        Assert.assertNotNull(allDatasource);

        eagleDatasourceApi.deleteDataSource(createResponse.getDatasource().getUid());
    }

    @Test
    public void healthCheck() {
        boolean checkHealth = eagleDatasourceApi.checkHealth("");
    }
}