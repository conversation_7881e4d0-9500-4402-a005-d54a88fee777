package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.List;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Dashboard;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.dashboard.DashboardSearchResult;
import com.iqiyi.vip.zeus.eagleclient.request.CreateDashboardRequest;
import com.iqiyi.vip.zeus.eagleclient.request.DashboardWithMeta;
import com.iqiyi.vip.zeus.eagleclient.response.CreateDashboardResponse;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2023/11/20 15:36
 */
public class EagleDashboardApiTest {

    private EagleDashboardApi eagleDashboardApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle.online.qiyi.qae";
        String serviceAccountToken = "glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693";
        RestTemplate restTemplate = new RestTemplate();
        eagleDashboardApi = new EagleDashboardApi(new EagleClient(restTemplate, serverDomain, serviceAccountToken));
    }

    @Test
    public void dashboardApiTest() {
        String folderUid = "d664536b-b95e-4dc3-9b7b-b31353771dbf";
        Dashboard dashboard = Dashboard.builder().title("Dashboard-测试").build();
        CreateDashboardRequest createRequest = new CreateDashboardRequest(dashboard, folderUid, "通过单测创建Dashboard-测试");
        CreateDashboardResponse createResponse = eagleDashboardApi.createOrUpdateDashboard(createRequest);
        assertNotNull(createResponse);

        DashboardWithMeta dashboardWithMeta = eagleDashboardApi.getDashboardByUid(createResponse.getUid());
        assertNotNull(dashboardWithMeta);
        Dashboard storedDashboardAfterCreate = dashboardWithMeta.getDashboard();
        assertEquals(dashboard.getTitle(), storedDashboardAfterCreate.getTitle());

        storedDashboardAfterCreate.setTitle("Dashboard-测试-更新");
        CreateDashboardRequest updateRequest = new CreateDashboardRequest(storedDashboardAfterCreate, folderUid, "通过单测更新Dashboard-测试", true);
        CreateDashboardResponse updateResponse = eagleDashboardApi.createOrUpdateDashboard(updateRequest);

        DashboardWithMeta dashboardWithMetaAfterUpdate = eagleDashboardApi.getDashboardByUid(updateResponse.getUid());
        assertNotNull(dashboardWithMetaAfterUpdate);
        Dashboard storedDashboardAfterUpdate = dashboardWithMetaAfterUpdate.getDashboard();
        assertEquals(storedDashboardAfterCreate.getTitle(), storedDashboardAfterUpdate.getTitle());

        List<DashboardSearchResult> allDashboard = eagleDashboardApi.getAllUnderFolder(dashboardWithMetaAfterUpdate.getMeta().getFolderId());
        assertFalse(allDashboard.isEmpty());

        boolean deleted = eagleDashboardApi.deleteDashboard(createResponse.getUid());
        assertTrue(deleted);
        DashboardWithMeta dashboardWithMetaAfterDelete = eagleDashboardApi.getDashboardByUid(createResponse.getUid());
        assertNull(dashboardWithMetaAfterDelete);
    }

    @Test
    public void getDashboardTest() {
        DashboardWithMeta dashboardWithMeta = eagleDashboardApi.getDashboardByUid("b667b031-bd9d-4586-9438-7cb5627402c6");
        assertNotNull(dashboardWithMeta);
    }
}