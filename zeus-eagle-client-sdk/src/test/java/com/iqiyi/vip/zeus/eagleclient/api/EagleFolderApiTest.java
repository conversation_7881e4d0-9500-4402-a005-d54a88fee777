package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.Folder;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.FolderSimpleInfo;
import com.iqiyi.vip.zeus.eagleclient.request.UpdateFolderRequest;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2023/11/17 10:28
 */
public class EagleFolderApiTest {

    private EagleFolderApi eagleFolderApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle.online.qiyi.qae";
        String serviceAccountToken = "glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693";
        RestTemplate restTemplate = new RestTemplate();
        eagleFolderApi = new EagleFolderApi(new EagleClient(restTemplate, serverDomain, serviceAccountToken));
    }

    @Test
    public void folderTest() {
        String createFolderTitle = "宙斯-Folder-test";
        Folder createdFolder = eagleFolderApi.createFolder(createFolderTitle);
        assertNotNull(createdFolder);
        assertEquals(createFolderTitle, createdFolder.getTitle());

        String updateFolderTitle = createFolderTitle + "-update";
        UpdateFolderRequest updateFolderRequest = new UpdateFolderRequest(createdFolder.getUid(), updateFolderTitle);
        Folder updateFolder = eagleFolderApi.updateFolder(updateFolderRequest);
        assertNotNull(updateFolder);
        assertEquals(updateFolderTitle, updateFolder.getTitle());

        Folder storedFolder = eagleFolderApi.getFolderByUid(createdFolder.getUid());
        assertNotNull(storedFolder);
        assertEquals(updateFolder, storedFolder);

        List<FolderSimpleInfo> allFolderList = eagleFolderApi.getAllFolder();
        assertNotNull(allFolderList);
        Optional<FolderSimpleInfo> searchResult = allFolderList.stream()
            .filter(folderSimpleInfo -> Objects.equals(folderSimpleInfo.getTitle(), updateFolderTitle))
            .findFirst();
        assertTrue(searchResult.isPresent());

        boolean deleted = eagleFolderApi.deleteFolder(updateFolder.getUid());
        assertTrue(deleted);
    }
}