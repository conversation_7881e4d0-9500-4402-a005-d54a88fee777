package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import com.iqiyi.vip.zeus.eagleclient.SmartAlertClient;

import static org.junit.Assert.assertNull;

/**
 * @author: guojing
 * @date: 2024/11/27 18:09
 */
public class SmartAlertApiTest {

    SmartAlertApi smartAlertApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://normshield.qiyi.domain/";
        RestTemplate restTemplate = new RestTemplate();
        smartAlertApi = new SmartAlertApi(new SmartAlertClient(restTemplate, serverDomain));
    }

    @Test
    public void test() {
        String rule = "xuanwu-smart-alert-demo";
        String ruleConfig = smartAlertApi.getRuleConfig(rule);
        System.out.println(ruleConfig);
        smartAlertApi.deleteRule(rule);
        ruleConfig = smartAlertApi.getRuleConfig(rule);
        assertNull(ruleConfig);
    }

    @Test
    public void deleteRule() {
        smartAlertApi.deleteRule("zhinenggaojingceshi-aaa_811208-vipType");
    }
}