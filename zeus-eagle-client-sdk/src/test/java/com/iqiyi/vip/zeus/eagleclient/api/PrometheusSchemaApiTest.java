package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Optional;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.MetricMetadata;

/**
 * @author: guojing
 * @date: 2024/1/31 20:18
 */
public class PrometheusSchemaApiTest {

    private PrometheusSchemaApi prometheusSchemaApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle.online.qiyi.qae";
        String serviceAccountToken = "glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693";
        RestTemplate restTemplate = new RestTemplate();
        prometheusSchemaApi = new PrometheusSchemaApi(new EagleClient(restTemplate, serverDomain, serviceAccountToken));
    }

    @Test
    public void test() {
        String prometheusDatasourceUid = "6hxAJm0Gz";
        String metric = "autorenew_renew_log_insert_total";
        String vipTypeLabel = "vipType";
        List<MetricMetadata> allMetrics = prometheusSchemaApi.getAllMetricsByUid(prometheusDatasourceUid);
        Assert.assertNotNull(allMetrics);
        Optional<String> exists = allMetrics.stream().map(MetricMetadata::getName).filter(item -> item.equals(metric)).findFirst();
        Assert.assertTrue(exists.isPresent());
        List<String> metricLabels = prometheusSchemaApi.getMetricLabels(prometheusDatasourceUid, metric);
        Assert.assertNotNull(metricLabels);
        Assert.assertTrue(metricLabels.contains(vipTypeLabel));
        List<String> metricLabelValues = prometheusSchemaApi.getMetricLabelValues(prometheusDatasourceUid, metric, vipTypeLabel);
        Assert.assertNotNull(metricLabelValues);
        System.out.println(metricLabelValues);
    }
}
