package com.iqiyi.vip.zeus.eagleclient.api;

import org.junit.Before;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import com.iqiyi.vip.zeus.eagleclient.EagleClient;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.AlertRule;
import com.iqiyi.vip.zeus.eagleclient.model.eagle.alertrule.AlertRuleGroup;
import com.iqiyi.vip.zeus.eagleclient.utils.JsonUtils;

import static org.junit.Assert.*;

/**
 * @author: guojing
 * @date: 2023/11/22 18:02
 */
public class EagleAlertRuleApiTest {

    private EagleAlertRuleApi eagleAlertRuleApi;

    @Before
    public void setUp() throws Exception {
        String serverDomain = "http://eagle.online.qiyi.qae";
        String serviceAccountToken = "glsa_PJgxy1Kg1ZjzV4XWViusQh3QoSQ35UNx_2e9e5693";
        RestTemplate restTemplate = new RestTemplate();
        eagleAlertRuleApi = new EagleAlertRuleApi(new EagleClient(restTemplate, serverDomain, serviceAccountToken));
    }

    @Test
    public void eagleAlertRuleApiTest() {
        String createJsonStr = "{\"orgID\": 1,\"folderUID\": \"d664536b-b95e-4dc3-9b7b-b31353771dbf\",\"ruleGroup\": \"宙斯-退款错误日志QPS-333\",\"title\": \"宙斯-退款错误日志QPS-333\",\"condition\": \"C\",\"data\": [{\"refId\": \"A\",\"queryType\": \"\",\"relativeTimeRange\": {\"from\": 21600,\"to\": 0},\"datasourceUid\": \"6hxAJm0Gz\",\"model\": {\"datasource\": {\"type\": \"prometheus\",\"uid\": \"6hxAJm0Gz\"},\"editorMode\": \"code\",\"expr\": \" (sum(increase(logback_events_total{ application=\\\"viptrade-refundservice-api\\\", level=\\\"error\\\"}[1m])) by (class))/60\",\"instant\": false,\"interval\": \"30s\",\"intervalMs\": 30000,\"legendFormat\": \"{{cluster}}-{{status}}\",\"maxDataPoints\": 43200,\"range\": true,\"refId\": \"A\"}},{\"refId\": \"B\",\"queryType\": \"\",\"relativeTimeRange\": {\"from\": 0,\"to\": 0},\"datasourceUid\": \"__expr__\",\"model\": {\"conditions\": [{\"evaluator\": {\"params\": [],\"type\": \"gt\"},\"operator\": {\"type\": \"and\"},\"query\": {\"params\": [\"B\"]},\"reducer\": {\"params\": [],\"type\": \"last\"},\"type\": \"query\"}],\"datasource\": {\"type\": \"__expr__\",\"uid\": \"__expr__\"},\"expression\": \"A\",\"intervalMs\": 1000,\"maxDataPoints\": 43200,\"reducer\": \"mean\",\"refId\": \"B\",\"type\": \"reduce\"}},{\"refId\": \"C\",\"queryType\": \"\",\"relativeTimeRange\": {\"from\": 0,\"to\": 0},\"datasourceUid\": \"__expr__\",\"model\": {\"conditions\": [{\"evaluator\": {\"params\": [1],\"type\": \"gt\"},\"operator\": {\"type\": \"and\"},\"query\": {\"params\": [\"C\"]},\"reducer\": {\"params\": [],\"type\": \"last\"},\"type\": \"query\"}],\"datasource\": {\"type\": \"__expr__\",\"uid\": \"__expr__\"},\"expression\": \"B\",\"intervalMs\": 1000,\"maxDataPoints\": 43200,\"refId\": \"C\",\"type\": \"threshold\"}}],\"updated\": \"2023-11-23T14:50:10+08:00\",\"noDataState\": \"OK\",\"execErrState\": \"Error\",\"for\": \"5m\",\"annotations\": {\"__dashboardUid__\": \"b667b031-bd9d-4586-9438-7cb5627402c6\",\"__panelId__\": \"3\",\"description\": \"\",\"runbook_url\": \"\",\"summary\": \"宙斯-退款错误日志QPS超过阈值\"},\"isPaused\": false,\"service\": \"viptrade-refundservice-api\",\"level\": \"other\",\"ext\": {\"notifyChannels\": [{\"uid\": \"feishu\"}],\"receivers\": [{\"uid\": \"zhouguojing\"}]}}";
        AlertRule createRequest = JsonUtils.parseObject(createJsonStr, AlertRule.class);
        String alertRuleUid = eagleAlertRuleApi.createAlertRule(createRequest);
        assertNotNull(alertRuleUid);

        AlertRule alertRule = eagleAlertRuleApi.getAlertRuleByUid(alertRuleUid);
        alertRule.setTitle(alertRule.getTitle() + "-update");
        boolean updated = eagleAlertRuleApi.updateAlertRule(alertRule);
        assertTrue(updated);


        AlertRuleGroup updateAlertRuleGroupRequest = AlertRuleGroup.builder()
            .title(alertRule.getRuleGroup())
            .folderUid(alertRule.getFolderUID())
            .interval(30)
            .build();
        AlertRuleGroup updateAlertRuleGroupResult = eagleAlertRuleApi.updateRuleGroup(updateAlertRuleGroupRequest);
        assertNotNull(updateAlertRuleGroupResult);
        assertEquals(updateAlertRuleGroupRequest.getInterval(), updateAlertRuleGroupResult.getInterval());
        AlertRuleGroup selectRuleGroupResult = eagleAlertRuleApi.getRuleGroup(updateAlertRuleGroupRequest.getFolderUid(), updateAlertRuleGroupRequest.getTitle());
        assertNotNull(selectRuleGroupResult);


        AlertRule getResult = eagleAlertRuleApi.getAlertRuleByUid(alertRule.getUid());
        assertNotNull(getResult);
        assertEquals(alertRule.getUid(), getResult.getUid());


        boolean deleted = eagleAlertRuleApi.deleteAlertRule(alertRule.getUid());
        assertTrue(deleted);
    }

    @Test
    public void updateAlertRule() {
        AlertRule getResult = eagleAlertRuleApi.getAlertRuleByUid("d991acf3-5bf8-4816-ab89-680193d82dfb");
        assertNotNull(getResult);
        getResult.setTitle(getResult.getTitle() + "-update");
        boolean updated = eagleAlertRuleApi.updateAlertRule(getResult);
        assertTrue(updated);
    }

    @Test
    public void ruleGroupTest() {
        AlertRuleGroup updateAlertRuleGroupRequest = AlertRuleGroup.builder()
            .title("宙斯-退款错误日志QPS-222")
            .folderUid("d664536b-b95e-4dc3-9b7b-b31353771dbf")
            .interval(30)
            .build();
        AlertRuleGroup updateAlertRuleGroupResult = eagleAlertRuleApi.updateRuleGroup(updateAlertRuleGroupRequest);
        assertNotNull(updateAlertRuleGroupResult);
        assertEquals(updateAlertRuleGroupRequest.getInterval(), updateAlertRuleGroupResult.getInterval());
        AlertRuleGroup selectRuleGroupResult = eagleAlertRuleApi.getRuleGroup(updateAlertRuleGroupRequest.getFolderUid(), updateAlertRuleGroupRequest.getTitle());
        assertNotNull(selectRuleGroupResult);
    }
}