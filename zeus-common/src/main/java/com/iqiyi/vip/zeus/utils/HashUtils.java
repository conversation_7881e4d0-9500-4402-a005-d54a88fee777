package com.iqiyi.vip.zeus.utils;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @author: guojing
 * @date: 2024/4/9 18:50
 */
public class HashUtils {

    /**
     * 计算字符串的SHA-256哈希值，并截取前32位
     * @param string
     */
    public static String shortSHA256(String string) {
        String sha256 = SHA256(string);
        if (StringUtils.isBlank(sha256)) {
            return null;
        }
        return sha256.substring(0, 16);
    }

    /**
     * 计算字符串的SHA-256哈希值
     * @param string
     */
    public static String SHA256(String string) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(string.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error computing hash with SHA-256", e);
        }
    }

}
