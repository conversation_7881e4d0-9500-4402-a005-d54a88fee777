package com.iqiyi.vip.zeus.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * @author: guojing
 * @date: 2025/5/26 15:52
 */
public class CommonDateUtils {

    public static String todayStr() {
        LocalDate now = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return now.format(formatter);
    }

    public static String yesterdayStr() {
        return nDaysAgo(1);
    }

    public static String nDaysAgo(int days) {
        LocalDate now = LocalDate.now();
        LocalDate sevenDaysAgo = now.minusDays(days);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return sevenDaysAgo.format(formatter);
    }

    public static String secondsToDateStr(long unixSeconds) {
        LocalDate date = Instant.ofEpochSecond(unixSeconds)
            .atZone(ZoneId.of("Asia/Shanghai"))
            .toLocalDate();

        return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

}
