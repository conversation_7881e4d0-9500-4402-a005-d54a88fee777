# Cursor Rules 目录说明

本目录包含了VIP玄武(Zeus)项目的Cursor AI编程助手规则文件，帮助AI更好地理解项目结构和开发规范。

## 规则文件说明

### 1. main-rules.md ⭐
- **用途**: Cursor AI的主要指导规则
- **内容**: 项目身份识别、核心原则、代码生成规则
- **适用**: AI助手的核心指导文件，包含最重要的规则

### 2. project-structure.md
- **用途**: 项目整体结构和模块说明
- **内容**: Maven多模块结构、技术栈、包结构规范
- **适用**: 项目架构理解、模块职责划分

### 3. coding-standards.md  
- **用途**: 编码规范和最佳实践
- **内容**: Java编码规范、Spring Boot最佳实践、前端规范
- **适用**: 代码编写、重构、审查

### 4. tianwang-system.md
- **用途**: 天网稽核系统专门规则
- **内容**: 业务模块、前后端集成、权限控制、数据展示
- **适用**: 天网系统功能开发和维护

### 5. database-entities.md
- **用途**: 数据库设计和实体规范
- **内容**: 表结构设计、实体类规范、Repository层规范
- **适用**: 数据库设计、实体类编写、数据访问层开发

### 6. api-design-standards.md
- **用途**: API设计规范和标准
- **内容**: RESTful设计、响应格式、错误处理、接口文档
- **适用**: API开发、接口设计、文档编写

### 7. development-guidelines.md
- **用途**: 开发指南和工作流程
- **内容**: 环境设置、Git流程、调试排查、测试部署
- **适用**: 日常开发流程、问题排查、发布管理

## 使用说明

### Cursor AI助手
这些规则文件会被Cursor AI助手自动加载，用于：
- **代码理解**: 更准确理解项目结构和业务逻辑
- **代码生成**: 生成符合项目规范的代码
- **重构建议**: 提供符合最佳实践的重构建议
- **问题解答**: 回答项目相关的技术问题

### 开发团队
团队成员可以通过这些文件：
- **快速上手**: 新成员快速了解项目结构
- **规范参考**: 开发过程中参考编码规范
- **最佳实践**: 学习和应用项目最佳实践
- **统一标准**: 保持团队开发标准一致

## 维护更新

### 更新时机
- 项目结构发生重大变化
- 技术栈升级或更换
- 编码规范调整
- 新增重要业务模块

### 更新流程
1. 识别需要更新的规则文件
2. 修改相应的规则内容
3. 提交代码审查
4. 同步更新项目文档

### 版本控制
- 规则文件与项目代码一起进行版本控制
- 重大变更需要在CHANGELOG中记录
- 向团队成员通知重要规则变更

## 项目概览

### 技术架构
- **后端**: Spring Boot 2.7.6 + MyBatis + Maven多模块
- **前端**: Vue 3 + Thymeleaf + Ant Design Vue
- **数据库**: MySQL, TiDB, ClickHouse, StarRocks, Hive, Prometheus
- **部署**: Docker + K8s

### 核心业务
- **订单监控**: 爱奇艺VIP订单数据监控和导出
- **数据源管理**: 多种数据源的统一管理
- **天网稽核**: 业务数据稽核和监控系统
- **告警规则**: 智能告警规则配置和管理

### 团队角色
- **研发工程师**: 拥有完整系统权限
- **产品经理**: 订阅和查看相关数据
- **运营人员**: 监控和分析业务数据

## 快速链接

- [项目结构说明](./project-structure.md)
- [编码规范](./coding-standards.md)  
- [天网系统规则](./tianwang-system.md)
- [开发指南](./development-guidelines.md)

---

**维护者**: VIP玄武开发团队
**最后更新**: 2024年1月
**联系方式**: [团队邮箱或内部联系方式]
