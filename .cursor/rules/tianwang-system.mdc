---
description: 天网稽核系统专门规则
globs:
  - "**/TianWangController.java"
  - "**/index.html"
  - "**/main.js"
  - "**/main.css"
alwaysApply: true
priority: 4
---

# 天网稽核系统规则

## 系统概述

天网是VIP玄武项目的核心稽核系统，用于通过数据异常发现业务问题。系统采用Thymeleaf + Vue 3的前后端集成架构。

## 核心业务模块

### 1. 数据源管理
- **功能**: 管理多种数据源连接配置
- **支持类型**: MySQL, TiDB, ClickHouse, StarRocks, Hive, Prometheus
- **权限**: 仅研发人员可管理
- **访问路径**: `/guard/datasources`

### 2. 稽核项管理
- **功能**: 配置稽核规则和SQL语句
- **稽核维度**: 准确性、一致性、时效性、合理性、完整性
- **权限**: 仅研发人员可管理
- **访问路径**: `/guard/items`

### 3. 用户订阅管理
- **功能**: 用户自主订阅关注的稽核项
- **权限**: 所有用户可订阅
- **访问路径**: `/guard/subscription`

### 4. 稽核报告查看
- **功能**: 查看稽核执行结果和趋势分析
- **展示方式**: 表格形式，按业务分组
- **权限**: 所有用户可查看
- **访问路径**: `/guard/reports`

### 5. 操作日志
- **功能**: 记录系统操作历史
- **权限**: 所有用户可查看
- **访问路径**: `/guard/logs`

## 前后端集成架构

### Thymeleaf模板引擎
```html
<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="${pageTitle != null ? pageTitle : '天网'}">天网</title>
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
    <script th:inline="javascript">
        window.serverData = {
            activeTab: /*[[${activeTab}]]*/ 'dashboard',
            userRole: /*[[${user?.role}]]*/ 'DEVELOPER',
            userName: /*[[${user?.name}]]*/ '张三',
            userAvatar: /*[[${user?.avatar}]]*/ 'https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32',
            datasources: /*[[${datasources}]]*/ [],
            guardItems: /*[[${guardItems}]]*/ [],
            reportData: /*[[${reportData}]]*/ []
        };
    </script>
    <script th:src="@{/js/main.js}"></script>
</body>
</html>
```

### Vue 3前端逻辑
```javascript
// 从服务器获取数据，提供默认值
const activeTab = ref(window.serverData?.activeTab || 'dashboard');
const userRole = ref(window.serverData?.userRole || 'DEVELOPER');
const userName = ref(window.serverData?.userName || '张三');
const userAvatar = ref(window.serverData?.userAvatar || 'https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32');

// 事件处理方法
const handleTabChange = (tab) => {
    activeTab.value = tab;
};
```

## 用户角色权限控制

### 研发工程师 (DEVELOPER)
- **数据源管理**: 创建、修改、删除数据源
- **稽核项管理**: 创建、修改、删除稽核项
- **订阅管理**: 订阅和取消订阅稽核项
- **报告查看**: 查看所有稽核报告
- **日志查看**: 查看操作日志

### 产品经理 (PRODUCT)
- **订阅管理**: 订阅和取消订阅稽核项
- **报告查看**: 查看订阅的稽核报告
- **日志查看**: 查看操作日志

### 运营人员 (OPERATION)
- **订阅管理**: 订阅和取消订阅稽核项
- **报告查看**: 查看订阅的稽核报告
- **日志查看**: 查看操作日志

## 稽核报告数据展示

### 表格结构
1. **序号列**: 显示行号
2. **稽核项列**: 稽核项名称 + 类型徽章
3. **周同比列**: 周同比变化百分比
4. **日环比列**: 日环比变化百分比
5. **日期列**: 按日期从近到远排列

### 类型徽章设计
```css
.type-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
}

.type-accuracy { background-color: #e6f7ff; color: #1890ff; }
.type-consistency { background-color: #f6ffed; color: #52c41a; }
.type-timeliness { background-color: #fff7e6; color: #fa8c16; }
.type-rationality { background-color: #f9f0ff; color: #722ed1; }
.type-completeness { background-color: #fff1f0; color: #ff4d4f; }
```

### 趋势颜色规则
- **增加**: 红色 (#ff4d4f)
- **减少**: 绿色 (#52c41a)
- **无变化**: 黑色 (#262626)

## 数据源集成规范

### Pilot组件集成
```java
/**
 * Pilot数据源查询服务
 */
@Service
public class PilotDataSourceService {
    
    /**
     * 执行Pilot查询
     */
    public List<Map<String, Object>> executePilotQuery(String sql) {
        // 调用Pilot组件API
    }
}
```

### DMS API集成
```java
/**
 * DMS数据源查询服务
 */
@Service
public class DmsDataSourceService {
    
    /**
     * 执行MySQL/TiDB查询
     */
    public List<Map<String, Object>> executeDmsQuery(String sql, String dataSourceId) {
        // 调用DMS API
    }
}
```

### Prometheus API集成
```java
/**
 * Prometheus数据源查询服务
 */
@Service
public class PrometheusDataSourceService {
    
    /**
     * 执行Prometheus查询
     */
    public List<Map<String, Object>> executePrometheusQuery(String query) {
        // 调用Prometheus API
    }
}
```

## 路由设计规范

### 后端路由
```java
@Controller
@RequestMapping("/tianwang")
public class TianWangController {
    
    @GetMapping("")
    public String index(Model model, @RequestParam(defaultValue = "dashboard") String tab) {
        // 主页
    }
    
    @GetMapping("/datasources")
    public String datasources(Model model) {
        // 数据源管理
    }
    
    @GetMapping("/items")
    public String guardItems(Model model) {
        // 稽核项管理
    }
    
    @GetMapping("/subscription")
    public String subscription(Model model) {
        // 我的订阅
    }
    
    @GetMapping("/reports")
    public String reports(Model model) {
        // 稽核报告
    }
    
    @GetMapping("/logs")
    public String logs(Model model) {
        // 操作日志
    }
}
```

### 前端路由
```javascript
// 页面标签页控制
const activeTab = ref('dashboard');

const handleTabChange = (tab) => {
    activeTab.value = tab;
    // 更新URL参数
    const url = new URL(window.location);
    url.searchParams.set('tab', tab);
    window.history.pushState({}, '', url);
};
```

## 数据传递规范

### 服务器到前端
```java
// Controller中传递数据
model.addAttribute("pageTitle", "天网");
model.addAttribute("activeTab", tab);
model.addAttribute("user", user);
model.addAttribute("datasources", datasources);
model.addAttribute("guardItems", guardItems);
model.addAttribute("reportData", reportData);
```

### 前端数据接收
```javascript
// 前端接收服务器数据
const activeTab = ref(window.serverData?.activeTab || 'dashboard');
const userRole = ref(window.serverData?.userRole || 'DEVELOPER');
const userName = ref(window.serverData?.userName || '张三');
const userAvatar = ref(window.serverData?.userAvatar || 'https://ui-avatars.com/api/?name=张三&background=1890ff&color=fff&size=32');
const datasources = ref(window.serverData?.datasources || []);
const guardItems = ref(window.serverData?.guardItems || []);
const reportData = ref(window.serverData?.reportData || []);
```

## 样式设计规范

### 整体风格
- **主色调**: 蓝色 (#1890ff)
- **辅助色**: 灰色 (#f5f5f5)
- **文字色**: 黑色 (#262626)
- **边框色**: 浅灰色 (#d9d9d9)

### 组件样式
```css
/* 页面容器 */
.page-container {
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* 侧边栏 */
.sidebar {
    width: 200px;
    background-color: white;
    border-right: 1px solid #d9d9d9;
}

/* 主内容区 */
.main-content {
    flex: 1;
    padding: 24px;
    background-color: #f5f5f5;
}

/* 卡片样式 */
.card {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
}
```

## 交互设计规范

### 用户反馈
- **成功操作**: 使用绿色提示
- **错误操作**: 使用红色提示
- **警告信息**: 使用橙色提示
- **信息提示**: 使用蓝色提示

### 加载状态
- **数据加载**: 显示加载动画
- **操作处理**: 显示处理中状态
- **异步操作**: 提供取消功能

### 响应式设计
- **桌面端**: 1200px以上
- **平板端**: 768px - 1199px
- **移动端**: 768px以下

## 性能优化规范

### 前端优化
1. **资源压缩**: 压缩CSS和JS文件
2. **图片优化**: 使用WebP格式
3. **懒加载**: 非关键资源懒加载
4. **缓存策略**: 合理设置缓存头

### 后端优化
1. **数据库优化**: 建立合适的索引
2. **查询优化**: 避免N+1查询
3. **缓存策略**: 使用Redis缓存
4. **异步处理**: 耗时操作异步处理

## 安全规范

### 权限验证
1. **页面权限**: 基于用户角色控制页面访问
2. **数据权限**: 根据用户角色过滤数据
3. **操作权限**: 控制用户可执行的操作

### 数据安全
1. **输入验证**: 验证用户输入
2. **SQL注入**: 使用参数化查询
3. **XSS防护**: 转义用户输入
4. **CSRF防护**: 使用CSRF令牌

## 测试规范

### 单元测试
1. **Controller测试**: 测试页面路由和数据传递
2. **Service测试**: 测试业务逻辑
3. **前端测试**: 测试Vue组件功能

### 集成测试
1. **端到端测试**: 测试完整业务流程
2. **数据源测试**: 测试各种数据源连接
3. **权限测试**: 测试用户权限控制

## 部署规范

### 环境配置
1. **开发环境**: 本地开发，使用H2数据库
2. **测试环境**: 集成测试，使用MySQL测试库
3. **生产环境**: 高可用部署，使用MySQL主从

### 监控告警
1. **应用监控**: Spring Boot Actuator
2. **业务监控**: 稽核执行状态监控
3. **性能监控**: 响应时间和吞吐量监控
4. **错误监控**: 异常和错误日志监控