---
description: 编码规范和最佳实践
globs:
  - "**/*.java"
  - "**/*.js"
  - "**/*.css"
  - "**/*.html"
alwaysApply: true
priority: 3
---

# 编码规范

## Java编码规范

### 命名规范
1. **类名**: PascalCase，如`TianWangController`
2. **方法名**: camelCase，如`getDatasources`
3. **变量名**: camelCase，如`userName`
4. **常量名**: UPPER_SNAKE_CASE，如`MAX_RETRY_COUNT`
5. **包名**: 全小写，如`com.iqiyi.vip.zeus.orderexport.controller`
6. **数据库实体类**: 必须以`PO`结尾，如`BusinessPO`、`GuardItemPO`

### 注释规范
1. **类注释**: 所有Java类必须包含完整的类注释
2. **@author**: 使用git的当前作者名字
3. **@date**: 创建日期，格式为 `yyyy-MM-dd HH:mm:ss`
4. **方法注释**: 所有public方法必须包含方法注释

```java
/**
 * 天网系统主控制器
 * 处理稽核系统的页面路由和数据传递
 * 
 * <AUTHOR>
 * @date yyyy-MM-dd HH:mm:ss
 */
@Controller
@RequestMapping("/tianwang")
public class TianWangController {
    
    /**
     * 获取稽核系统主页
     * 
     * @param model 模板数据模型
     * @param tab 激活的标签页，默认为dashboard
     * @return 返回index模板
     */
    @GetMapping("")
    public String index(Model model, @RequestParam(defaultValue = "dashboard") String tab) {
        // 实现逻辑
    }
}
```

### 数据库实体类规范
1. **目录位置**: 所有数据库实体类必须放在`po`目录下
2. **包命名**: `com.iqiyi.vip.zeus.core.po.guard.*`
3. **类名规范**: 必须以`PO`结尾，如`BusinessPO`、`GuardItemPO`
4. **注解使用**: 使用Lombok的`@Data`和`@EqualsAndHashCode`注解
5. **字段类型**: 使用Java 8时间类型`LocalDateTime`、`LocalDate`

```java
/**
 * 业务信息实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BusinessPO {
    /**
     * 自增主键id
     */
    private Integer id;
    
    /**
     * 业务名称
     */
    private String name;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
```

### Spring Boot最佳实践
1. **配置类**: 使用`@Configuration`和`@Bean`
2. **服务层**: 使用`@Service`和`@Transactional`
3. **数据访问**: 使用`@Repository`和MyBatis
4. **异常处理**: 使用`@ControllerAdvice`
5. **参数验证**: 使用`@Valid`和`@Validated`

### 数据库设计规范
1. **表名**: 使用下划线分隔，如`guard_items`
2. **字段名**: 使用下划线分隔，如`create_time`
3. **主键**: 使用`id`，类型为`BIGINT AUTO_INCREMENT`
4. **通用字段**: 包含`create_time`, `update_time`, `create_by`, `update_by`, `is_deleted`

## 前端编码规范

### Vue 3规范
1. **组件命名**: PascalCase，如`UserDropdown`
2. **事件命名**: camelCase，如`handleTabChange`
3. **Props命名**: camelCase，如`userName`
4. **响应式数据**: 使用`ref`和`reactive`

```javascript
// 正确的Vue 3 Composition API写法
export default {
    setup() {
        const activeTab = ref('dashboard');
        const userName = ref(window.serverData?.userName || '张三');
        
        const handleTabChange = (tab) => {
            activeTab.value = tab;
        };
        
        return {
            activeTab,
            userName,
            handleTabChange
        };
    }
};
```

### CSS规范
1. **类名**: 使用kebab-case，如`.user-dropdown`
2. **ID名**: 使用camelCase，如`#userAvatar`
3. **变量名**: 使用--前缀，如`--primary-color`
4. **嵌套层级**: 不超过3层

```css
/* 正确的CSS写法 */
.user-dropdown {
    position: relative;
    display: inline-block;
}

.user-dropdown__menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-dropdown__item {
    padding: 8px 16px;
    cursor: pointer;
}

.user-dropdown__item:hover {
    background-color: #f5f5f5;
}
```

### HTML规范
1. **标签**: 使用小写，如`<div>`
2. **属性**: 使用双引号，如`class="container"`
3. **缩进**: 使用2个空格
4. **自闭合标签**: 使用`<img />`格式

```html
<!-- 正确的HTML写法 -->
<div class="user-dropdown">
    <img :src="userAvatar" :alt="userName" class="user-avatar" />
    <div class="user-dropdown__menu" v-show="showMenu">
        <div class="user-dropdown__item" @click="handleLogout">退出登录</div>
        <div class="user-dropdown__item" @click="handleFeedback">意见反馈</div>
    </div>
</div>
```

## Thymeleaf模板规范

### 基本语法
1. **命名空间**: 必须包含`xmlns:th="http://www.thymeleaf.org"`
2. **数据绑定**: 使用`th:*`属性
3. **条件判断**: 使用`th:if`和`th:unless`
4. **循环**: 使用`th:each`

```html
<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="${pageTitle != null ? pageTitle : '天网'}">天网</title>
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
    <div th:if="${user != null}">
        <span th:text="${user.name}">用户名</span>
    </div>
    
    <script th:inline="javascript">
        window.serverData = {
            userName: /*[[${user?.name}]]*/ '张三',
            userRole: /*[[${user?.role}]]*/ 'DEVELOPER'
        };
    </script>
</body>
</html>
```

### 静态资源引用
1. **CSS**: 使用`th:href="@{/css/main.css}"`
2. **JS**: 使用`th:src="@{/js/main.js}"`
3. **图片**: 使用`th:src="@{/images/logo.png}"`

## 数据库编码规范

### SQL编写规范
1. **关键字**: 使用大写，如`SELECT`, `FROM`, `WHERE`
2. **表名**: 使用下划线分隔，如`guard_items`
3. **字段名**: 使用下划线分隔，如`create_time`
4. **别名**: 使用有意义的别名，如`ai.id AS audit_item_id`

```sql
-- 正确的SQL写法
SELECT 
    ai.id AS guard_item_id,
    ai.name AS guard_item_name,
    ai.dimension AS guard_dimension,
    ai.create_time AS create_time
FROM guard_items ai
WHERE ai.is_deleted = 0
    AND ai.status = 'ACTIVE'
ORDER BY ai.create_time DESC
LIMIT 10;
```

### MyBatis映射规范
1. **命名空间**: 使用完整类名，如`com.iqiyi.vip.zeus.core.dao.GuardItemMapper`
2. **SQL ID**: 使用camelCase，如`selectGuardItems`
3. **参数映射**: 使用`#{}`语法
4. **结果映射**: 使用`<resultMap>`标签

```xml
<!-- 正确的MyBatis映射写法 -->
<mapper namespace="com.iqiyi.vip.zeus.core.dao.GuardItemMapper">
    <resultMap id="GuardItemResultMap" type="com.iqiyi.vip.zeus.core.model.GuardItem">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="dimension" property="dimension"/>
        <result column="create_time" property="createTime"/>
    </resultMap>
    
    <select id="selectGuardItems" resultMap="GuardItemResultMap">
        SELECT id, name, dimension, create_time
        FROM guard_items
        WHERE is_deleted = 0
        ORDER BY create_time DESC
    </select>
</mapper>
```

## API设计规范

### RESTful API设计
1. **URL设计**: 使用名词，如`/api/tianwang/v1/datasources`
2. **HTTP方法**: GET(查询), POST(创建), PUT(更新), DELETE(删除)
3. **状态码**: 使用标准HTTP状态码
4. **响应格式**: 统一使用JSON格式

### Controller返回值规范
1. **统一返回值**: 所有Controller方法必须返回`BaseResponse<T>`类型
2. **成功响应**: 使用`BaseResponse.createSuccess(data)`或`BaseResponse.createSuccessList(data)`
3. **失败响应**: 使用`BaseResponse.createParamError(message)`或`BaseResponse.createSystemError(message)`
4. **资源不存在**: 使用`BaseResponse.create(CodeEnum.NOT_FOUND)`
5. **禁止使用**: 禁止使用`ResponseEntity<Map<String, Object>>`作为返回值

```java
@RestController
@RequestMapping("/api/tianwang/v1/datasources")
public class DataSourceController {
    
    /**
     * 创建数据源
     */
    @PostMapping
    public BaseResponse<GuardDatasource> create(@RequestBody @Valid GuardDatasource datasource) {
        try {
            int result = guardDatasourceService.create(datasource);
            if (result > 0) {
                return BaseResponse.createSuccess(datasource);
            } else {
                return BaseResponse.createSystemError("创建失败");
            }
        } catch (IllegalArgumentException e) {
            return BaseResponse.createParamError(e.getMessage());
        } catch (Exception e) {
            return BaseResponse.createSystemError("创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询数据源列表
     */
    @GetMapping
    public BaseResponse<List<GuardDatasource>> getDatasources() {
        try {
            List<GuardDatasourcePO> poList = guardDatasourceService.getAllValid();
            List<GuardDatasource> datasources = poList.stream()
                    .map(GuardDatasource::fromPO)
                    .collect(Collectors.toList());
            return BaseResponse.createSuccessList(datasources);
        } catch (Exception e) {
            return BaseResponse.createSystemError("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询数据源
     */
    @GetMapping("/{id}")
    public BaseResponse<GuardDatasource> getById(@PathVariable Integer id) {
        try {
            GuardDatasourcePO po = guardDatasourceService.getById(id);
            if (po == null) {
                return BaseResponse.create(CodeEnum.NOT_FOUND);
            }
            return BaseResponse.createSuccess(GuardDatasource.fromPO(po));
        } catch (Exception e) {
            return BaseResponse.createSystemError("查询失败: " + e.getMessage());
        }
    }
}
```

### 响应格式统一
```json
{
  "code": "A00000",
  "msg": "处理成功",
  "data": { ... }
}
```

**状态码说明：**
- `A00000`: 处理成功
- `Q00301`: 参数错误
- `Q00332`: 系统错误
- `Q00404`: 资源不存在
- `Q00409`: 资源已存在

```java
@RestController
@RequestMapping("/api/tianwang/v1/datasources")
public class DataSourceApiController {
    
    /**
     * 获取数据源列表
     */
    @GetMapping
    public BaseResponse<PageResponse<DataSourceResponse>> getDatasources(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        try {
            // 实现逻辑
            PageResponse<DataSourceResponse> result = datasourceService.getDatasources(page, size);
            return BaseResponse.createSuccess(result);
        } catch (Exception e) {
            return BaseResponse.createSystemError("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建数据源
     */
    @PostMapping
    public BaseResponse<DataSourceResponse> createDatasource(
            @RequestBody @Valid CreateDataSourceRequest request) {
        try {
            // 实现逻辑
            DataSourceResponse result = datasourceService.create(request);
            return BaseResponse.createSuccess(result);
        } catch (IllegalArgumentException e) {
            return BaseResponse.createParamError(e.getMessage());
        } catch (Exception e) {
            return BaseResponse.createSystemError("创建失败: " + e.getMessage());
        }
    }
}
```

### 响应格式规范
**成功响应：**
```json
{
  "code": "A00000",
  "msg": "处理成功",
  "data": {
    "id": 1,
    "name": "MySQL数据源",
    "type": "MYSQL"
  }
}
```

**失败响应：**
```json
{
  "code": "Q00301",
  "msg": "参数错误",
  "data": null
}
```

**资源不存在：**
```json
{
  "code": "Q00404",
  "msg": "资源不存在",
  "data": null
}
```

## 测试规范

### 单元测试
1. **测试类命名**: 以`Test`结尾，如`TianWangControllerTest`
2. **测试方法命名**: 使用`should_期望结果_when_条件`格式
3. **测试覆盖率**: 核心业务逻辑覆盖率不低于80%

```java
@SpringBootTest
@Transactional
class TianWangControllerTest {
    
    @Test
    @DisplayName("应该返回天网主页 - 当访问主页时")
    void shouldReturnIndexPage_whenAccessMainPage() {
        // Given - 准备数据
        
        // When - 执行操作
        
        // Then - 验证结果
    }
}
```

### 集成测试
1. **测试类命名**: 以`IT`结尾，如`TianWangControllerIT`
2. **测试环境**: 使用`@SpringBootTest`和`@AutoConfigureTestDatabase`
3. **数据准备**: 使用`@Sql`注解或测试数据构建器

## 性能优化规范

### 数据库优化
1. **索引设计**: 为常用查询字段建立索引
2. **查询优化**: 避免N+1查询，使用JOIN替代子查询
3. **分页查询**: 使用LIMIT和OFFSET进行分页

### 缓存策略
1. **本地缓存**: 使用Caffeine进行本地缓存
2. **分布式缓存**: 使用Redis进行分布式缓存
3. **缓存更新**: 使用Cache-Aside模式

### 前端优化
1. **资源压缩**: 压缩CSS和JS文件
2. **图片优化**: 使用WebP格式，设置合适的尺寸
3. **懒加载**: 对非关键资源使用懒加载

## 安全规范

### 数据安全
1. **敏感信息**: 密码等敏感信息必须加密存储
2. **SQL注入**: 使用参数化查询，避免字符串拼接
3. **XSS防护**: 对用户输入进行转义处理

### 权限控制
1. **角色权限**: 基于角色的访问控制(RBAC)
2. **接口权限**: 使用`@PreAuthorize`注解
3. **数据权限**: 根据用户角色过滤数据

## 日志规范

### 日志级别
1. **ERROR**: 系统错误，需要立即处理
2. **WARN**: 警告信息，需要关注
3. **INFO**: 重要业务信息
4. **DEBUG**: 调试信息，仅在开发环境使用

### 日志格式
```java
// 正确的日志写法
log.info("用户登录成功，用户ID: {}, 用户名: {}", userId, userName);
log.error("数据源连接失败，数据源ID: {}, 错误信息: {}", dataSourceId, e.getMessage(), e);
```

### 敏感信息处理
1. **密码**: 不记录密码等敏感信息
2. **身份证号**: 脱敏处理，如`123456****5678`
3. **手机号**: 脱敏处理，如`138****5678`