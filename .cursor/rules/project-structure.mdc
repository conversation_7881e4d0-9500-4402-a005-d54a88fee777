---
description: 项目整体结构和模块说明
globs:
  - "**/*.java"
  - "**/*.xml"
  - "**/*.yml"
  - "**/*.properties"
alwaysApply: true
priority: 2
---

# 项目结构规范

## Maven多模块结构

### 父模块 (order-monitor)
- **坐标**: `com.iqiyi.vip.zeus:order-monitor`
- **版本**: `1.0.0-SNAPSHOT`
- **打包方式**: `pom`
- **作用**: 统一管理子模块版本和依赖

### 子模块说明

#### 1. order-export (主业务模块)
- **坐标**: `com.iqiyi.vip.zeus:order-export`
- **打包方式**: `jar`
- **作用**: 订单导出和天网稽核系统主应用
- **技术栈**: Spring Boot 2.7.6, Thymeleaf, Vue 3
- **端口**: 8080

#### 2. zeus-core (核心服务模块)
- **坐标**: `com.iqiyi.vip.zeus:zeus-core`
- **打包方式**: `jar`
- **作用**: 核心业务逻辑、数据访问层、服务层
- **技术栈**: MyBatis, Spring Data JPA

#### 3. zeus-common (公共工具模块)
- **坐标**: `com.iqiyi.vip.zeus:zeus-common`
- **打包方式**: `jar`
- **作用**: 公共工具类、常量定义、通用组件

#### 4. zeus-eagle-client-sdk (Eagle客户端SDK)
- **坐标**: `com.iqiyi.vip.zeus:zeus-eagle-client-sdk`
- **打包方式**: `jar`
- **作用**: Eagle监控系统客户端SDK

#### 5. zeus-worker (后台任务模块)
- **坐标**: `com.iqiyi.vip.zeus:zeus-worker`
- **打包方式**: `jar`
- **作用**: 定时任务、异步处理、后台作业

## 技术栈规范

### 后端技术栈
- **框架**: Spring Boot 2.7.6
- **数据库**: MySQL 8.0, TiDB, ClickHouse, StarRocks, Hive
- **ORM**: MyBatis 3.5.9
- **模板引擎**: Thymeleaf 3.0.15
- **监控**: Prometheus, Eagle
- **构建工具**: Maven 3.6+
- **Java版本**: JDK 8+

### 前端技术栈
- **框架**: Vue 3 (Composition API)
- **UI库**: Ant Design Vue
- **图标**: Font Awesome
- **构建**: 内嵌在Spring Boot中

## 包结构规范

### order-export模块包结构
```
com.iqiyi.vip.zeus.orderexport
├── client/            # 外部服务客户端
├── component/         # 组件层
├── config/            # 配置类
├── constant/          # 常量定义
├── consumer/          # 消息消费者
├── controller/        # 控制器层
├── entity/            # 业务实体类
├── enums/             # 枚举类
├── exception/         # 异常处理
├── handler/           # 处理器
├── interceptor/       # 拦截器
├── job/               # 定时任务
├── model/             # 数据模型
├── param/             # 请求参数
├── response/          # 响应对象
├── security/          # 安全相关
└── validator/         # 验证器
```

### zeus-core模块包结构
```
com.iqiyi.vip.zeus.core
├── component/         # 核心组件
├── config/            # 配置类
├── constants/         # 常量定义
├── context/           # 上下文
├── enums/             # 枚举类
├── exception/         # 异常处理
├── manager/           # 管理器
├── mapper/            # MyBatis映射器
│   ├── guard/         # 稽核相关映射器
│   ├── mysql/         # MySQL映射器
│   ├── pilot/         # Pilot组件映射器
│   └── zeus/          # Zeus系统映射器
├── model/             # 数据模型
├── mybatis/           # MyBatis配置
├── po/                # 数据库实体类(Persistent Object)
│   ├── guard/         # 稽核相关实体
│   └── zeus/          # Zeus系统实体
├── req/               # 请求参数
├── service/           # 服务层
└── utils/             # 工具类
```

### zeus-common模块包结构
```
com.iqiyi.vip.zeus
├── Main.java          # 主类
└── utils/             # 公共工具类
```

### zeus-eagle-client-sdk模块包结构
```
com.iqiyi.vip.zeus.eagleclient
├── api/               # API接口
├── constants/         # 常量定义
├── exception/         # 异常处理
├── handler/           # 处理器
├── model/             # 数据模型
│   ├── devops/        # DevOps模型
│   ├── eagle/         # Eagle模型
│   └── prometheus/    # Prometheus模型
├── request/           # 请求对象
├── response/          # 响应对象
├── utils/             # 工具类
├── DevOpsClient.java
├── EagleClient.java
└── SmartAlertClient.java
```

### zeus-worker模块包结构
```
com.iqiyi.vip.zeus.worker
├── consumer/          # 消息消费者
├── job/               # 定时任务
└── ZeusWorkerApplication.java
```

### 数据库实体类规范
- **目录位置**: `com.iqiyi.vip.zeus.core.po.guard.*`
- **命名规范**: 类名必须以 `PO` 结尾，如 `BusinessPO`、`GuardItemPO`
- **注解使用**: 使用 Lombok 的 `@Data`、`@NoArgsConstructor`、`@AllArgsConstructor`、`@Builder`注解
- **示例**:
  ```java
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public class BusinessPO {
      private Integer id;
      private String name;
      // ...
  }
  ```

### 资源文件结构
```
src/main/resources/
├── application.yml           # 主配置文件
├── application-dev.yml       # 开发环境配置
├── application-prod.yml      # 生产环境配置
├── templates/                # Thymeleaf模板
│   └── index.html           # 天网系统主页面
├── static/                  # 静态资源
│   ├── css/
│   │   └── main.css         # 主样式文件
│   └── js/
│       └── main.js          # Vue 3前端逻辑
└── mapper/                  # MyBatis映射文件
    └── mysql/
```

## 天网系统特殊说明

### 前端集成方式
- **模板引擎**: Thymeleaf + Vue 3
- **数据传递**: 通过`window.serverData`从服务器传递数据到前端
- **路由控制**: 后端Controller控制页面路由，前端Vue控制页面内容切换

### 访问路径设计
- **主页**: `/tianwang` -> `index.html`
- **数据源管理**: `/guard/datasources` -> `index.html` (tab=datasource)
- **稽核项管理**: `/guard/items` -> `index.html` (tab=guard-items)
- **我的订阅**: `/guard/subscription` -> `index.html` (tab=subscription)
- **稽核报告**: `/guard/reports` -> `index.html` (tab=reports)
- **操作日志**: `/guard/logs` -> `index.html` (tab=logs)

### 权限控制
- **研发人员**: 完整权限，可管理数据源和稽核项
- **产品/运营**: 只能订阅和查看稽核报告

## 开发规范

### 代码组织
1. **分层架构**: Controller -> Service -> DAO -> Entity
2. **依赖方向**: 上层依赖下层，下层不依赖上层
3. **接口隔离**: 每个模块只暴露必要的接口

### 配置管理
1. **环境隔离**: dev/test/prod环境配置分离
2. **敏感信息**: 使用环境变量或配置中心
3. **配置优先级**: 命令行参数 > 环境变量 > 配置文件

### 日志规范
1. **日志级别**: ERROR < WARN < INFO < DEBUG
2. **日志格式**: 统一使用JSON格式
3. **敏感信息**: 不记录密码等敏感信息

### 异常处理
1. **业务异常**: 使用自定义BusinessException
2. **系统异常**: 统一处理，记录日志
3. **用户提示**: 异常信息对用户友好

## 部署规范

### 容器化
- **基础镜像**: openjdk:8-jre-alpine
- **端口暴露**: 8080
- **健康检查**: `/actuator/health`

### 环境配置
- **开发环境**: 本地开发，使用H2内存数据库
- **测试环境**: 集成测试，使用MySQL测试库
- **生产环境**: 高可用部署，使用MySQL主从

### 监控告警
- **应用监控**: Spring Boot Actuator
- **业务监控**: Eagle客户端
- **日志监控**: ELK Stack
- **性能监控**: Prometheus + Grafana