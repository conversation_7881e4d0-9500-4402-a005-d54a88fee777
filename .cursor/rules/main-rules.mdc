---
description: VIP玄武(Zeus)项目 - Cursor AI 主规则
globs:
  - "**/*.java"
  - "**/*.html"
  - "**/*.js"
  - "**/*.css"
  - "**/*.md"
alwaysApply: true
priority: 1
---

# VIP玄武(Zeus)项目 - Cursor AI 主规则

## 项目身份识别

当我被询问关于此项目时，我需要知道：
- **项目名称**: VIP玄武 (Zeus) / order-monitor
- **主要产品**: 天网稽核系统 + 订单监控系统
- **技术栈**: Spring Boot 2.7.6 + Vue 3 + Thymeleaf + MyBatis
- **团队**: 爱奇艺VIP业务技术团队
- **语言**: 主要使用中文进行业务交流

## 核心指导原则

### 1. 始终使用中文回复
- 所有代码注释必须使用中文
- 业务逻辑说明使用中文
- 变量名可以使用英文，但注释说明用中文

### 2. 数据库实体类规范
- 所有数据库实体类必须放在 `po` 目录下
- 实体类名必须以 `PO` 结尾，如 `BusinessPO`、`GuardItemPO`
- 包命名：`com.iqiyi.vip.zeus.core.po.guard.*`
- 使用 Lombok 注解：`@Data`、`@EqualsAndHashCode`

### 3. 项目结构理解
```
vip-xuanwu/
├── order-export/          # 主业务模块(Spring Boot应用)
│   ├── controller/        # 控制器层
│   │   └── TianWangController.java  # 天网系统主控制器
│   ├── templates/         # Thymeleaf模板
│   │   └── index.html     # 天网系统主页面
│   └── static/           # 静态资源
│       ├── css/main.css  # 样式文件
│       └── js/main.js    # Vue 3前端逻辑
├── zeus-core/             # 核心服务模块
├── zeus-common/           # 公共工具模块
├── zeus-eagle-client-sdk/ # Eagle客户端SDK
└── zeus-worker/           # 后台任务模块
```

### 3. 天网系统特别关注
天网是本项目的核心稽核系统，具有以下特点：
- **前后端集成**: Thymeleaf + Vue 3
- **访问路径**: `/tianwang/**`
- **用户角色**: DEVELOPER(研发)、PRODUCT(产品)、OPERATION(运营)
- **核心功能**: 数据源管理、稽核项管理、订阅管理、报告查看

## 代码生成规则

### Java代码规则
1. **包命名**: 始终使用`com.iqiyi.vip.zeus.orderexport.*`
2. **类命名**: 使用PascalCase，如`TianWangController`
3. **方法命名**: 使用camelCase，如`getDatasources`
4. **注释**: 必须使用中文，说明业务含义

```java
/**
 * 天网系统主控制器
 * 处理稽核系统的页面路由和数据传递
 */
@Controller
@RequestMapping("/tianwang")
public class TianWangController {
    
    /**
     * 获取稽核系统主页
     * @param model 模板数据模型
     * @param tab 激活的标签页，默认为dashboard
     * @return 返回index模板
     */
    @GetMapping("")
    public String index(Model model, @RequestParam(defaultValue = "dashboard") String tab) {
        // 实现逻辑
    }
}
```

### 前端代码规则
1. **Vue 3**: 使用Composition API
2. **数据获取**: 优先从`window.serverData`获取服务器数据
3. **组件命名**: 使用kebab-case
4. **事件处理**: 使用`handle*`或`on*`前缀

```javascript
// 从服务器获取数据，提供默认值
const userName = ref(window.serverData?.userName || '张三');
const userRole = ref(window.serverData?.userRole || 'DEVELOPER');

// 事件处理方法
const handleTabChange = (tab) => {
    activeTab.value = tab;
};
```

### Thymeleaf模板规则
1. **命名空间**: 必须包含`xmlns:th="http://www.thymeleaf.org"`
2. **数据绑定**: 使用`th:*`属性
3. **静态资源**: 使用`@{}`语法
4. **服务器数据**: 通过`th:inline="javascript"`传递

```html
<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <title th:text="${pageTitle != null ? pageTitle : '天网'}">天网</title>
    <link rel="stylesheet" th:href="@{/css/main.css}">
</head>
<body>
    <script th:inline="javascript">
        window.serverData = {
            userName: /*[[${user?.name}]]*/ '张三',
            userRole: /*[[${user?.role}]]*/ 'DEVELOPER'
        };
    </script>
</body>
</html>
```

## 业务逻辑规则

### 稽核系统业务
1. **数据源类型**: MySQL, TiDB, ClickHouse, StarRocks, Hive, Prometheus
2. **稽核维度**: 准确性、一致性、时效性、合理性、完整性
3. **用户权限**: 研发人员拥有管理权限，其他用户只能订阅查看

### 数据展示规则
1. **稽核报告**: 表格形式，按业务分组
2. **列顺序**: 序号 → 稽核项 → 周同比 → 日环比 → 各日期数据
3. **趋势颜色**: 增加显示红色，减少显示绿色
4. **类型徽章**: 使用颜色徽章显示稽核类型

## API设计规则

### Controller返回值规范
1. **统一返回值**: 所有Controller方法必须返回`BaseResponse<T>`类型
2. **成功响应**: 使用`BaseResponse.createSuccess(data)`或`BaseResponse.createSuccessList(data)`
3. **失败响应**: 使用`BaseResponse.createParamError(message)`或`BaseResponse.createSystemError(message)`
4. **资源不存在**: 使用`BaseResponse.create(CodeEnum.NOT_FOUND)`
5. **禁止使用**: 禁止使用`ResponseEntity<Map<String, Object>>`作为返回值

### RESTful API
```java
@RestController
@RequestMapping("/api/tianwang")
public class TianWangApiController {
    
    /**
     * 获取数据源列表
     */
    @GetMapping("/datasources")
    public BaseResponse<List<Datasource>> getDatasources() {
        try {
            List<Datasource> datasources = datasourceService.getAll();
            return BaseResponse.createSuccessList(datasources);
        } catch (Exception e) {
            return BaseResponse.createSystemError("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建稽核项
     */
    @PostMapping("/guard-items")
    public BaseResponse<GuardItem> createGuardItem(@RequestBody @Valid GuardItemRequest request) {
        try {
            GuardItem item = guardItemService.create(request);
            return BaseResponse.createSuccess(item);
        } catch (IllegalArgumentException e) {
            return BaseResponse.createParamError(e.getMessage());
        } catch (Exception e) {
            return BaseResponse.createSystemError("创建失败: " + e.getMessage());
        }
    }
}
```

### 响应格式统一
**成功响应：**
```json
{
  "code": "A00000",
  "msg": "处理成功",
  "data": { ... }
}
```

**失败响应：**
```json
{
  "code": "Q00301",
  "msg": "参数错误",
  "data": null
}
```

**状态码说明：**
- `A00000`: 处理成功
- `Q00301`: 参数错误
- `Q00332`: 系统错误
- `Q00404`: 资源不存在
- `Q00409`: 资源已存在

## 特殊注意事项

### 1. 系统命名
- 项目叫"业务稽核系统"，网页上的名称为"天网"
- URL路径使用`/guard`
- 模板文件名为`index.html`

### 2. 角色权限
- `DEVELOPER`: 研发工程师，完整权限
- `PRODUCT`: 产品经理，个人功能权限
- `OPERATION`: 运营人员，个人功能权限

### 3. 数据源集成
- 使用公司Pilot组件查询大数据源
- 使用DMS API查询MySQL和TiDB
- 使用Prometheus API查询监控数据

### 4. 前端状态管理
- 使用Vue 3 Composition API
- 状态通过`activeTab`控制页面显示
- 服务器数据通过`window.serverData`传递

当开发此项目时，我会：
1. 优先考虑天网系统的业务需求
2. 遵循现有的技术架构和编码规范
3. 保持前后端集成的一致性
4. 注重用户权限和安全性
5. 确保代码可读性和可维护性