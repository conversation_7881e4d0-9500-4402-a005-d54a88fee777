package com.iqiyi.vip.zeus.worker;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;

/**
 * @author: guojing
 * @date: 2023/12/7 20:50
 */
@ComponentScan(basePackages = {"com.iqiyi.vip.order.dal","com.iqiyi.vip.zeus"})
@ImportResource({"classpath:applicationContext-orderDal.xml"})
@EnableScheduling
@EnableCaching
@SpringBootApplication
@EnableDiscoveryClient
@EnableCloudConfig
@EnableAspectJAutoProxy(exposeProxy = true)
public class ZeusWorkerApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZeusWorkerApplication.class, args);
    }
}