spring:
  application:
    name: order-export
  cache:
    type: caffeine
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=60s
  cloud:
    loadbalancer:
      retry:
        enabled: true
      ribbon:
        enabled: true
  main:
    allow-bean-definition-overriding: true

server:
  port: 8080

management:
  security:
    enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
      jmx:
        enabled: true
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: health,info,prometheus
      base-path: /actuator
  health:
    db:
      enabled: false
    redis:
      enabled: false
    defaults:
      enabled: false
  info:
    git:
      mode: full


# eureka config
eureka:
  instance:
    hostname: ${HOST}
    ip-address: ${HOST}
    non-secure-port: ${PORT_8080}
    instance-id: ${HOST}:${spring.application.name}:${eureka.instance.non-secure-port}
    prefer-ip-address: false
    initial-status: up
    metadata-map:
      zone: zone-bj
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 5
  client:
    healthcheck:
      enabled: true
    register-with-eureka: true
    region: region-bj
    availability-zones:
      region-bj: zone-bj
    service-url:
      zone-bj: http://bj.eureka.vip.qiyi.domain:8080/eureka

ribbon:
  eureka:
    enabled: true
  restclient:
    enabled: true
  ConnectTimeout: 3000
  ReadTimeout: 500

#配置中心
cloud:
  config:
    app:
      name: vip-xuanwu
      env: pro
      region: default

mysql:
  order:
    binlog:
      rmq:
        namesrvaddr: dc-resource-worker-online176-hbaz1.qiyi.virtual:9876;dc-resource-worker-online334-hbaz2.qiyi.virtual:9876
        consumer:
          token: CT-2f2b46c1-4690-44b0-8280-25e01b1f716e

trade:
  order:
    rmq:
      namesrvaddr: dc-resource-worker-online176-hbaz1.qiyi.virtual:9876;dc-resource-worker-online334-hbaz2.qiyi.virtual:9876
      paid:
        consumer:
          token: CT-556e0285-66e0-4627-9809-f656fda4fdd8
      finished:
        consumer:
          token: CT-df5f8aae-3e23-4a4d-b196-3643314e11bc
      monitor:
        producer:
          token: PT-2c7696a6-8bc3-4f04-980b-143fd4e5e276
        consumer:
          token: CT-b54372e5-3e51-4c2c-a43f-e5e173d7ca92

#自动续费binlog RMQ集群配置
autorenew:
  binlog:
    rmq:
      namesrvaddr: hb-az-2.viptrade-autorenew-asynctask.online001.rocketmq.qiyi.middle:9876;hb-az-4.viptrade-autorenew-asynctask.online002.rocketmq.qiyi.middle:9876
      consumer:
        token: ***********-5788-47f3-8dbe-366a2bfc88aa
  async:
    task:
      binlog:
        rmq:
          consumer:
            token: CT-7fd3e0bf-3a1b-490b-8390-ec3bbbd59754
      rmq:
        consumer:
          token: CT-5a36004c-473d-474e-b250-695fcb563a17


datasource:
  order:
    url: ******************************************************************************************************************************************************************************************************
    username: huiyuan_order
    password: tewptqqW
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  present:
    url: ******************************************************************************************************************************************************************************************************
    username: vip_present
    password: B@7fcd296b631
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  autorenew:
    url: **********************************************************************************************************************************************************************************************
    username: boss
    password: "Rdi!H)ui4*AZ"
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  tidb:
    order:
      url: jdbc:mysql://************:4000/huiyuan_order_user?useUnicode=true&characterEncoding=UTF-8&characterSetResults=UTF-8&autoReconnect=true&useAffectedRows=true&useServerPrepStmts=false&useLocalSessionState=true&connectTimeout=30000&socketTimeout=10000
      username: huiyuan_order_user
      password: ZYwGZIpfptBlmdgu
      driverClassName: com.mysql.jdbc.Driver
      hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
        minimumIdle: 10
        maximumPoolSize: 10
        maxLifetime: 1800000
        idleTimeout: 600000
        connectionTimeout: 30000
  promotion:
    url: *********************************************************************************************************************************************************************************************************************************************
    username: viptrade
    password: Rdi!H)ui4*AZ
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  store:
    url: ********************************************************************************************************************************************************************************************************
    username: viptrade_store
    password: dS!kXcCW
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  payresult:
    url: *****************************************************************************************************************************************************************************************************************
    username: pay_result
    password: Rdi!H)ui4*AZ
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  coupon:
    url: ****************************************************************************************************************************************************************************************************************************************************************
    username: viptrade_coupon
    password: TqFbzEMo
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  payinfo:
    url: *************************************************************************************************************************************************************************************************************
    username: pay_info
    password: Hg!avBm2Uu
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000

# vip-job配置
vip:
  job:
    access:
      way: qke
    accessToken: 958b9bf186014d41aa43bd4ba4aa993b
    admin:
      addresses: http://qiyi-job-admin.qiyi.domain
    executor:
      appname: vip-xuanwu
      logpath: /data/log/vip-xuanwu/
      port: 9083
      switch: on
    qke:
      app:
        id: 12388

# 买赠present_order表个数
present:
  order:
    table:
      count: 100

cache:
  max:
    size: 5000000
  expire:
    hours: 24

user:
  statics:
    cache:
      max:
        size: 5000000
      expire:
        minutes: 60

pay:
  info:
    qsm:
      url: http://viptrade-pay-info-api.qsm.qiyi.middle/api/payChannel/
    cloud:
      url: http://viptrade-pay-info/api/payChannel/
    sign:
      key: L7RJxBpRSi

#邮件配置
mail:
  user:
    name: vipmessage
    address: <EMAIL>
    token: 8u36q9d63g96hqlr

favor:
  record:
    table:
      number: 256

# Order Sharding Configuration
order:
  sharding:
    tableSize: 64
    database:
      urls: **************************************************,**************************************************,**************************************************,**************************************************,**************************************************,**************************************************,**************************************************,**************************************************,**************************************************,***************************************************,***************************************************,***************************************************,***************************************************,***************************************************,***************************************************,***************************************************
      username: vip_order_pro
      password: agujmw59!chinagooD8b%

commodity:
  config:
    app:
      caller: vip-operation-api
      signKey: c931acc3d4b440c79630c14567af310e
      url: http://vcc.vip.qiyi.domain/vip-commodity
      profile: prod

iqiyi:
  cloud:
    config:
      bootstrap:
        enabled: true

auth:
  system: qiyue
  key: 98c04aae9b689ae2
  url: http://admin.vip.qiyi.domain/ops-auth

alertUrl: http://admin.vip.qiyi.domain/watcher-api/alert/sendAlert

jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 300000