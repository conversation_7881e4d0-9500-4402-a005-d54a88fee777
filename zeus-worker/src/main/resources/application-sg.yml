spring:
  application:
    name: intl-order-export
  cache:
    type: caffeine
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=60s
  cloud:
    loadbalancer:
      retry:
        enabled: true
      ribbon:
        enabled: true
  main:
    allow-bean-definition-overriding: true

server:
  port: 8080

management:
  security:
    enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
      jmx:
        enabled: true
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: health,info,prometheus
      base-path: /actuator
  health:
    db:
      enabled: false
    redis:
      enabled: false
    defaults:
      enabled: false
  info:
    git:
      mode: full

# eureka config
eureka:
  instance:
    port: ${PORT_8080}
    hostname: ${HOST}
    ip-address: ${HOST}
    non-secure-port: ${PORT_8080}
    instance-id: ${HOST}:${spring.application.name}:${eureka.instance.non-secure-port}
    prefer-ip-address: false
    initial-status: up
    metadata-map.zone: zone-aws
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 5
  client:
    healthcheck:
      enabled: true
    register-with-eureka: true
    region: region-sea
    availability-zones:
      region-sea: zone-aws
    service-url:
      zone-aws: http://aws.eureka.vip.qiyi.domain:8080/eureka

  ribbon:
    eureka:
      enabled: true
    restclient:
      enabled: true
    ConnectTimeout: 3000
    ReadTimeout: 500
#配置中心
cloud:
  config:
    app:
      name: intl-vip-xuanwu
      env: pro
      region: intl

mysql:
  order:
    binlog:
      rmq:
        namesrvaddr: aws-apse1-az1.intl-vip-trade-order-msg.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-vip-trade-order-msg.online002.rocketmq.qiyi.middle:9876
        consumer:
          token: CT-716810bd-3524-4470-82d2-cb8c59b508c5

trade:
  order:
    rmq:
      namesrvaddr: aws-apse1-az1.intl-vip-trade-order-msg.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-vip-trade-order-msg.online002.rocketmq.qiyi.middle:9876
      paid:
        consumer:
          token: CT-861df39e-08f5-476e-b7d8-f3aaf1422ab6
      finished:
        consumer:
          token: CT-726149db-a921-4495-a363-beb5de085be4
      monitor:
        producer:
          token: PT-42e26969-afbc-4720-8416-7004a7c633d9
        consumer:
          token: CT-16faf587-c498-48dd-aead-bd560a4ca6cb

#自动续费binlog RMQ集群配置
autorenew:
  binlog:
    rmq:
      namesrvaddr: aws-apse1-az1.intl-autorenew.online001.rocketmq.qiyi.middle:9876;aws-apse1-az1.intl-autorenew.online002.rocketmq.qiyi.middle:9876
      consumer:
        token: CT-dd9f59e2-7dd8-4299-bcf5-e1f0d77ccdc3
  async:
    task:
      binlog:
        rmq:
          consumer:
            token: CT-f7d3b0cf-f2dc-414d-85a9-545e28a44f27
      rmq:
        consumer:
          token: CT-9a62bcdd-8764-4a40-9de3-522c56f94a03


datasource:
  order:
    url: ************************************************************************************************************************************************************************************************************
    username: huiyuan_order
    password: FuVTxZo5
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  present:
    url: ******************************************************************************************************************************************************************************************************
    username: vip_persent
    password: "@9BXv72NKj"
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  autorenew:
    url: ********************************************************************************************************************************************************************************************************
    username: autorenew
    password: "Rdi!H)ui4*AZ"
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  tidb:
    order:
      url: jdbc:mysql://**************:4000/huiyuan_order_user_sg?useUnicode=true&characterEncoding=UTF-8&characterSetResults=UTF-8&autoReconnect=true&useAffectedRows=true&useServerPrepStmts=false&useLocalSessionState=true&connectTimeout=30000&socketTimeout=10000
      username: viporderuser
      password: iqVf2Cw1gyzo
      driverClassName: com.mysql.jdbc.Driver
      hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
        minimumIdle: 10
        maximumPoolSize: 10
        maxLifetime: 1800000
        idleTimeout: 600000
        connectionTimeout: 30000

# vip-job配置
vip:
  job:
    access:
      way: qae
    accessToken: 89f19c7e99aa496d92b18e4dcc57fc32
    admin:
      addresses: http://intl-vip-job.online.qiyi.qae
    executor:
      appname: intl-order-xuanwu-monitor
      logpath: /data/log/vip-xuanwu/
      port: 9083
      switch: on
    qae:
      api:
        access:
          key: ef2eb201345dd47dec9e71ded9991564dc8fa4cf
        url: http://qae.qiyi.virtual/api/v3/apps
      app:
        id: linpeihui.intl-vip-xuanwu.sgp-ali-online01
      switch: on

# 买赠present_order表个数
present:
  order:
    table:
      count: 100

cache:
  max:
    size: 5000000
  expire:
    hours: 6

user:
  statics:
    cache:
      max:
        size: 5000000
      expire:
        minutes: 60

pay:
  info:
    qsm:
      url: http://viptrade-pay-info-api.qsm.qiyi.middle/api/payChannel/
    cloud:
      url: http://viptrade-pay-info/api/payChannel/
    sign:
      key: L7RJxBpRSi


#邮件配置
mail:
  user:
    name: vipmessage
    address: <EMAIL>
    token: 8u36q9d63g96hqlr

# Order Sharding Configuration
order:
  sharding:
    tableSize: 256
    database:
      urls: *******************************************************************************,*******************************************************************************,*******************************************************************************,*******************************************************************************
      username: viporder
      password: u3BMe0Xm

commodity:
  config:
    app:
      caller: viptrade-refundservice
      signKey: cc0c6580d06d46979fc9499f1cce0bce
      url: http://intl-vcc.vip.qiyi.domain/vip-commodity
      profile: intl

alertUrl: http://admin.vip.qiyi.domain/watcher-api/alert/sendAlert

jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 300000