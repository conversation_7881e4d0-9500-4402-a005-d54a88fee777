<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="INFO_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/zeus-worker/info.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/zeus-worker/info.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>168</maxHistory>
            <!--保留7天的历史记录，但最多8GB-->
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_INFO_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="INFO_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/zeus-worker/error.log</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/zeus-worker/error.log.%d{yyyy-MM-dd_HH}</fileNamePattern>
            <!--日志文件保留天数，fileNamePattern按小时归档 -->
            <!--因为fileNamePattern中时间正则最小单位是H（小时）所以maxHistory的单元是小时-->
            <maxHistory>168</maxHistory>
            <!--保留7天的历史记录，但最多8GB-->
            <totalSizeCap>8GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <includeCallerData>true</includeCallerData>
        <neverBlock>true</neverBlock>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="ERROR_DAILY_ROLLING_FILE"/>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%tid] [%t] [%C{1}:%M:%L] %m%n</pattern>
            </layout>
        </encoder>
    </appender>

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_INFO_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

    <springProfile name="prod,sg">
        <root level="INFO">
<!--            <appender-ref ref="CONSOLE"/>-->
            <appender-ref ref="ASYNC_INFO_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>

</configuration>