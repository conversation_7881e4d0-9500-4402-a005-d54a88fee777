spring:
  application:
    name: order-export
  cache:
    type: caffeine
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=60s
  cloud:
    loadbalancer:
      retry:
        enabled: true
      ribbon:
        enabled: true
  main:
    allow-bean-definition-overriding: true
server:
  port: 8080

# eureka config
eureka:
  instance:
    hostname: ${spring.cloud.client.ip-address}
    non-secure-port: 8080
    lease-renewal-interval-in-seconds: 3
    lease-expiration-duration-in-seconds: 5
  client:
    service-url:
      defaultZone: http://test-eureka.vip.qiyi.domain:8080/eureka/
    healthcheck:
      enabled: true
hystrix:
  metrics:
    enabled: false
management:
  security:
    enabled: false
  metrics:
    export:
      prometheus:
        enabled: true
      jmx:
        enabled: true
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: health,info,prometheus
      base-path: /actuator
  health:
    db:
      enabled: false
    redis:
      enabled: false
    defaults:
      enabled: false
  info:
    git:
      mode: full


  ribbon:
    eureka:
      enabled: true
    restclient:
      enabled: true
    ConnectTimeout: 3000
    ReadTimeout: 500

#配置中心
cloud:
  config:
    app:
      name: vip-xuanwu
      env: dev
      region: default

mysql:
  order:
    binlog:
      rmq:
        namesrvaddr: dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
        consumer:
          token: CT-474e6ada-8945-4451-a2bf-efd3a9d1e7d9

trade:
  order:
    rmq:
      namesrvaddr: dc-resource-fb597f31-10.qiyi.virtual:9876;dc-resource-fb597f31-8.qiyi.virtual:9876
      paid:
        consumer:
          token: CT-c368f398-2422-44fd-9488-88dc20d05432
      finished:
        consumer:
          token: CT-e167c95a-831c-48ee-bf48-56736bf39d5f
      monitor:
        producer:
          token: PT-abfe0a7a-b821-4827-8ca9-366c9b598f3b
        consumer:
          token: CT-2fe7353d-1504-43ab-b2dd-94a5e26a4570

#自动续费binlog RMQ集群配置
autorenew:
  binlog:
    rmq:
      namesrvaddr: vip-autorenew-rocketmq-dev009-bdwg.qiyi.virtual:9876;vip-autorenew-rocketmq-dev011-bdwg.qiyi.virtual:9876
      consumer:
        token: CT-2d7fcfea-57cf-43de-bf1c-193a2b8b93ae
  async:
    task:
      binlog:
        rmq:
          consumer:
            token: CT-fbccfaf1-ef9d-4876-b792-2b4c8ce283a1
      rmq:
        consumer:
          token: CT-4be5e295-5279-4305-a43a-59af2c67fcad


datasource:
  order:
    url: **************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  present:
    url: ************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  autorenew:
    url: **********************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  tidb:
    order:
      url: ********************************************************************************************************************************************************************************************************************************************************************************
      username: viptrade_order_user
      password: +7D/8NslxTTO
      driverClassName: com.mysql.jdbc.Driver
      hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
        minimumIdle: 10
        maximumPoolSize: 10
        maxLifetime: 1800000
        idleTimeout: 600000
        connectionTimeout: 30000
  promotion:
    url: *******************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  store:
    url: ***************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  payresult:
    url: ********************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  coupon:
    url: *******************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  zeus:
    url: *********************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000
  payinfo:
    url: ******************************************************************************************************************************************************************************************************
    username: vip_test
    password: rg_z_6UF)w=Y
    driverClassName: com.mysql.jdbc.Driver
    hikari: # https://github.com/brettwooldridge/HikariCP (uses milliseconds for all time values)
      minimumIdle: 10
      maximumPoolSize: 10
      maxLifetime: 1800000
      idleTimeout: 600000
      connectionTimeout: 30000

# vip-job配置
vip:
  job:
    access:
      way: qke
    accessToken: 0909c01867f14703acef553f970379c5
    admin:
      addresses: http://vip-job-admin.test.qiyi.qae
    executor:
      appname: vip-xuanwu
      logpath: /data/log/vip-xuanwu/
      port: 9083
      switch: on
    qke:
      app:
        id: 12388

# 买赠present_order表个数
present:
  order:
    table:
      count: 20

cache:
  max:
    size: 100000
  expire:
    hours: 6

user:
  statics:
    cache:
      max:
        size: 5000000
      expire:
        minutes: 1

pay:
  info:
    qsm:
      url: http://viptrade-pay-info-api.qsm.qiyi.middle/api/payChannel/
    cloud:
      url: http://viptrade-pay-info/api/payChannel/
    sign:
      key: 123456

#邮件配置
mail:
  user:
    name: vipmessage
    address: <EMAIL>
    token: 8u36q9d63g96hqlr

favor:
  record:
    table:
      number: 3

# Order Sharding Configuration
order:
  sharding:
    tableSize: 2
    database:
      urls: ******************************************************,******************************************************
      username: vip_order_test
      password: agh3!schingood5TR$

commodity:
  config:
    app:
      caller: vip-operation-api-test
      signKey: 123456
      url: http://vcc-test.vip.qiyi.domain/vip-commodity
      profile: test

iqiyi:
  cloud:
    config:
      bootstrap:
        enabled: true

auth:
  system: qiyue
  key: 98c04aae9b689ae2
  url: http://admin-test.vip.qiyi.domain/ops-auth

alertUrl: http://admin-test.vip.qiyi.domain/watcher-api/alert/sendAlert

jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 300000
